{"name": "print-terminal", "version": "1.0.0", "description": "现代化云打印终端系统 - 基于Electron + Vue 3 + Vuetify", "main": "dist/main/index.js", "author": "Print Terminal Team", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "cross-env NODE_ENV=development tsc -p src/main/tsconfig.json && cross-env NODE_ENV=development electron dist/main/index.js --inspect=5858", "dev:renderer": "cross-env NODE_ENV=development vite", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p src/main/tsconfig.json", "build:renderer": "vite build", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "rebuild": "electron-rebuild -f -w sqlite3", "postinstall": "electron-rebuild", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@cloudbase/node-sdk": "^2.0.0", "@mdi/font": "^7.3.67", "axios": "^1.6.0", "dayjs": "^1.11.0", "electron-store": "^8.1.0", "lodash-es": "^4.17.21", "pinia": "^2.1.0", "sequelize": "^6.35.0", "sqlite3": "^5.1.6", "vue": "^3.4.0", "vue-router": "^4.2.0", "vuetify": "^3.4.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.0", "concurrently": "^8.2.0", "cross-env": "^10.0.0", "electron": "^28.0.0", "electron-builder": "^24.8.0", "electron-rebuild": "^3.2.9", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.19.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "build": {"appId": "com.printTerminal.app", "productName": "Print Terminal", "directories": {"output": "release"}, "files": ["dist/**/*", "resources/**/*", "node_modules/**/*"], "extraResources": [{"from": "resources", "to": "resources"}], "win": {"target": "nsis", "icon": "resources/icon.ico"}, "mac": {"target": "dmg", "icon": "resources/icon.icns"}, "linux": {"target": "AppImage", "icon": "resources/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}