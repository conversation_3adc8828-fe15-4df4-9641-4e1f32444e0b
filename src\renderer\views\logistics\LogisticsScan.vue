<template>
  <div class="logistics-scan">
    <div class="page-header">
      <h1 class="page-title">扫描发货</h1>
      <p class="page-subtitle">扫描条码进行发货操作</p>
    </div>

    <v-card class="content-card" elevation="0">
      <v-card-title>扫描发货</v-card-title>
      <v-card-text>
        <div class="text-center py-8">
          <v-icon icon="mdi-qrcode-scan" size="64" color="grey-lighten-1" />
          <div class="text-h6 mt-4 mb-2">扫描发货功能</div>
          <div class="text-body-2 text-grey">将在任务4中实现</div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
// TODO: 在任务4中实现扫描发货功能
</script>
