// 混合样式文件

// 响应式断点混合
@mixin breakpoint($size) {
  @if $size == xs {
    @media (max-width: 599px) { @content; }
  } @else if $size == sm {
    @media (min-width: 600px) and (max-width: 959px) { @content; }
  } @else if $size == md {
    @media (min-width: 960px) and (max-width: 1279px) { @content; }
  } @else if $size == lg {
    @media (min-width: 1280px) and (max-width: 1919px) { @content; }
  } @else if $size == xl {
    @media (min-width: 1920px) { @content; }
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 居中对齐
@mixin center($direction: both) {
  display: flex;
  
  @if $direction == both {
    align-items: center;
    justify-content: center;
  } @else if $direction == horizontal {
    justify-content: center;
  } @else if $direction == vertical {
    align-items: center;
  }
}

// 卡片阴影
@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: var(--shadow-card);
  } @else if $level == 2 {
    box-shadow: var(--shadow-card-hover);
  } @else if $level == 3 {
    box-shadow: var(--shadow-lg);
  }
}

// 按钮样式
@mixin button-style($variant: primary) {
  border-radius: var(--border-radius-button);
  font-weight: var(--font-weight-medium);
  text-transform: none;
  transition: all var(--duration-normal) var(--ease-out);
  
  @if $variant == primary {
    background: var(--primary-500);
    color: white;
    
    &:hover {
      background: var(--primary-600);
      box-shadow: var(--shadow-button-hover);
    }
  } @else if $variant == secondary {
    background: var(--secondary-500);
    color: white;
    
    &:hover {
      background: var(--secondary-600);
      box-shadow: var(--shadow-button-hover);
    }
  }
}

// 输入框样式
@mixin input-style {
  border-radius: var(--border-radius-input);
  border: 1px solid var(--border-base);
  transition: all var(--duration-normal) var(--ease-out);
  
  &:focus {
    border-color: var(--primary-500);
    box-shadow: var(--shadow-outline);
  }
  
  &:hover {
    border-color: var(--border-dark);
  }
}

// 滚动条样式
@mixin scrollbar($width: 6px, $track-color: transparent, $thumb-color: rgba(0, 0, 0, 0.2)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: calc($width / 2);
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// 动画
@mixin fade-in($duration: var(--duration-normal)) {
  animation: fadeIn $duration var(--ease-out);
}

@mixin slide-in($direction: up, $distance: 20px, $duration: var(--duration-normal)) {
  @if $direction == up {
    animation: slideInUp $duration var(--ease-out);
  } @else if $direction == down {
    animation: slideInDown $duration var(--ease-out);
  } @else if $direction == left {
    animation: slideInLeft $duration var(--ease-out);
  } @else if $direction == right {
    animation: slideInRight $duration var(--ease-out);
  }
}

// 悬停效果
@mixin hover-lift($distance: 2px) {
  transition: transform var(--duration-normal) var(--ease-out);
  
  &:hover {
    transform: translateY(-$distance);
  }
}

@mixin hover-scale($scale: 1.02) {
  transition: transform var(--duration-normal) var(--ease-out);
  
  &:hover {
    transform: scale($scale);
  }
}

// 网格布局
@mixin grid($columns: auto-fit, $min-width: 280px, $gap: var(--spacing-6)) {
  display: grid;
  grid-template-columns: repeat($columns, minmax($min-width, 1fr));
  gap: $gap;
}

// Flexbox 工具
@mixin flex($direction: row, $wrap: nowrap, $justify: flex-start, $align: stretch) {
  display: flex;
  flex-direction: $direction;
  flex-wrap: $wrap;
  justify-content: $justify;
  align-items: $align;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏文本但保持可访问性
@mixin sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 状态颜色
@mixin status-color($status) {
  @if $status == success {
    color: var(--success-500);
    background-color: var(--success-100);
  } @else if $status == warning {
    color: var(--warning-500);
    background-color: var(--warning-100);
  } @else if $status == error {
    color: var(--error-500);
    background-color: var(--error-100);
  } @else if $status == info {
    color: var(--info-500);
    background-color: var(--info-100);
  }
}
