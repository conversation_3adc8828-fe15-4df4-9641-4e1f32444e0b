<template>
  <v-app>
    <!-- 主布局 -->
    <AppLayout />
  </v-app>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useTheme } from 'vuetify';
import AppLayout from '@/components/layout/AppLayout.vue';

// 使用Vuetify主题
const theme = useTheme();

// 应用初始化
onMounted(async () => {
  try {
    console.log('🚀 Print Terminal 应用初始化开始');
    console.log('✅ Print Terminal 应用初始化完成');
  } catch (error) {
    console.error('❌ 应用初始化失败:', error);
  }
});
</script>

<style lang="scss">
// 全局样式
.v-application {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  background: var(--v-theme-background) !important;
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(var(--v-theme-on-surface), 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--v-theme-on-surface), 0.3);
}

// 禁用文本选择（除了输入框）
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input, textarea, [contenteditable] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

// 动画优化
* {
  box-sizing: border-box;
}

.v-enter-active,
.v-leave-active {
  transition: all 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

// 工具类
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

// 响应式工具类
@media (max-width: 600px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (max-width: 960px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (max-width: 1280px) {
  .hidden-md {
    display: none !important;
  }
}
</style>
