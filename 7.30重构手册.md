# 打印端重构手册 - 7.30版本

## 📋 重构概述

基于对现有print-terminal-app系统的深入分析，本手册提供了一套完整的重构方案，旨在构建一个以Ghostscript为核心、本地数据库为权威、Material UI为界面的现代化云打印系统。

## 🎯 重构目标

### 核心原则
1. **本地数据库为权威** - 所有业务数据以本地SQLite数据库为准
2. **云端任务队列** - 云端仅作为任务分发和最终状态收集的中转站
3. **单向数据流** - 任务从云端拉取，状态向云端回写，避免双向同步冲突
4. **离线优先** - 支持离线处理已下载的订单和文件
5. **Ghostscript为核心** - 所有文档处理、打印功能围绕Ghostscript展开

### 业务流程
```
新订单同步 → 文件下载 → Ghostscript文档处理 → 标签生成 → Ghostscript文件合并 → 打印执行 → 物流发货 → 状态回写
```

## 🏗️ 系统架构设计

### 🎯 离线优先架构原则

#### 核心设计理念
> **重要**: 打印端采用"离线优先"架构，所有业务模块只访问本地数据库，通过专门的同步服务与云端交互。

#### 架构优势
1. **高可用性**: 网络故障不影响核心业务流程
2. **高性能**: 本地数据库访问速度快，用户体验佳
3. **数据一致性**: 本地数据库作为单一数据源
4. **简化开发**: 业务模块无需处理网络异常
5. **易于测试**: 可以完全离线测试业务逻辑

#### 数据流向设计
```mermaid
graph TD
    A[云端数据库] -->|定时同步| B[SyncService]
    B -->|写入| C[本地SQLite数据库]
    C -->|读取| D[OrderService]
    C -->|读取| E[FileService]
    C -->|读取| F[PrintService]
    C -->|读取| G[LogisticsService]
    D -->|状态更新| C
    E -->|处理状态| C
    F -->|打印状态| C
    G -->|发货状态| C
    H[发货扫描] -->|扫描快递单| G
    D -->|标记同步| I[同步队列]
    G -->|标记同步| I
    I -->|批量推送| B
    B -->|状态回写| A
```

#### 模块职责分离
- **SyncService**: 唯一负责云端交互的服务
- **业务模块**: 只访问本地数据库，不直接访问云端
- **本地数据库**: 作为单一数据源，确保数据一致性
- **同步队列**: 缓存待同步的状态变更

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端界面层 (Material UI)                    │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Service Layer)                  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ 订单管理服务 │ 文件处理服务 │ 打印队列服务 │ 物流发货服务 │ 同步服务     │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                 Ghostscript核心引擎层                        │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ 文档处理引擎 │ 打印驱动引擎 │ 颜色管理引擎 │ 预印刷引擎   │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    数据持久层 (SQLite)                       │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ 订单数据表   │ 文件数据表   │ 打印任务表   │ 配置数据表   │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    外部接口层                                │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ 腾讯云接口   │ 打印机驱动   │ 物流接口     │ 支付接口     │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择

#### 前端技术栈
- **框架**: Electron + Vue 3 + TypeScript
- **UI组件库**: Vuetify + Materio模板
- **界面风格**: prototype.html
参考 https://modernize-nextjs.adminmart.com/
- **状态管理**: Pinia
- **构建工具**: Vite

#### 后端技术栈
- **运行时**: Node.js 20.x
- **数据库**: SQLite (本地权威数据源)
- **文档处理**: Ghostscript 10.0+
- **云服务**: 腾讯云开发
- **进程通信**: Electron IPC

#### 核心依赖
```json
{
  "dependencies": {
    "@mui/material": "^5.14.0",
    "@mui/icons-material": "^5.14.0",
    "@emotion/react": "^11.11.0",
    "@emotion/styled": "^11.11.0",
    "electron": "^25.0.0",
    "vue": "^3.3.0",
    "typescript": "^5.0.0",
    "sequelize": "^6.32.0",
    "sqlite3": "^5.1.0",
    "@cloudbase/node-sdk": "^2.0.0"
  }
}
```

## 📊 数据库设计

### 🎯 设计原则
1. **本地优先**: SQLite作为权威数据源
2. **性能优化**: 合理的索引设计和查询优化
3. **数据完整性**: 外键约束和事务保证
4. **扩展性**: 预留字段支持未来功能扩展
5. **同步支持**: 内置云端同步状态管理

### 核心数据表结构

#### 订单表 (orders)
```sql
CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT UNIQUE NOT NULL,           -- 订单号
    order_no TEXT NOT NULL,                  -- 显示订单号
    user_name TEXT NOT NULL,                 -- 用户姓名
    user_phone TEXT NOT NULL,                -- 用户电话
    status TEXT DEFAULT 'pending_process',    -- 订单状态

    -- 打印配置 (JSON格式存储小程序参数)
    print_config TEXT NOT NULL,              -- 完整打印配置
    binding_config TEXT,                     -- 装订配置

    -- 价格信息
    print_price REAL DEFAULT 0,              -- 打印费用
    binding_price REAL DEFAULT 0,            -- 装订费用
    delivery_price REAL DEFAULT 0,           -- 配送费用
    original_price REAL DEFAULT 0,           -- 原价
    discount REAL DEFAULT 0,                 -- 优惠金额
    total_price REAL DEFAULT 0,              -- 实付金额

    -- 配送信息
    delivery_type TEXT DEFAULT 'express',    -- 配送方式
    delivery_address TEXT,                   -- 配送地址

    -- 物流信息
    logistics_company TEXT,                  -- 物流公司
    tracking_number TEXT,                    -- 快递单号
    shipping_time INTEGER,                   -- 发货时间
    shipping_status TEXT DEFAULT 'pending',  -- 发货状态: pending/shipped/delivered
    estimated_delivery_time INTEGER,         -- 预计送达时间
    actual_delivery_time INTEGER,            -- 实际送达时间

    -- 时间戳
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,

    -- 云端同步
    cloud_sync_status TEXT DEFAULT 'pending', -- 同步状态
    cloud_sync_time INTEGER,                 -- 同步时间

    -- 备注
    remarks TEXT,                            -- 用户备注
    internal_notes TEXT                      -- 内部备注
);
```

#### 文件表 (files)
```sql
CREATE TABLE files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    cloud_file_id TEXT,                      -- 云端文件ID
    file_name TEXT NOT NULL,                 -- 文件名
    original_name TEXT,                      -- 原始文件名
    file_size INTEGER,                       -- 文件大小
    file_type TEXT,                          -- 文件类型
    local_path TEXT,                         -- 本地路径
    cloud_url TEXT,                          -- 云端URL
    download_status TEXT DEFAULT '待下载',    -- 下载状态
    status TEXT DEFAULT '待处理',             -- 处理状态
    page_count INTEGER,                      -- 页数

    -- Ghostscript处理相关
    gs_processed_path TEXT,                  -- GS处理后路径
    gs_preview_path TEXT,                    -- 预览图路径
    gs_analysis_result TEXT,                 -- 分析结果(JSON)

    -- 处理历史
    process_history TEXT,                    -- 处理历史(JSON)

    -- 时间戳
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,

    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

#### 打印任务表 (print_jobs)
```sql
CREATE TABLE print_jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_id TEXT UNIQUE NOT NULL,             -- 任务ID
    order_id INTEGER NOT NULL,               -- 订单ID
    file_ids TEXT,                           -- 文件ID列表(JSON) - 支持多文件打印
    printer_name TEXT NOT NULL,              -- 打印机名称
    status TEXT DEFAULT 'queued',            -- 任务状态: queued, printing, completed, failed, cancelled
    priority INTEGER DEFAULT 5,              -- 优先级(1-10)

    -- 打印配置
    print_config TEXT NOT NULL,              -- 打印配置(JSON)
    copies INTEGER DEFAULT 1,                -- 份数

    -- Ghostscript配置
    gs_device TEXT,                          -- GS设备
    gs_options TEXT,                         -- GS选项(JSON)

    -- 智能选择信息
    selection_mode TEXT DEFAULT 'auto',      -- 选择模式: auto, manual
    selection_reason TEXT,                   -- 选择原因
    selection_confidence REAL,               -- 选择置信度(0-1)

    -- 执行信息
    start_time INTEGER,                      -- 开始时间
    end_time INTEGER,                        -- 结束时间
    progress INTEGER DEFAULT 0,              -- 进度(0-100)
    error_message TEXT,                      -- 错误信息

    -- 时间戳
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,

    FOREIGN KEY (order_id) REFERENCES orders(id)
    -- 注意: file_ids是JSON数组，不使用外键约束
);
```

#### 物流发货表 (logistics_shipments)
```sql
CREATE TABLE logistics_shipments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    shipment_id TEXT UNIQUE NOT NULL,        -- 发货单号
    order_id INTEGER NOT NULL,               -- 订单ID

    -- 物流信息
    logistics_company TEXT NOT NULL,         -- 物流公司
    logistics_company_code TEXT,             -- 物流公司代码
    tracking_number TEXT UNIQUE NOT NULL,    -- 快递单号

    -- 发货信息
    shipping_status TEXT DEFAULT 'pending',  -- 发货状态
    package_weight REAL,                     -- 包裹重量(kg)
    package_size TEXT,                       -- 包裹尺寸(长x宽x高cm)
    package_count INTEGER DEFAULT 1,         -- 包裹数量

    -- 收件人信息
    recipient_name TEXT NOT NULL,            -- 收件人姓名
    recipient_phone TEXT NOT NULL,           -- 收件人电话
    recipient_address TEXT NOT NULL,         -- 收件人地址

    -- 时间信息
    shipping_time INTEGER,                   -- 发货时间
    estimated_delivery_time INTEGER,         -- 预计送达时间
    actual_delivery_time INTEGER,            -- 实际送达时间

    -- 操作信息
    operator_name TEXT,                      -- 操作员姓名
    scan_time INTEGER,                       -- 扫描时间
    scan_device TEXT,                        -- 扫描设备

    -- 备注信息
    shipping_notes TEXT,                     -- 发货备注
    delivery_instructions TEXT,              -- 配送说明

    -- 时间戳
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,

    -- 云端同步
    cloud_sync_status TEXT DEFAULT '待同步',
    cloud_sync_time INTEGER,

    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

#### 物流公司配置表 (logistics_companies)
```sql
CREATE TABLE logistics_companies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_code TEXT UNIQUE NOT NULL,       -- 公司代码
    company_name TEXT NOT NULL,              -- 公司名称
    company_name_en TEXT,                    -- 英文名称

    -- API配置
    api_enabled BOOLEAN DEFAULT 0,           -- 是否启用API
    api_url TEXT,                           -- API地址
    api_key TEXT,                           -- API密钥
    api_secret TEXT,                        -- API密钥

    -- 服务配置
    service_types TEXT,                      -- 服务类型(JSON)
    supported_regions TEXT,                  -- 支持区域(JSON)
    price_config TEXT,                       -- 价格配置(JSON)

    -- 状态配置
    status BOOLEAN DEFAULT 1,                -- 是否启用
    sort_order INTEGER DEFAULT 0,            -- 排序

    -- 时间戳
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL
);
```

#### 物流轨迹表 (logistics_tracking)
```sql
CREATE TABLE logistics_tracking (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    shipment_id INTEGER NOT NULL,           -- 发货单ID
    tracking_number TEXT NOT NULL,          -- 快递单号

    -- 轨迹信息
    status_code TEXT NOT NULL,              -- 状态代码
    status_description TEXT NOT NULL,       -- 状态描述
    location TEXT,                          -- 当前位置
    operator TEXT,                          -- 操作员

    -- 时间信息
    tracking_time INTEGER NOT NULL,         -- 轨迹时间
    update_time INTEGER NOT NULL,           -- 更新时间

    -- 数据来源
    data_source TEXT DEFAULT 'manual',      -- 数据来源: manual/api/webhook
    raw_data TEXT,                          -- 原始数据(JSON)

    FOREIGN KEY (shipment_id) REFERENCES logistics_shipments(id)
);
```

#### 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT UNIQUE NOT NULL,         -- 配置键
    config_value TEXT,                       -- 配置值(JSON)
    config_type TEXT NOT NULL,               -- 配置类型
    description TEXT,                        -- 配置描述
    is_encrypted BOOLEAN DEFAULT FALSE,      -- 是否加密存储

    -- 时间戳
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL
);

-- 插入默认配置
INSERT INTO system_config (config_key, config_value, config_type, description, create_time, update_time) VALUES
('file_paths', '{"cdnDownloadPath":"./downloads","processingPaths":{"inputDir":"./input","outputDir":"./output","tempDir":"./temp","archiveDir":"./archive"},"labelPaths":{"templateDir":"./templates","generatedDir":"./labels"},"systemPaths":{"logDir":"./logs","configDir":"./config","backupDir":"./backup","cacheDir":"./cache"},"cleanupSettings":{"enableAutoCleanup":true,"tempFileRetentionDays":7,"archiveRetentionDays":30,"logRetentionDays":30}}', 'file_management', '文件路径配置', datetime('now'), datetime('now')),
('cdn_download', '{"downloadSettings":{"maxConcurrentDownloads":3,"downloadTimeout":300,"retryAttempts":3,"retryDelay":5},"storageSettings":{"maxStorageSize":100,"compressionEnabled":true,"encryptionEnabled":false},"cacheStrategy":{"enableCache":true,"cacheExpiration":24,"maxCacheSize":1024},"networkSettings":{"useProxy":false}}', 'file_management', 'CDN下载配置', datetime('now'), datetime('now')),
('printer_settings', '{"defaultPrinter":"","printerDiscovery":{"autoDetect":true,"refreshInterval":300,"enableCapabilityProbing":true},"intelligentSelection":{"enabled":true,"selectionStrategy":"hybrid","fallbackToManual":true},"printerGroups":[{"name":"彩色打印组","description":"用于彩色文档打印","printers":[],"autoAssignRules":{"colorMode":"color"}},{"name":"黑白打印组","description":"用于黑白文档打印","printers":[],"autoAssignRules":{"colorMode":"mono"}},{"name":"骑马订打印组","description":"用于骑马订装订","printers":[],"autoAssignRules":{"bindingType":["saddle"]}}],"printers":[],"queueSettings":{"maxConcurrentJobs":3,"retryFailedJobs":true,"jobTimeout":300,"priorityLevels":10,"loadBalancing":true,"scheduling":{"algorithm":"intelligent","considerPrinterLoad":true,"considerJobComplexity":true}},"selectionUI":{"showCapabilities":true,"showStatistics":true,"showRecommendations":true,"groupByCapability":true,"enableQuickSelect":true}}', 'printer', '智能打印机配置', datetime('now'), datetime('now')),
('ghostscript_settings', '{"installPath":"C:/Program Files/gs/gs10.00.0","performance":{"maxMemory":1024,"bufferSize":256,"enableMultiThreading":true,"threadCount":4},"fontSettings":{"fontPath":"C:/Windows/Fonts","enableCJKSupport":true,"defaultCJKFont":"SimSun","fontSubstitution":true},"colorManagement":{"defaultICCProfile":"sRGB","enableColorManagement":true,"renderingIntent":"RelativeColorimetric"}}', 'ghostscript', 'Ghostscript配置', datetime('now'), datetime('now')),
('sync_settings', '{"syncInterval":5,"networkSettings":{"timeout":30,"retryAttempts":3,"useProxy":false},"syncStrategy":{"batchSize":50,"prioritySync":true,"conflictResolution":"local"}}', 'sync', '同步配置', datetime('now'), datetime('now')),
('performance_settings', '{"systemResources":{"maxCPUUsage":80,"maxMemoryUsage":80,"diskSpaceWarning":90},"logging":{"logLevel":"info","maxLogFileSize":100,"logRotation":true,"retentionDays":30},"monitoring":{"enablePerformanceMonitoring":true,"metricsInterval":60,"alertThresholds":{"cpuUsage":90,"memoryUsage":90,"diskUsage":95,"errorRate":5}}}', 'performance', '性能配置', datetime('now'), datetime('now'));
```

#### 同步队列表 (sync_queue)
```sql
CREATE TABLE sync_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id TEXT UNIQUE NOT NULL,            -- 队列项ID
    item_type TEXT NOT NULL,                 -- 类型: order_status, file_upload, etc.
    order_id TEXT,                           -- 关联订单ID
    action TEXT NOT NULL,                    -- 操作类型
    data TEXT,                               -- 数据内容(JSON)
    priority INTEGER DEFAULT 5,              -- 优先级(1-10)
    retry_count INTEGER DEFAULT 0,           -- 重试次数
    max_retries INTEGER DEFAULT 3,           -- 最大重试次数
    status TEXT DEFAULT 'pending',           -- 状态: pending, syncing, completed, failed
    error_message TEXT,                      -- 错误信息

    -- 时间戳
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,
    scheduled_time INTEGER,                  -- 计划执行时间
    completed_time INTEGER                   -- 完成时间
);
```

### 📈 数据库性能优化

#### 索引策略
```sql
-- 订单表索引 - 优化查询性能
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_create_time ON orders(create_time);
CREATE INDEX idx_orders_user_phone ON orders(user_phone);
CREATE INDEX idx_orders_order_no ON orders(order_no);
CREATE INDEX idx_orders_status_create_time ON orders(status, create_time);

-- 文件表索引 - 优化文件查询
CREATE INDEX idx_files_order_id ON files(order_id);
CREATE INDEX idx_files_status ON files(status);
CREATE INDEX idx_files_file_type ON files(file_type);
CREATE INDEX idx_files_download_status ON files(download_status);

-- 打印任务表索引 - 优化队列管理
CREATE INDEX idx_print_jobs_order_id ON print_jobs(order_id);
CREATE INDEX idx_print_jobs_status ON print_jobs(status);
CREATE INDEX idx_print_jobs_create_time ON print_jobs(create_time);
CREATE INDEX idx_print_jobs_printer_name ON print_jobs(printer_name);

-- 复合索引 - 优化复杂查询
CREATE INDEX idx_orders_status_time ON orders(status, create_time);
CREATE INDEX idx_files_order_status ON files(order_id, status);
CREATE INDEX idx_print_jobs_status_printer ON print_jobs(status, printer_name);

-- 系统配置表索引
CREATE INDEX idx_system_config_key ON system_config(config_key);
CREATE INDEX idx_system_config_type ON system_config(config_type);

-- 同步队列表索引
CREATE INDEX idx_sync_queue_status ON sync_queue(status);
CREATE INDEX idx_sync_queue_priority ON sync_queue(priority, create_time);
CREATE INDEX idx_sync_queue_order_id ON sync_queue(order_id);
CREATE INDEX idx_sync_queue_type ON sync_queue(item_type);
CREATE INDEX idx_sync_queue_scheduled ON sync_queue(scheduled_time);
```

#### SQLite性能配置
```sql
-- 性能优化设置
PRAGMA journal_mode = WAL;           -- 启用WAL模式提高并发性能
PRAGMA synchronous = NORMAL;         -- 平衡性能和安全性
PRAGMA cache_size = 10000;           -- 增加缓存大小(约40MB)
PRAGMA temp_store = memory;          -- 临时表存储在内存中
PRAGMA mmap_size = 268435456;        -- 启用内存映射(256MB)
PRAGMA optimize;                     -- 自动优化查询计划

-- 定期维护
PRAGMA incremental_vacuum;           -- 增量清理
PRAGMA analyze;                      -- 更新统计信息
```

#### 查询优化示例
```typescript
// 优化前：全表扫描
const orders = await db.all('SELECT * FROM orders WHERE status = ?', [status]);

// 优化后：使用索引和分页
const orders = await db.all(`
  SELECT * FROM orders
  WHERE status = ?
  ORDER BY create_time DESC
  LIMIT ? OFFSET ?
`, [status, pageSize, offset]);

// 复杂查询优化：使用复合索引
const orderStats = await db.all(`
  SELECT status, COUNT(*) as count, AVG(total_price) as avg_price
  FROM orders
  WHERE create_time >= ? AND create_time <= ?
  GROUP BY status
  ORDER BY count DESC
`, [startTime, endTime]);
```

## 🔧 核心服务模块

### 1. 订单管理服务 (OrderService)

#### 核心功能
- 订单数据的CRUD操作 (仅访问本地数据库)
- 订单状态流转管理 (仅更新本地数据库)
- 本地订单数据管理和维护
- 订单统计和查询 (基于本地数据)

#### 🔄 数据流向原则
> **重要**: OrderService 只负责本地数据库的读写操作，不直接访问云端。数据同步由专门的 SyncService 负责。

**正确的数据流**:
```
云端数据库 → SyncService → 本地数据库 → OrderService → 前端界面
                ↑
        状态回写 ← 发货管理 ← OrderService
```

#### 关键方法
```typescript
class OrderService {
  // 本地订单查询 (仅访问本地数据库)
  async getOrderList(options: QueryOptions): Promise<PaginatedResult<Order>>

  // 本地订单详情 (仅访问本地数据库)
  async getOrderDetails(orderId: string): Promise<Order>

  // 本地状态更新 (仅更新本地数据库，标记为待同步)
  async updateOrderStatus(orderId: string, status: OrderStatus, notes?: string): Promise<boolean>

  // 批量本地更新 (仅操作本地数据库)
  async batchUpdateOrders(orderIds: string[], updates: Partial<Order>): Promise<void>

  // 本地订单统计 (基于本地数据)
  async getOrderStatistics(dateRange?: DateRange): Promise<OrderStats>

  // 标记订单需要同步 (用于状态变更后的云端同步)
  async markOrderForSync(orderId: string): Promise<void>
}
```

### 2. 文件处理服务 (FileService)

#### 核心功能
- 文件下载和本地存储
- 文件格式转换和优化
- 文件预览生成
- 文件完整性验证

#### Ghostscript集成
```typescript
class FileService {
  // 处理文件(使用Ghostscript)
  async processFile(fileId: string, options: ProcessOptions): Promise<ProcessResult>

  // 生成预览
  async generatePreview(filePath: string, options: PreviewOptions): Promise<string>

  // 文档分析
  async analyzeDocument(filePath: string): Promise<DocumentAnalysis>

  // 格式转换
  async convertFormat(inputPath: string, outputFormat: string): Promise<string>
}
```

### 3. 标签生成服务 (LabelService)

#### 核心功能
- 标签页模板管理
- 订单信息渲染
- 文件合并处理
- 二维码/条形码生成

#### 实现方案
```typescript
class LabelService {
  // 生成完整打印稿
  async createCompletePrintPackage(orderId: string): Promise<string>

  // 生成标签页
  async generateLabelPage(order: Order): Promise<string>

  // 合并文件
  async mergeFiles(labelPath: string, filePaths: string[], options: MergeOptions): Promise<string>

  // 更新模板
  async updateTemplate(templateId: string, template: LabelTemplate): Promise<boolean>
}
```

### 4. 智能打印队列服务 (PrintQueueService)

#### 核心功能
- 智能打印机选择（支持15台打印机管理）
- 基于Ghostscript的统一打印驱动
- 打印任务队列管理和优先级调度
- 打印机状态监控和能力检测
- 自动/手动打印机分配策略
- 错误处理和重试机制

#### 打印机管理架构
```typescript
// 打印机能力检测和管理
interface PrinterCapabilities {
  name: string;                           // 打印机名称
  displayName: string;                    // 显示名称
  manufacturer: string;                   // 制造商
  model: string;                          // 型号

  // 物理能力
  paperSizes: string[];                   // 支持纸张: ['A4', 'A3', 'Letter']
  colorSupport: 'mono' | 'color';         // 颜色支持
  duplexSupport: boolean;                 // 双面打印
  maxResolution: number;                  // 最大分辨率

  // Ghostscript设备映射
  ghostscriptDevice: string;              // 最佳GS设备: 'mswinpr2', 'tiff24nc'
  ghostscriptOptions: string[];           // GS选项

  // 专业功能
  bindingSupport: string[];               // 装订支持: ['saddle', 'perfect', 'spiral']
  specialFeatures: string[];              // 特殊功能: ['borderless', 'photo_paper']

  // 状态信息
  isOnline: boolean;                      // 在线状态
  isDefault: boolean;                     // 默认打印机
  currentJobs: number;                    // 当前任务数
  lastUsed: number;                       // 最后使用时间
}

// 智能打印机选择器
class IntelligentPrinterSelector {
  constructor(
    private printerManager: PrinterManager,
    private orderAnalyzer: OrderAnalyzer
  ) {}

  // 自动选择最佳打印机
  async selectOptimalPrinter(order: Order): Promise<PrinterSelection> {
    const orderAnalysis = await this.orderAnalyzer.analyzeOrder(order);
    const availablePrinters = await this.printerManager.getAvailablePrinters();

    // 按订单特征筛选打印机
    const candidates = this.filterPrintersByRequirements(availablePrinters, orderAnalysis);

    // 负载均衡选择
    const selectedPrinter = this.selectByLoadBalancing(candidates);

    return {
      printer: selectedPrinter,
      reason: this.generateSelectionReason(orderAnalysis, selectedPrinter),
      confidence: this.calculateConfidence(orderAnalysis, selectedPrinter)
    };
  }

  // 根据订单要求筛选打印机
  private filterPrintersByRequirements(
    printers: PrinterCapabilities[],
    analysis: OrderAnalysis
  ): PrinterCapabilities[] {
    return printers.filter(printer => {
      // 纸张大小匹配
      if (!printer.paperSizes.includes(analysis.paperSize)) return false;

      // 颜色要求匹配
      if (analysis.requiresColor && printer.colorSupport === 'mono') return false;

      // 双面打印要求
      if (analysis.requiresDuplex && !printer.duplexSupport) return false;

      // 装订要求匹配
      if (analysis.bindingType && !printer.bindingSupport.includes(analysis.bindingType)) return false;

      // 打印机在线且可用
      if (!printer.isOnline || printer.currentJobs >= 5) return false;

      return true;
    });
  }

  // 负载均衡选择
  private selectByLoadBalancing(candidates: PrinterCapabilities[]): PrinterCapabilities {
    if (candidates.length === 0) {
      throw new Error('没有可用的打印机满足订单要求');
    }

    // 按当前任务数排序，选择负载最轻的
    return candidates.sort((a, b) => {
      // 优先考虑任务数
      if (a.currentJobs !== b.currentJobs) {
        return a.currentJobs - b.currentJobs;
      }

      // 其次考虑最后使用时间（负载均衡）
      return a.lastUsed - b.lastUsed;
    })[0];
  }
}
```

#### 打印机管理服务
```typescript
class PrinterManager {
  private printers: Map<string, PrinterCapabilities> = new Map();
  private printerProfiles: Map<string, PrinterProfile> = new Map();

  constructor(private ghostscriptService: GhostscriptService) {
    this.loadPrinterProfiles();
    this.detectSystemPrinters();
  }

  // 检测系统安装的打印机
  async detectSystemPrinters(): Promise<PrinterCapabilities[]> {
    const systemPrinters = await this.getSystemPrinterList();
    const detectedPrinters: PrinterCapabilities[] = [];

    for (const systemPrinter of systemPrinters) {
      const capabilities = await this.detectPrinterCapabilities(systemPrinter);
      this.printers.set(systemPrinter.name, capabilities);
      detectedPrinters.push(capabilities);
    }

    return detectedPrinters;
  }

  // 获取系统打印机列表（跨平台）
  private async getSystemPrinterList(): Promise<SystemPrinter[]> {
    const platform = process.platform;

    if (platform === 'win32') {
      return this.getWindowsPrinters();
    } else if (platform === 'darwin') {
      return this.getMacPrinters();
    } else {
      return this.getLinuxPrinters();
    }
  }

  // Windows打印机检测
  private async getWindowsPrinters(): Promise<SystemPrinter[]> {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      const { stdout } = await execAsync('wmic printer get name,drivername,portname /format:csv');
      return this.parseWindowsPrinterOutput(stdout);
    } catch (error) {
      console.error('Windows打印机检测失败:', error);
      return [];
    }
  }

  // 打印机能力检测
  private async detectPrinterCapabilities(systemPrinter: SystemPrinter): Promise<PrinterCapabilities> {
    // 1. 从预定义配置文件查找
    const knownProfile = this.printerProfiles.get(systemPrinter.model);
    if (knownProfile) {
      return this.buildCapabilitiesFromProfile(systemPrinter, knownProfile);
    }

    // 2. 通过Ghostscript探测
    const gsCapabilities = await this.probeWithGhostscript(systemPrinter.name);

    // 3. 通过系统API探测
    const systemCapabilities = await this.probeSystemCapabilities(systemPrinter.name);

    // 4. 合并结果
    return this.mergeCapabilities(systemPrinter, gsCapabilities, systemCapabilities);
  }

  // Ghostscript能力探测
  private async probeWithGhostscript(printerName: string): Promise<Partial<PrinterCapabilities>> {
    try {
      // 使用Ghostscript测试打印机能力
      const testCommand = [
        'gs',
        '-dNOPAUSE',
        '-dBATCH',
        '-dSAFER',
        '-sDEVICE=nullpage',
        '-dQueryDeviceCapabilities=true',
        `-sOutputFile=%printer%${printerName}`,
        // 使用空白测试页
        '-c', 'showpage'
      ];

      const result = await this.ghostscriptService.executeCommand(testCommand);
      return this.parseGhostscriptCapabilities(result.output);
    } catch (error) {
      console.warn(`Ghostscript探测打印机 ${printerName} 失败:`, error);
      return {};
    }
  }
}

// 预定义打印机配置文件
interface PrinterProfile {
  manufacturer: string;
  model: string;
  series: string;

  // Ghostscript最佳设备映射
  ghostscriptDevice: string;
  ghostscriptOptions: string[];

  // 物理能力
  capabilities: {
    maxResolution: number;
    colorSupport: 'mono' | 'color';
    paperSizes: string[];
    duplexSupport: boolean;
    borderlessSupport: boolean;
    bindingSupport: string[];
  };

  // ICC配置文件
  iccProfile?: string;

  // 特殊功能
  specialFeatures: string[];
}

// 打印机配置数据库
const PRINTER_PROFILES: Record<string, PrinterProfile> = {
  // HP LaserJet 系列
  'HP_LaserJet_Pro_M404': {
    manufacturer: 'HP',
    model: 'LaserJet Pro M404',
    series: 'LaserJet Pro',
    ghostscriptDevice: 'mswinpr2',
    ghostscriptOptions: ['-dColorConversionStrategy=/Gray'],
    capabilities: {
      maxResolution: 1200,
      colorSupport: 'mono',
      paperSizes: ['A4', 'Letter', 'Legal'],
      duplexSupport: true,
      borderlessSupport: false,
      bindingSupport: ['perfect']
    },
    specialFeatures: ['duplex', 'collate', 'staple']
  },

  // Canon PIXMA 系列
  'Canon_PIXMA_PRO-100': {
    manufacturer: 'Canon',
    model: 'PIXMA PRO-100',
    series: 'PIXMA PRO',
    ghostscriptDevice: 'tiff48nc',
    ghostscriptOptions: ['-dTextAlphaBits=4', '-dGraphicsAlphaBits=4'],
    capabilities: {
      maxResolution: 4800,
      colorSupport: 'color',
      paperSizes: ['A4', 'Letter', '4x6', '5x7', '8x10'],
      duplexSupport: false,
      borderlessSupport: true,
      bindingSupport: []
    },
    iccProfile: 'Canon_PIXMA_PRO-100.icc',
    specialFeatures: ['borderless', 'photo_paper', 'wide_gamut']
  },

  // Epson SureColor 系列
  'Epson_SureColor_P800': {
    manufacturer: 'Epson',
    model: 'SureColor P800',
    series: 'SureColor',
    ghostscriptDevice: 'tiffsep',
    ghostscriptOptions: ['-dPrintSpotCMYK=true'],
    capabilities: {
      maxResolution: 2880,
      colorSupport: 'color',
      paperSizes: ['A4', 'A3', 'Letter', 'Tabloid'],
      duplexSupport: false,
      borderlessSupport: true,
      bindingSupport: []
    },
    specialFeatures: ['spot_colors', 'wide_gamut', 'professional_photo']
  }
};
```

#### Ghostscript统一打印引擎
```typescript
class PrintQueueService {
  constructor(
    private db: DatabaseService,
    private orderService: OrderService,
    private logisticsService: LogisticsService,
    private printerManager: PrinterManager,
    private printerSelector: IntelligentPrinterSelector,
    private eventEmitter: EventEmitter
  ) {}

  // 智能添加打印任务（自动选择打印机）
  async addPrintJob(jobConfig: PrintJobConfig): Promise<string> {
    const jobId = `PRINT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 获取订单详情用于智能选择
    const order = await this.orderService.getOrderDetails(jobConfig.orderId);

    let selectedPrinter: PrinterCapabilities;
    let selectionMode: 'auto' | 'manual' = 'auto';

    // 如果指定了打印机，验证其可用性
    if (jobConfig.printerName) {
      selectedPrinter = await this.printerManager.getPrinter(jobConfig.printerName);
      if (!selectedPrinter || !selectedPrinter.isOnline) {
        throw new Error(`指定的打印机 ${jobConfig.printerName} 不可用`);
      }
      selectionMode = 'manual';
    } else {
      // 自动选择最佳打印机
      const selection = await this.printerSelector.selectOptimalPrinter(order);
      selectedPrinter = selection.printer;

      console.log(`自动选择打印机: ${selectedPrinter.name}, 原因: ${selection.reason}`);
    }

    // 根据打印机能力优化Ghostscript配置
    const optimizedGsConfig = await this.optimizeGhostscriptConfig(
      selectedPrinter,
      order,
      jobConfig.gsOptions
    );

    await this.db.run(`
      INSERT INTO print_jobs (
        job_id, order_id, file_ids, printer_name, status,
        print_config, copies, gs_device, gs_options,
        selection_mode, create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      jobId, jobConfig.orderId, JSON.stringify(jobConfig.fileIds),
      selectedPrinter.name, 'queued', JSON.stringify(jobConfig.printConfig),
      jobConfig.copies, optimizedGsConfig.device, JSON.stringify(optimizedGsConfig.options),
      selectionMode, Date.now(), Date.now()
    ]);

    // 更新打印机使用统计
    await this.printerManager.updatePrinterUsage(selectedPrinter.name);

    // 触发队列处理
    this.processQueue();

    return jobId;
  }

  // 根据打印机能力优化Ghostscript配置
  private async optimizeGhostscriptConfig(
    printer: PrinterCapabilities,
    order: Order,
    baseOptions: any[] = []
  ): Promise<{ device: string; options: string[] }> {
    const orderAnalysis = await this.analyzeOrderRequirements(order);

    // 选择最佳Ghostscript设备
    let device = printer.ghostscriptDevice;
    let options = [...printer.ghostscriptOptions, ...baseOptions];

    // 根据订单特征调整设备和选项
    if (orderAnalysis.hasPhotos && printer.capabilities.maxResolution >= 2400) {
      // 照片打印优化
      device = 'tiff48nc';
      options.push(
        '-dTextAlphaBits=4',
        '-dGraphicsAlphaBits=4',
        '-dColorImageResolution=2400',
        '-dJPEGQ=95'
      );
    } else if (orderAnalysis.hasSpotColors && printer.specialFeatures.includes('spot_colors')) {
      // 专色打印优化
      device = 'tiffsep';
      options.push(
        '-dPrintSpotCMYK=true',
        '-dSeparationColorNames=' + JSON.stringify(orderAnalysis.spotColors)
      );
    } else if (orderAnalysis.isTextHeavy) {
      // 文本优化
      options.push(
        '-dTextAlphaBits=4',
        '-dMonoImageResolution=1200'
      );
    }

    // 双面打印配置
    if (orderAnalysis.requiresDuplex && printer.capabilities.duplexSupport) {
      options.push('-dDuplex=true');
    }

    // 颜色管理
    if (printer.iccProfile && orderAnalysis.requiresColorManagement) {
      options.push(
        `-sDefaultRGBProfile=${printer.iccProfile}`,
        '-dColorConversionStrategy=/UseDeviceIndependentColor'
      );
    }

    return { device, options };
  }

  // 分析订单打印要求
  private async analyzeOrderRequirements(order: Order): Promise<OrderAnalysis> {
    const printConfig = JSON.parse(order.print_config);

    return {
      paperSize: printConfig.paperSize || 'A4',
      requiresColor: printConfig.colorMode === 'color',
      requiresDuplex: printConfig.duplex === true,
      bindingType: printConfig.bindingType,
      hasPhotos: printConfig.documentType === 'photo',
      hasSpotColors: printConfig.spotColors?.length > 0,
      spotColors: printConfig.spotColors || [],
      isTextHeavy: printConfig.documentType === 'text',
      requiresColorManagement: printConfig.colorManagement === true,
      quality: printConfig.quality || 'standard'
    };
  }

  // 获取可用打印机列表（用于手动选择）
  async getAvailablePrinters(): Promise<PrinterInfo[]> {
    const printers = await this.printerManager.getAvailablePrinters();

    return printers.map(printer => ({
      name: printer.name,
      displayName: printer.displayName,
      manufacturer: printer.manufacturer,
      model: printer.model,
      isOnline: printer.isOnline,
      currentJobs: printer.currentJobs,
      capabilities: {
        colorSupport: printer.capabilities.colorSupport,
        duplexSupport: printer.capabilities.duplexSupport,
        paperSizes: printer.capabilities.paperSizes,
        bindingSupport: printer.capabilities.bindingSupport,
        maxResolution: printer.capabilities.maxResolution
      },
      suitableFor: this.calculateSuitability(printer)
    }));
  }

  // 计算打印机适用性标签
  private calculateSuitability(printer: PrinterCapabilities): string[] {
    const tags: string[] = [];

    if (printer.capabilities.colorSupport === 'color') {
      tags.push('彩色打印');
    }

    if (printer.capabilities.duplexSupport) {
      tags.push('双面打印');
    }

    if (printer.capabilities.maxResolution >= 2400) {
      tags.push('高分辨率');
    }

    if (printer.specialFeatures.includes('photo_paper')) {
      tags.push('照片打印');
    }

    if (printer.capabilities.bindingSupport.includes('saddle')) {
      tags.push('骑马订');
    }

    if (printer.capabilities.bindingSupport.includes('perfect')) {
      tags.push('胶装');
    }

    return tags;
  }

  // 执行打印任务（增强版）
  async executePrintJob(jobId: string): Promise<PrintResult> {
    try {
      // 更新任务状态为执行中
      await this.updateJobStatus(jobId, 'printing', 0);

      // 获取任务详情
      const job = await this.getJobDetails(jobId);

      // 获取打印机信息并验证
      const printer = await this.printerManager.getPrinter(job.printerName);
      if (!printer || !printer.isOnline) {
        throw new Error(`打印机 ${job.printerName} 不可用`);
      }

      // 执行Ghostscript打印
      const printResult = await this.executeGhostscriptPrint(job, printer);

      if (printResult.success) {
        // 打印成功，更新状态
        await this.updateJobStatus(jobId, 'completed', 100);

        // 更新打印机统计
        await this.printerManager.updatePrintStatistics(job.printerName, {
          jobsCompleted: 1,
          pagesProcessed: job.fileIds.length,
          lastSuccessfulPrint: Date.now()
        });

        // 更新订单状态为打印完成
        await this.orderService.updateOrderStatus(job.orderId, OrderStatus.PRINT_COMPLETED);

        // 🎯 关键：触发物流发货准备
        await this.triggerShippingPreparation(job.orderId);

        return {
          success: true,
          jobId,
          orderId: job.orderId,
          message: '打印完成，已进入发货准备状态'
        };
      } else {
        // 打印失败
        await this.updateJobStatus(jobId, 'failed', 0, printResult.error);

        return {
          success: false,
          jobId,
          error: printResult.error
        };
      }
    } catch (error) {
      await this.updateJobStatus(jobId, 'failed', 0, error.message);

      return {
        success: false,
        jobId,
        error: error.message
      };
    }
  }

  // 增强的Ghostscript打印执行
  private async executeGhostscriptPrint(job: PrintJob, printer: PrinterCapabilities): Promise<PrintResult> {
    try {
      // 构建优化的Ghostscript命令
      const gsCommand = this.buildOptimizedGhostscriptCommand(job, printer);

      console.log(`执行打印命令: ${gsCommand.join(' ')}`);

      // 执行打印
      const result = await this.ghostscriptService.executeCommand(gsCommand);

      return {
        success: result.success,
        error: result.error,
        output: result.output,
        executionTime: result.executionTime
      };
    } catch (error) {
      return {
        success: false,
        error: `Ghostscript打印失败: ${error.message}`,
        output: '',
        executionTime: 0
      };
    }
  }

  // 构建优化的Ghostscript命令
  private buildOptimizedGhostscriptCommand(job: PrintJob, printer: PrinterCapabilities): string[] {
    const baseCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-dNOPROMPT'
    ];

    // 设备选择
    const device = job.gsDevice || printer.ghostscriptDevice || 'mswinpr2';
    baseCommand.push(`-sDEVICE=${device}`);

    // 输出目标
    baseCommand.push(`-sOutputFile=%printer%${job.printerName}`);

    // 打印机特定选项
    if (printer.ghostscriptOptions) {
      baseCommand.push(...printer.ghostscriptOptions);
    }

    // 任务特定选项
    if (job.gsOptions && job.gsOptions.length > 0) {
      baseCommand.push(...job.gsOptions);
    }

    // 添加文件路径
    const filePaths = job.fileIds.map(fileId => this.getFilePath(fileId));
    baseCommand.push(...filePaths);

    return baseCommand;
  }

  // 触发物流发货准备
  private async triggerShippingPreparation(orderId: string): Promise<void> {
    try {
      // 更新订单状态为待发货
      await this.orderService.updateOrderStatus(orderId, OrderStatus.PENDING_DELIVERY);

      // 发送事件通知
      this.eventEmitter.emit('order:print_completed', {
        orderId,
        timestamp: Date.now(),
        nextStep: 'shipping_preparation'
      });

      // 记录日志
      console.log(`订单 ${orderId} 打印完成，已进入发货准备状态`);

    } catch (error) {
      console.error(`触发发货准备失败 [${orderId}]:`, error);
    }
  }

  // 执行Ghostscript打印
  private async executeGhostscriptPrint(job: PrintJob): Promise<PrintResult> {
    // 构建Ghostscript打印命令
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      `-sDEVICE=${job.gsDevice || 'mswinpr2'}`,
      `-sOutputFile=%printer%${job.printerName}`,
      ...job.gsOptions,
      ...job.fileIds.map(fileId => this.getFilePath(fileId))
    ];

    // 执行打印命令
    const result = await this.ghostscriptService.executeCommand(gsCommand);

    return {
      success: result.success,
      error: result.error,
      executionTime: result.executionTime
    };
  }

  // 获取队列状态
  async getQueueStatus(): Promise<QueueStatus> {
    const stats = await this.db.get(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'queued' THEN 1 END) as queued,
        COUNT(CASE WHEN status = 'printing' THEN 1 END) as printing,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
      FROM print_jobs
      WHERE create_time > ?
    `, [Date.now() - 24 * 60 * 60 * 1000]); // 最近24小时

    return {
      total: stats.total,
      queued: stats.queued,
      printing: stats.printing,
      completed: stats.completed,
      failed: stats.failed,
      isProcessing: stats.printing > 0
    };
  }

  // 暂停/恢复队列
  async pauseQueue(): Promise<void> {
    this.isPaused = true;
    console.log('打印队列已暂停');
  }

  async resumeQueue(): Promise<void> {
    this.isPaused = false;
    console.log('打印队列已恢复');
    this.processQueue(); // 恢复后立即处理队列
  }

  // 处理打印队列
  private async processQueue(): Promise<void> {
    if (this.isPaused || this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      const pendingJobs = await this.db.all(`
        SELECT * FROM print_jobs
        WHERE status = 'queued'
        ORDER BY priority DESC, create_time ASC
        LIMIT 1
      `);

      for (const job of pendingJobs) {
        await this.executePrintJob(job.job_id);
      }
    } finally {
      this.isProcessing = false;
    }
  }
}
```

### 5. 同步服务 (SyncService)

#### 🎯 核心职责
> **重要**: SyncService 是唯一负责云端数据交互的服务，实现打印端的离线优先架构。

- **下行同步**: 从云端拉取新订单到本地数据库
- **上行同步**: 将本地状态变更推送到云端
- **离线支持**: 网络断开时缓存操作，恢复后自动同步
- **冲突解决**: 处理数据同步冲突
- **定时任务**: 定期执行同步操作

#### 🔄 同步策略
1. **启动同步**: 打印端启动时立即同步云端数据
2. **定时同步**: 每30分钟自动同步一次
3. **状态同步**: 订单状态变更后立即标记待同步
4. **批量同步**: 累积多个变更后批量推送

#### 📋 同步队列数据结构
```typescript
interface SyncQueueItem {
  id: string;                       // 同步项ID
  type: 'order_status' | 'order_complete' | 'file_upload';
  orderId: string;                  // 关联订单ID
  action: string;                   // 同步动作
  data: any;                        // 同步数据
  priority: 'high' | 'normal' | 'low'; // 优先级
  retryCount: number;               // 重试次数
  maxRetries: number;               // 最大重试次数
  createdAt: Date;                  // 创建时间
  lastAttempt?: Date;               // 最后尝试时间
  nextRetry?: Date;                 // 下次重试时间
  error?: string;                   // 错误信息
  status: 'pending' | 'syncing' | 'completed' | 'failed';
}
```

### 6. 物流发货服务 (LogisticsService)

#### 🎯 核心职责
> **重要**: LogisticsService 负责打印完成后的物流发货管理，实现从打印到客户收货的完整闭环。

- **发货管理**: 扫描快递单条码，记录物流信息
- **状态跟踪**: 实时更新发货状态和物流轨迹
- **多物流支持**: 支持多家物流公司的API集成
- **自动化流程**: 打印完成后自动触发发货流程
- **客户通知**: 发货后自动通知客户物流信息

#### 📦 发货流程设计
1. **打印完成触发**: 打印任务完成后自动进入发货准备状态
2. **包装准备**: 操作员进行产品包装和快递单准备
3. **扫描发货**: 使用扫描设备扫描快递单条码
4. **信息录入**: 自动识别物流公司和快递单号
5. **状态更新**: 更新订单状态为"已发货"
6. **轨迹同步**: 定期同步物流轨迹信息
7. **客户通知**: 通过云端推送发货通知给客户

#### 🚚 物流公司配置
```typescript
interface LogisticsCompany {
  code: string;                     // 公司代码 (如: SF, YTO, ZTO)
  name: string;                     // 公司名称
  apiEnabled: boolean;              // 是否启用API
  apiConfig: {
    baseUrl: string;                // API基础地址
    apiKey: string;                 // API密钥
    secret: string;                 // API密钥
    timeout: number;                // 超时时间
  };
  trackingConfig: {
    trackingUrl: string;            // 轨迹查询URL模板
    statusMapping: Record<string, string>; // 状态映射
  };
  barcodePattern: string;           // 快递单号正则表达式
  supportedServices: string[];      // 支持的服务类型
}
```

#### 📱 扫描设备集成
```typescript
interface ScannerDevice {
  deviceId: string;                 // 设备ID
  deviceName: string;               // 设备名称
  connectionType: 'USB' | 'Bluetooth' | 'WiFi'; // 连接类型
  isConnected: boolean;             // 连接状态
  lastScanTime?: Date;              // 最后扫描时间
  scanCount: number;                // 扫描次数
}

interface ScanResult {
  barcode: string;                  // 扫描到的条码
  scanTime: Date;                   // 扫描时间
  deviceId: string;                 // 扫描设备ID
  isValid: boolean;                 // 条码是否有效
  logisticsCompany?: string;        // 识别的物流公司
  trackingNumber?: string;          // 快递单号
}
```

#### 实现方案
```typescript
class LogisticsService {
  constructor(
    private db: DatabaseService,
    private scannerService: ScannerService,
    private orderService: OrderService,
    private syncService: SyncService
  ) {}

  // 扫描快递单发货
  async scanAndShip(orderId: string, barcode: string, operatorName: string): Promise<ShipmentResult> {
    try {
      // 1. 验证订单状态
      const order = await this.orderService.getOrder(orderId);
      if (order.status !== OrderStatus.PENDING_DELIVERY) {
        throw new Error('订单状态不允许发货');
      }

      // 2. 解析快递单号
      const parseResult = await this.parseTrackingNumber(barcode);
      if (!parseResult.isValid) {
        throw new Error('无效的快递单号');
      }

      // 3. 创建发货记录
      const shipment = await this.createShipment({
        orderId,
        trackingNumber: parseResult.trackingNumber,
        logisticsCompany: parseResult.logisticsCompany,
        operatorName,
        scanTime: Date.now()
      });

      // 4. 更新订单状态
      await this.orderService.updateOrderStatus(orderId, OrderStatus.SHIPPED);
      await this.updateOrderLogisticsInfo(orderId, shipment);

      // 5. 标记同步
      await this.syncService.markForSync('logistics_shipment', shipment.id);

      // 6. 启动轨迹跟踪
      await this.startTrackingMonitor(shipment.id);

      return {
        success: true,
        shipmentId: shipment.id,
        trackingNumber: parseResult.trackingNumber,
        logisticsCompany: parseResult.logisticsCompany
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 解析快递单号
  private async parseTrackingNumber(barcode: string): Promise<ParseResult> {
    const companies = await this.getLogisticsCompanies();

    for (const company of companies) {
      const pattern = new RegExp(company.barcodePattern);
      if (pattern.test(barcode)) {
        return {
          isValid: true,
          trackingNumber: barcode,
          logisticsCompany: company.code,
          companyName: company.name
        };
      }
    }

    return {
      isValid: false,
      error: '无法识别的快递单号格式'
    };
  }

  // 创建发货记录
  private async createShipment(shipmentData: CreateShipmentData): Promise<Shipment> {
    const shipmentId = `SHIP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const shipment = {
      id: shipmentId,
      shipmentId,
      orderId: shipmentData.orderId,
      trackingNumber: shipmentData.trackingNumber,
      logisticsCompany: shipmentData.logisticsCompany,
      shippingStatus: 'shipped',
      operatorName: shipmentData.operatorName,
      scanTime: shipmentData.scanTime,
      shippingTime: Date.now(),
      createTime: Date.now(),
      updateTime: Date.now(),
      cloudSyncStatus: 'pending'
    };

    await this.db.run(`
      INSERT INTO logistics_shipments (
        shipment_id, order_id, tracking_number, logistics_company,
        shipping_status, operator_name, scan_time, shipping_time,
        create_time, update_time, cloud_sync_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      shipment.shipmentId, shipment.orderId, shipment.trackingNumber,
      shipment.logisticsCompany, shipment.shippingStatus, shipment.operatorName,
      shipment.scanTime, shipment.shippingTime, shipment.createTime,
      shipment.updateTime, shipment.cloudSyncStatus
    ]);

    return shipment;
  }

  // 更新订单物流信息
  private async updateOrderLogisticsInfo(orderId: string, shipment: Shipment): Promise<void> {
    await this.db.run(`
      UPDATE orders SET
        logistics_company = ?,
        tracking_number = ?,
        shipping_time = ?,
        shipping_status = 'shipped',
        update_time = ?,
        cloud_sync_status = 'pending'
      WHERE order_id = ?
    `, [
      shipment.logisticsCompany,
      shipment.trackingNumber,
      shipment.shippingTime,
      Date.now(),
      orderId
    ]);
  }

  // 启动轨迹跟踪监控
  private async startTrackingMonitor(shipmentId: string): Promise<void> {
    // 添加到轨迹跟踪队列
    await this.addToTrackingQueue(shipmentId);
  }

  // 查询物流轨迹
  async queryLogisticsTracking(trackingNumber: string, companyCode: string): Promise<TrackingResult> {
    try {
      const company = await this.getLogisticsCompany(companyCode);

      if (company.apiEnabled) {
        // 使用API查询
        return await this.queryTrackingByAPI(trackingNumber, company);
      } else {
        // 返回手动录入的轨迹
        return await this.getManualTracking(trackingNumber);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 批量更新发货状态
  async batchUpdateShippingStatus(): Promise<BatchUpdateResult> {
    const pendingShipments = await this.getPendingShipments();
    const results: UpdateResult[] = [];

    for (const shipment of pendingShipments) {
      try {
        const tracking = await this.queryLogisticsTracking(
          shipment.trackingNumber,
          shipment.logisticsCompany
        );

        if (tracking.success && tracking.status) {
          await this.updateShipmentStatus(shipment.id, tracking.status);
          results.push({ shipmentId: shipment.id, success: true });
        }
      } catch (error) {
        results.push({
          shipmentId: shipment.id,
          success: false,
          error: error.message
        });
      }
    }

    return {
      total: pendingShipments.length,
      success: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  // 获取发货统计
  async getShippingStatistics(dateRange?: DateRange): Promise<ShippingStatistics> {
    const conditions = ['1=1'];
    const params: any[] = [];

    if (dateRange) {
      conditions.push('shipping_time BETWEEN ? AND ?');
      params.push(dateRange.start, dateRange.end);
    }

    const sql = `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN shipping_status = '已发货' THEN 1 END) as shipped,
        COUNT(CASE WHEN shipping_status = '运输中' THEN 1 END) as inTransit,
        COUNT(CASE WHEN shipping_status = '已签收' THEN 1 END) as delivered,
        logistics_company,
        COUNT(*) as companyCount
      FROM logistics_shipments
      WHERE ${conditions.join(' AND ')}
      GROUP BY logistics_company
    `;

    const result = await this.db.all(sql, params);

    return {
      totalShipments: result.reduce((sum, row) => sum + row.companyCount, 0),
      byStatus: {
        shipped: result.reduce((sum, row) => sum + row.shipped, 0),
        inTransit: result.reduce((sum, row) => sum + row.inTransit, 0),
        delivered: result.reduce((sum, row) => sum + row.delivered, 0)
      },
      byCompany: result.map(row => ({
        company: row.logistics_company,
        count: row.companyCount
      }))
    };
  }
}
```

```typescript
class SyncService {
  // 启动时全量同步 (云端 → 本地)
  async initialSync(): Promise<void>

  // 定时增量同步 (云端 → 本地)
  async pullNewOrders(): Promise<Order[]>

  // 推送状态更新 (本地 → 云端)
  async pushStatusUpdates(): Promise<boolean>

  // 标记订单需要同步
  async scheduleStatusSync(orderId: string, status: OrderStatus): Promise<void>

  // 批量推送待同步的状态
  async batchPushPendingUpdates(): Promise<void>

  // 同步文件下载 (云端 → 本地)
  async syncFileDownloads(): Promise<void>

  // 处理同步冲突
  async resolveConflicts(): Promise<void>

  // 启动定时同步任务
  startPeriodicSync(): void

  // 停止定时同步任务
  stopPeriodicSync(): void

  // 网络状态变化处理
  onNetworkStatusChange(isOnline: boolean): void

  // 获取同步统计信息
  getSyncStatistics(): SyncStatistics
}

interface SyncStatistics {
  totalPending: number;             // 待同步数量
  totalFailed: number;              // 失败数量
  lastSyncTime: Date;               // 最后同步时间
  nextSyncTime: Date;               // 下次同步时间
  networkStatus: 'online' | 'offline'; // 网络状态
}
```

### 6. 文件下载服务 (FileDownloadService)

#### 🎯 核心职责
> **重要**: 专门负责云端文件下载和本地存储管理，确保文件完整性和下载效率。

```typescript
class FileDownloadService {
  // 下载订单相关文件
  async downloadOrderFiles(orderId: string): Promise<DownloadResult[]>

  // 批量下载文件
  async batchDownloadFiles(fileIds: string[]): Promise<DownloadResult[]>

  // 检查文件完整性
  async verifyFileIntegrity(filePath: string, expectedHash: string): Promise<boolean>

  // 获取下载进度
  getDownloadProgress(fileId: string): DownloadProgress

  // 暂停/恢复下载
  async pauseDownload(fileId: string): Promise<void>
  async resumeDownload(fileId: string): Promise<void>

  // 清理临时文件
  async cleanupTempFiles(): Promise<void>
}

interface DownloadResult {
  fileId: string;
  filePath: string;
  status: 'success' | 'failed' | 'cancelled';
  error?: string;
  downloadTime: number;             // 下载耗时(ms)
  fileSize: number;                 // 文件大小(bytes)
}
```

### 7. 配置管理服务 (ConfigurationService)

#### 🎯 核心职责
```typescript
class ConfigurationService {
  // 获取应用配置
  getAppConfig(): AppConfiguration

  // 更新配置
  async updateConfig(key: string, value: any): Promise<void>

  // 智能打印机管理
  getPrinterConfigs(): PrinterConfiguration[]
  async detectSystemPrinters(): Promise<PrinterCapabilities[]>
  async updatePrinterConfig(printerName: string, config: PrinterConfiguration): Promise<void>
  async testPrinterConnection(printerName: string): Promise<boolean>
  async getPrinterStatistics(printerName: string): Promise<PrinterStatistics>

  // 打印机分组管理
  getPrinterGroups(): PrinterGroup[]
  async createPrinterGroup(group: PrinterGroup): Promise<void>
  async updatePrinterGroup(groupName: string, group: PrinterGroup): Promise<void>
  async deletePrinterGroup(groupName: string): Promise<void>
  async assignPrinterToGroup(printerName: string, groupName: string): Promise<void>

  // 获取Ghostscript配置
  getGhostscriptConfig(): GhostscriptConfiguration

  // 文件路径管理
  getFilePathConfig(): FilePathConfiguration
  async updateFilePathConfig(config: FilePathConfiguration): Promise<void>

  // CDN文件下载管理
  getCDNDownloadConfig(): CDNDownloadConfiguration
  async updateCDNDownloadConfig(config: CDNDownloadConfiguration): Promise<void>

  // 导入/导出配置
  async exportConfig(): Promise<string>
  async importConfig(configData: string): Promise<void>

  // 验证配置有效性
  async validateConfig(config: any): Promise<ValidationResult>
}
```

### 8. 文件管理服务 (FileManagementService)

#### 🎯 核心职责
```typescript
class FileManagementService {
  // CDN文件下载管理
  async downloadFromCDN(fileUrl: string, orderId: string): Promise<DownloadResult>
  async batchDownloadFromCDN(fileUrls: string[], orderId: string): Promise<BatchDownloadResult>

  // 本地文件管理
  async moveToProcessingDir(filePath: string): Promise<string>
  async moveToArchiveDir(filePath: string): Promise<string>
  async cleanupTempFiles(): Promise<CleanupResult>

  // 文件路径管理
  getProcessingPath(orderId: string, fileName: string): string
  getArchivePath(orderId: string, fileName: string): string
  getTempPath(fileName: string): string

  // 存储空间管理
  async checkStorageSpace(): Promise<StorageInfo>
  async optimizeStorage(): Promise<OptimizationResult>

  // 文件完整性验证
  async verifyFileIntegrity(filePath: string): Promise<IntegrityResult>
  async generateFileChecksum(filePath: string): Promise<string>

  // 更新文件下载状态
  async updateFileDownloadStatus(fileId: number, status: string, localPath?: string): Promise<void>

  // 批量更新文件状态
  async batchUpdateFileStatus(updates: Array<{fileId: number, status: string, localPath?: string}>): Promise<void>
}
```

## 🚀 高性能文件下载系统设计

### 📊 业务需求分析

#### 下载规模特征
- **日均文件量**: 100-3000个文件
- **文件大小**: 200MB-1GB per file
- **日均数据量**: 20GB-3TB
- **峰值处理**: 需要支持突发下载需求
- **网络环境**: 企业网络，相对稳定但需要考虑波动

#### 技术挑战
1. **大文件下载**: 单文件最大1GB，需要断点续传
2. **并发控制**: 避免网络拥塞，合理控制并发数
3. **存储管理**: 大量文件的本地存储和清理
4. **错误恢复**: 网络中断、服务器错误的自动恢复
5. **进度监控**: 实时下载进度和状态反馈

### 🏗️ 下载系统架构设计

#### 核心设计原则
1. **分片下载**: 大文件分片并行下载，提高效率
2. **断点续传**: 支持下载中断后从断点继续
3. **智能调度**: 根据网络状况动态调整下载策略
4. **资源管理**: 合理分配带宽和存储资源
5. **状态持久化**: 下载状态实时保存，支持重启恢复

#### 系统架构图
```mermaid
graph TD
    A[订单同步] --> B[下载调度器]
    B --> C[下载队列管理]
    C --> D[并发控制器]
    D --> E[分片下载器]
    E --> F[断点续传管理]
    F --> G[文件合并器]
    G --> H[完整性验证]
    H --> I[本地存储管理]

    J[网络监控] --> D
    K[存储监控] --> I
    L[进度监控] --> M[状态更新]
    M --> N[数据库更新]

    E --> O[CDN/云存储]
    I --> P[文件处理流程]
```

### 🔧 核心组件实现

#### 1. 下载调度器 (DownloadScheduler)
```typescript
class DownloadScheduler {
  private downloadQueue: PriorityQueue<DownloadTask>;
  private activeDownloads: Map<string, DownloadWorker>;
  private networkMonitor: NetworkMonitor;
  private storageMonitor: StorageMonitor;

  constructor(
    private config: DownloadConfiguration,
    private db: DatabaseService,
    private fileManager: FileManagementService
  ) {
    this.downloadQueue = new PriorityQueue();
    this.activeDownloads = new Map();
    this.networkMonitor = new NetworkMonitor();
    this.storageMonitor = new StorageMonitor();
  }

  // 启动下载调度
  async start(): Promise<void> {
    // 1. 恢复未完成的下载任务
    await this.recoverPendingDownloads();

    // 2. 启动调度循环
    this.scheduleLoop();

    // 3. 启动监控
    this.startMonitoring();
  }

  // 添加下载任务
  async addDownloadTask(orderId: string, files: FileInfo[]): Promise<void> {
    for (const file of files) {
      const task: DownloadTask = {
        id: `${orderId}_${file.id}`,
        orderId,
        fileId: file.id,
        url: file.cloudUrl,
        fileName: file.fileName,
        fileSize: file.fileSize,
        priority: this.calculatePriority(orderId, file),
        status: 'pending',
        createdAt: Date.now(),
        retryCount: 0,
        maxRetries: this.config.maxRetries
      };

      await this.saveDownloadTask(task);
      this.downloadQueue.enqueue(task, task.priority);
    }
  }

  // 调度循环
  private async scheduleLoop(): Promise<void> {
    while (true) {
      try {
        // 检查系统资源
        const canSchedule = await this.checkSystemResources();
        if (!canSchedule) {
          await this.sleep(5000); // 等待5秒后重试
          continue;
        }

        // 获取下一个任务
        const task = this.downloadQueue.dequeue();
        if (!task) {
          await this.sleep(1000);
          continue;
        }

        // 启动下载
        await this.startDownload(task);

      } catch (error) {
        console.error('调度循环错误:', error);
        await this.sleep(5000);
      }
    }
  }

  // 检查系统资源
  private async checkSystemResources(): Promise<boolean> {
    const networkStatus = await this.networkMonitor.getStatus();
    const storageStatus = await this.storageMonitor.getStatus();
    const activeCount = this.activeDownloads.size;

    return (
      networkStatus.isStable &&
      storageStatus.freeSpace > this.config.minFreeSpace &&
      activeCount < this.config.maxConcurrentDownloads
    );
  }

  // 计算任务优先级
  private calculatePriority(orderId: string, file: FileInfo): number {
    // 基础优先级
    let priority = 5;

    // 根据订单紧急程度调整
    const order = this.getOrderInfo(orderId);
    if (order?.isUrgent) priority += 3;

    // 根据文件大小调整（小文件优先）
    if (file.fileSize < 500 * 1024 * 1024) priority += 1; // 小于500MB

    // 根据文件类型调整
    if (file.fileType === 'pdf') priority += 1;

    return Math.min(priority, 10); // 最高优先级为10
  }
}
```

#### 2. 分片下载器 (ChunkedDownloader)
```typescript
class ChunkedDownloader {
  private readonly CHUNK_SIZE = 10 * 1024 * 1024; // 10MB per chunk
  private readonly MAX_CONCURRENT_CHUNKS = 4;     // 最大并发分片数

  constructor(
    private config: DownloadConfiguration,
    private progressCallback: (progress: DownloadProgress) => void
  ) {}

  // 执行分片下载
  async download(task: DownloadTask): Promise<DownloadResult> {
    try {
      // 1. 获取文件信息
      const fileInfo = await this.getFileInfo(task.url);
      const totalSize = fileInfo.contentLength;

      // 2. 计算分片
      const chunks = this.calculateChunks(totalSize);

      // 3. 检查已下载的分片
      const existingChunks = await this.checkExistingChunks(task.id);
      const pendingChunks = chunks.filter(chunk =>
        !existingChunks.some(existing => existing.index === chunk.index)
      );

      // 4. 并发下载分片
      const downloadPromises = this.createChunkDownloadPromises(
        task,
        pendingChunks,
        totalSize
      );

      await Promise.all(downloadPromises);

      // 5. 合并分片
      const finalPath = await this.mergeChunks(task, chunks);

      // 6. 验证文件完整性
      const isValid = await this.verifyFileIntegrity(finalPath, fileInfo.etag);

      if (!isValid) {
        throw new Error('文件完整性验证失败');
      }

      // 7. 清理临时文件
      await this.cleanupChunks(task.id);

      return {
        success: true,
        localPath: finalPath,
        fileSize: totalSize,
        downloadTime: Date.now() - task.startTime!
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        retryable: this.isRetryableError(error)
      };
    }
  }

  // 计算分片信息
  private calculateChunks(totalSize: number): ChunkInfo[] {
    const chunks: ChunkInfo[] = [];
    const chunkCount = Math.ceil(totalSize / this.CHUNK_SIZE);

    for (let i = 0; i < chunkCount; i++) {
      const start = i * this.CHUNK_SIZE;
      const end = Math.min(start + this.CHUNK_SIZE - 1, totalSize - 1);

      chunks.push({
        index: i,
        start,
        end,
        size: end - start + 1,
        status: 'pending'
      });
    }

    return chunks;
  }

  // 创建分片下载Promise
  private createChunkDownloadPromises(
    task: DownloadTask,
    chunks: ChunkInfo[],
    totalSize: number
  ): Promise<void>[] {
    const semaphore = new Semaphore(this.MAX_CONCURRENT_CHUNKS);

    return chunks.map(chunk =>
      semaphore.acquire().then(async (release) => {
        try {
          await this.downloadChunk(task, chunk, totalSize);
        } finally {
          release();
        }
      })
    );
  }

  // 下载单个分片
  private async downloadChunk(
    task: DownloadTask,
    chunk: ChunkInfo,
    totalSize: number
  ): Promise<void> {
    const chunkPath = this.getChunkPath(task.id, chunk.index);
    const tempPath = `${chunkPath}.tmp`;

    try {
      const response = await fetch(task.url, {
        headers: {
          'Range': `bytes=${chunk.start}-${chunk.end}`,
          'User-Agent': 'PrintTerminal/1.0'
        },
        signal: AbortSignal.timeout(this.config.chunkTimeout)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 流式写入文件
      const writer = fs.createWriteStream(tempPath);
      const reader = response.body?.getReader();

      if (!reader) {
        throw new Error('无法获取响应流');
      }

      let downloadedBytes = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        writer.write(value);
        downloadedBytes += value.length;

        // 更新进度
        this.updateChunkProgress(task.id, chunk.index, downloadedBytes, totalSize);
      }

      writer.end();

      // 重命名临时文件
      await fs.rename(tempPath, chunkPath);

      // 更新分片状态
      await this.updateChunkStatus(task.id, chunk.index, 'completed');

    } catch (error) {
      // 清理临时文件
      if (await fs.pathExists(tempPath)) {
        await fs.remove(tempPath);
      }

      throw error;
    }
  }

  // 合并分片文件
  private async mergeChunks(task: DownloadTask, chunks: ChunkInfo[]): Promise<string> {
    const finalPath = this.getFinalPath(task);
    const writer = fs.createWriteStream(finalPath);

    try {
      for (const chunk of chunks.sort((a, b) => a.index - b.index)) {
        const chunkPath = this.getChunkPath(task.id, chunk.index);
        const chunkData = await fs.readFile(chunkPath);
        writer.write(chunkData);
      }

      writer.end();
      return finalPath;

    } catch (error) {
      writer.destroy();
      if (await fs.pathExists(finalPath)) {
        await fs.remove(finalPath);
      }
      throw error;
    }
  }
}
```

#### 3. 断点续传管理器 (ResumeManager)
```typescript
class ResumeManager {
  constructor(
    private db: DatabaseService,
    private config: DownloadConfiguration
  ) {}

  // 保存下载进度
  async saveProgress(taskId: string, progress: DownloadProgress): Promise<void> {
    const progressData = {
      taskId,
      totalSize: progress.totalSize,
      downloadedSize: progress.downloadedSize,
      chunks: progress.chunks,
      lastUpdateTime: Date.now()
    };

    await this.db.run(
      `INSERT OR REPLACE INTO download_progress
       (task_id, progress_data, update_time) VALUES (?, ?, ?)`,
      [taskId, JSON.stringify(progressData), Date.now()]
    );
  }

  // 恢复下载进度
  async loadProgress(taskId: string): Promise<DownloadProgress | null> {
    const result = await this.db.get(
      'SELECT progress_data FROM download_progress WHERE task_id = ?',
      [taskId]
    );

    if (!result) return null;

    try {
      return JSON.parse(result.progress_data);
    } catch (error) {
      console.error('解析下载进度失败:', error);
      return null;
    }
  }

  // 清理过期的进度记录
  async cleanupExpiredProgress(): Promise<void> {
    const expireTime = Date.now() - (this.config.progressRetentionDays * 24 * 60 * 60 * 1000);

    await this.db.run(
      'DELETE FROM download_progress WHERE update_time < ?',
      [expireTime]
    );
  }

  // 检查任务是否可以续传
  async canResume(taskId: string): Promise<boolean> {
    const progress = await this.loadProgress(taskId);
    if (!progress) return false;

    // 检查分片文件是否存在
    const existingChunks = await this.verifyChunkFiles(taskId, progress.chunks);
    return existingChunks.length > 0;
  }

  // 验证分片文件
  private async verifyChunkFiles(taskId: string, chunks: ChunkProgress[]): Promise<ChunkProgress[]> {
    const validChunks: ChunkProgress[] = [];

    for (const chunk of chunks) {
      if (chunk.status === 'completed') {
        const chunkPath = this.getChunkPath(taskId, chunk.index);
        const exists = await fs.pathExists(chunkPath);

        if (exists) {
          const stat = await fs.stat(chunkPath);
          if (stat.size === chunk.size) {
            validChunks.push(chunk);
          }
        }
      }
    }

    return validChunks;
  }
}
```

#### 4. 网络监控器 (NetworkMonitor)
```typescript
class NetworkMonitor {
  private networkStatus: NetworkStatus;
  private speedHistory: number[] = [];
  private readonly SPEED_HISTORY_SIZE = 10;

  constructor() {
    this.networkStatus = {
      isOnline: true,
      isStable: true,
      downloadSpeed: 0,
      latency: 0,
      lastCheck: Date.now()
    };

    this.startMonitoring();
  }

  // 启动网络监控
  private startMonitoring(): void {
    // 每30秒检查一次网络状态
    setInterval(async () => {
      await this.checkNetworkStatus();
    }, 30000);

    // 每5秒测量一次网络速度
    setInterval(async () => {
      await this.measureNetworkSpeed();
    }, 5000);
  }

  // 检查网络状态
  private async checkNetworkStatus(): Promise<void> {
    try {
      const startTime = Date.now();
      const response = await fetch('https://www.baidu.com', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });

      const latency = Date.now() - startTime;

      this.networkStatus = {
        isOnline: response.ok,
        isStable: this.calculateStability(),
        downloadSpeed: this.getAverageSpeed(),
        latency,
        lastCheck: Date.now()
      };

    } catch (error) {
      this.networkStatus = {
        isOnline: false,
        isStable: false,
        downloadSpeed: 0,
        latency: 9999,
        lastCheck: Date.now()
      };
    }
  }

  // 测量网络速度
  private async measureNetworkSpeed(): Promise<void> {
    try {
      const testUrl = 'https://speed.cloudflare.com/__down?bytes=1048576'; // 1MB测试文件
      const startTime = Date.now();

      const response = await fetch(testUrl, {
        signal: AbortSignal.timeout(10000)
      });

      if (response.ok) {
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000; // 秒
        const speed = (1048576 * 8) / duration / 1000000; // Mbps

        this.speedHistory.push(speed);
        if (this.speedHistory.length > this.SPEED_HISTORY_SIZE) {
          this.speedHistory.shift();
        }
      }

    } catch (error) {
      // 网络测速失败，记录0速度
      this.speedHistory.push(0);
      if (this.speedHistory.length > this.SPEED_HISTORY_SIZE) {
        this.speedHistory.shift();
      }
    }
  }

  // 计算网络稳定性
  private calculateStability(): boolean {
    if (this.speedHistory.length < 3) return true;

    const recentSpeeds = this.speedHistory.slice(-3);
    const avgSpeed = recentSpeeds.reduce((sum, speed) => sum + speed, 0) / recentSpeeds.length;

    // 如果最近的速度变化超过50%，认为网络不稳定
    const variance = recentSpeeds.some(speed =>
      Math.abs(speed - avgSpeed) / avgSpeed > 0.5
    );

    return !variance && avgSpeed > 1; // 至少1Mbps且稳定
  }

  // 获取平均速度
  private getAverageSpeed(): number {
    if (this.speedHistory.length === 0) return 0;
    return this.speedHistory.reduce((sum, speed) => sum + speed, 0) / this.speedHistory.length;
  }

  // 获取网络状态
  getStatus(): NetworkStatus {
    return { ...this.networkStatus };
  }

  // 获取推荐的并发下载数
  getRecommendedConcurrency(): number {
    const speed = this.getAverageSpeed();

    if (speed > 100) return 6;      // 100Mbps以上：6个并发
    if (speed > 50) return 4;       // 50-100Mbps：4个并发
    if (speed > 20) return 3;       // 20-50Mbps：3个并发
    if (speed > 10) return 2;       // 10-20Mbps：2个并发
    return 1;                       // 10Mbps以下：1个并发
  }
}
```

#### 5. 存储监控器 (StorageMonitor)
```typescript
class StorageMonitor {
  private storageStatus: StorageStatus;

  constructor(private config: FilePathConfiguration) {
    this.storageStatus = {
      totalSpace: 0,
      usedSpace: 0,
      freeSpace: 0,
      usagePercentage: 0,
      lastCheck: Date.now()
    };

    this.startMonitoring();
  }

  // 启动存储监控
  private startMonitoring(): void {
    // 每分钟检查一次存储状态
    setInterval(async () => {
      await this.checkStorageStatus();
    }, 60000);

    // 立即检查一次
    this.checkStorageStatus();
  }

  // 检查存储状态
  private async checkStorageStatus(): Promise<void> {
    try {
      const downloadPath = this.config.cdnDownloadPath;
      const stats = await fs.statSync(downloadPath);

      // 获取磁盘使用情况（Windows）
      const diskUsage = await this.getDiskUsage(downloadPath);

      this.storageStatus = {
        totalSpace: diskUsage.total,
        usedSpace: diskUsage.used,
        freeSpace: diskUsage.free,
        usagePercentage: (diskUsage.used / diskUsage.total) * 100,
        lastCheck: Date.now()
      };

      // 检查是否需要清理
      if (this.storageStatus.usagePercentage > 85) {
        await this.triggerCleanup();
      }

    } catch (error) {
      console.error('存储状态检查失败:', error);
    }
  }

  // 获取磁盘使用情况
  private async getDiskUsage(path: string): Promise<{total: number, used: number, free: number}> {
    return new Promise((resolve, reject) => {
      const { exec } = require('child_process');
      const drive = path.substring(0, 2); // 获取盘符，如 "C:"

      exec(`wmic logicaldisk where caption="${drive}" get size,freespace /value`, (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }

        const lines = stdout.split('\n').filter(line => line.includes('='));
        const freeSpace = parseInt(lines.find(line => line.startsWith('FreeSpace'))?.split('=')[1] || '0');
        const totalSpace = parseInt(lines.find(line => line.startsWith('Size'))?.split('=')[1] || '0');
        const usedSpace = totalSpace - freeSpace;

        resolve({
          total: totalSpace,
          used: usedSpace,
          free: freeSpace
        });
      });
    });
  }

  // 触发自动清理
  private async triggerCleanup(): Promise<void> {
    console.log('存储空间不足，触发自动清理...');

    // 清理临时文件
    await this.cleanupTempFiles();

    // 清理过期的归档文件
    await this.cleanupArchiveFiles();

    // 清理过期的日志文件
    await this.cleanupLogFiles();
  }

  // 清理临时文件
  private async cleanupTempFiles(): Promise<void> {
    const tempDir = this.config.processingPaths.tempDir;
    const retentionTime = this.config.cleanupSettings.tempFileRetentionDays * 24 * 60 * 60 * 1000;
    const cutoffTime = Date.now() - retentionTime;

    try {
      const files = await fs.readdir(tempDir);

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stat = await fs.stat(filePath);

        if (stat.mtime.getTime() < cutoffTime) {
          await fs.remove(filePath);
          console.log(`清理临时文件: ${filePath}`);
        }
      }
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  }

  // 获取存储状态
  getStatus(): StorageStatus {
    return { ...this.storageStatus };
  }

  // 预估下载空间需求
  estimateDownloadSpace(fileSize: number, chunkCount: number): number {
    // 分片文件 + 最终文件 + 10%缓冲
    return (fileSize + (fileSize * chunkCount * 0.1)) * 1.1;
  }

  // 检查是否有足够空间
  hasEnoughSpace(requiredSpace: number): boolean {
    return this.storageStatus.freeSpace > requiredSpace;
  }
}
```

### 📊 下载性能优化策略

#### 1. 智能并发控制
```typescript
class ConcurrencyController {
  private currentConcurrency: number = 3;
  private readonly MIN_CONCURRENCY = 1;
  private readonly MAX_CONCURRENCY = 8;

  constructor(
    private networkMonitor: NetworkMonitor,
    private storageMonitor: StorageMonitor
  ) {
    this.startAdaptiveControl();
  }

  // 启动自适应并发控制
  private startAdaptiveControl(): void {
    setInterval(() => {
      this.adjustConcurrency();
    }, 30000); // 每30秒调整一次
  }

  // 动态调整并发数
  private adjustConcurrency(): void {
    const networkStatus = this.networkMonitor.getStatus();
    const storageStatus = this.storageMonitor.getStatus();

    // 基于网络状况调整
    let recommendedConcurrency = this.networkMonitor.getRecommendedConcurrency();

    // 基于存储状况调整
    if (storageStatus.usagePercentage > 90) {
      recommendedConcurrency = Math.max(1, recommendedConcurrency - 2);
    } else if (storageStatus.usagePercentage > 80) {
      recommendedConcurrency = Math.max(1, recommendedConcurrency - 1);
    }

    // 基于系统负载调整
    const systemLoad = this.getSystemLoad();
    if (systemLoad > 80) {
      recommendedConcurrency = Math.max(1, recommendedConcurrency - 1);
    }

    // 应用调整
    this.currentConcurrency = Math.max(
      this.MIN_CONCURRENCY,
      Math.min(this.MAX_CONCURRENCY, recommendedConcurrency)
    );

    console.log(`并发数调整为: ${this.currentConcurrency}`);
  }

  // 获取当前并发数
  getCurrentConcurrency(): number {
    return this.currentConcurrency;
  }

  // 获取系统负载
  private getSystemLoad(): number {
    // 简化的系统负载计算
    const { exec } = require('child_process');
    return new Promise((resolve) => {
      exec('wmic cpu get loadpercentage /value', (error, stdout) => {
        if (error) {
          resolve(50); // 默认值
          return;
        }

        const match = stdout.match(/LoadPercentage=(\d+)/);
        const load = match ? parseInt(match[1]) : 50;
        resolve(load);
      });
    });
  }
}
```

#### 2. 错误处理与重试机制
```typescript
class ErrorHandler {
  private readonly RETRY_DELAYS = [1000, 3000, 5000, 10000, 30000]; // 递增延迟

  // 处理下载错误
  async handleDownloadError(
    task: DownloadTask,
    error: Error
  ): Promise<RetryDecision> {
    const errorType = this.classifyError(error);

    switch (errorType) {
      case 'NETWORK_ERROR':
        return this.handleNetworkError(task, error);

      case 'SERVER_ERROR':
        return this.handleServerError(task, error);

      case 'STORAGE_ERROR':
        return this.handleStorageError(task, error);

      case 'TIMEOUT_ERROR':
        return this.handleTimeoutError(task, error);

      default:
        return { shouldRetry: false, delay: 0, reason: '未知错误类型' };
    }
  }

  // 分类错误类型
  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase();

    if (message.includes('network') || message.includes('fetch')) {
      return 'NETWORK_ERROR';
    }

    if (message.includes('5') && message.includes('server')) {
      return 'SERVER_ERROR';
    }

    if (message.includes('enospc') || message.includes('disk')) {
      return 'STORAGE_ERROR';
    }

    if (message.includes('timeout') || message.includes('aborted')) {
      return 'TIMEOUT_ERROR';
    }

    return 'UNKNOWN_ERROR';
  }

  // 处理网络错误
  private async handleNetworkError(
    task: DownloadTask,
    error: Error
  ): Promise<RetryDecision> {
    if (task.retryCount >= task.maxRetries) {
      return { shouldRetry: false, delay: 0, reason: '超过最大重试次数' };
    }

    // 等待网络恢复
    const networkStatus = await this.waitForNetworkRecovery();
    if (!networkStatus.isOnline) {
      return { shouldRetry: false, delay: 0, reason: '网络持续不可用' };
    }

    const delay = this.RETRY_DELAYS[Math.min(task.retryCount, this.RETRY_DELAYS.length - 1)];
    return { shouldRetry: true, delay, reason: '网络错误，准备重试' };
  }

  // 等待网络恢复
  private async waitForNetworkRecovery(maxWait: number = 300000): Promise<NetworkStatus> {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait) {
      const status = await this.checkNetworkStatus();
      if (status.isOnline && status.isStable) {
        return status;
      }

      await this.sleep(5000); // 每5秒检查一次
    }

    return { isOnline: false, isStable: false, downloadSpeed: 0, latency: 9999, lastCheck: Date.now() };
  }

  // 处理存储错误
  private async handleStorageError(
    task: DownloadTask,
    error: Error
  ): Promise<RetryDecision> {
    // 尝试清理空间
    const cleanupResult = await this.performEmergencyCleanup();

    if (cleanupResult.freedSpace > task.estimatedSize) {
      return { shouldRetry: true, delay: 5000, reason: '清理空间后重试' };
    }

    return { shouldRetry: false, delay: 0, reason: '存储空间不足且无法清理' };
  }

  // 紧急清理
  private async performEmergencyCleanup(): Promise<CleanupResult> {
    // 实现紧急空间清理逻辑
    return {
      deletedFiles: 0,
      freedSpace: 0,
      errors: []
    };
  }
}
```

#### 3. 下载队列管理
```typescript
class DownloadQueue {
  private queue: PriorityQueue<DownloadTask>;
  private processing: Set<string> = new Set();
  private completed: Map<string, DownloadResult> = new Map();
  private failed: Map<string, DownloadError> = new Map();

  constructor() {
    this.queue = new PriorityQueue((a, b) => b.priority - a.priority);
  }

  // 添加下载任务
  enqueue(task: DownloadTask): void {
    if (this.processing.has(task.id) || this.completed.has(task.id)) {
      return; // 避免重复添加
    }

    this.queue.enqueue(task);
    this.saveQueueState();
  }

  // 获取下一个任务
  dequeue(): DownloadTask | null {
    const task = this.queue.dequeue();
    if (task) {
      this.processing.add(task.id);
    }
    return task;
  }

  // 标记任务完成
  markCompleted(taskId: string, result: DownloadResult): void {
    this.processing.delete(taskId);
    this.completed.set(taskId, result);
    this.saveQueueState();
  }

  // 标记任务失败
  markFailed(taskId: string, error: DownloadError): void {
    this.processing.delete(taskId);
    this.failed.set(taskId, error);

    // 如果可以重试，重新加入队列
    if (error.retryable && error.retryCount < error.maxRetries) {
      const task = this.createRetryTask(taskId, error);
      setTimeout(() => {
        this.enqueue(task);
      }, error.retryDelay);
    }

    this.saveQueueState();
  }

  // 获取队列统计
  getStatistics(): QueueStatistics {
    return {
      pending: this.queue.size(),
      processing: this.processing.size,
      completed: this.completed.size,
      failed: this.failed.size,
      totalTasks: this.queue.size() + this.processing.size + this.completed.size + this.failed.size
    };
  }

  // 保存队列状态
  private saveQueueState(): void {
    const state = {
      queue: this.queue.toArray(),
      processing: Array.from(this.processing),
      completed: Array.from(this.completed.entries()),
      failed: Array.from(this.failed.entries()),
      timestamp: Date.now()
    };

    localStorage.setItem('downloadQueueState', JSON.stringify(state));
  }

  // 恢复队列状态
  restoreQueueState(): void {
    const stateStr = localStorage.getItem('downloadQueueState');
    if (!stateStr) return;

    try {
      const state = JSON.parse(stateStr);

      // 恢复队列
      state.queue.forEach(task => this.queue.enqueue(task));

      // 恢复处理中的任务（重新加入队列）
      state.processing.forEach(taskId => {
        const task = this.findTaskById(taskId);
        if (task) {
          this.queue.enqueue(task);
        }
      });

      // 恢复完成和失败的记录
      this.completed = new Map(state.completed);
      this.failed = new Map(state.failed);

    } catch (error) {
      console.error('恢复队列状态失败:', error);
    }
  }
}
```

### 🔧 数据库扩展设计

#### 下载相关数据表
```sql
-- 下载任务表
CREATE TABLE download_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    file_id INTEGER NOT NULL,
    file_url TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_size INTEGER NOT NULL,

    -- 任务状态
    status TEXT DEFAULT 'pending',           -- pending, downloading, paused, completed, failed
    priority INTEGER DEFAULT 5,              -- 1-10优先级
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,

    -- 下载进度
    downloaded_size INTEGER DEFAULT 0,
    download_speed REAL DEFAULT 0,           -- KB/s
    estimated_time INTEGER DEFAULT 0,        -- 预估剩余时间(秒)

    -- 路径信息
    local_path TEXT,                         -- 最终文件路径
    temp_dir TEXT,                           -- 临时目录

    -- 时间信息
    create_time INTEGER NOT NULL,
    start_time INTEGER,
    complete_time INTEGER,
    update_time INTEGER NOT NULL,

    -- 错误信息
    error_message TEXT,
    error_type TEXT,

    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (file_id) REFERENCES files(id)
);

-- 下载进度表
CREATE TABLE download_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    chunk_start INTEGER NOT NULL,
    chunk_end INTEGER NOT NULL,
    chunk_size INTEGER NOT NULL,
    downloaded_size INTEGER DEFAULT 0,
    status TEXT DEFAULT 'pending',           -- pending, downloading, completed, failed
    chunk_path TEXT,                         -- 分片文件路径
    checksum TEXT,                           -- 分片校验和

    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,

    UNIQUE(task_id, chunk_index),
    FOREIGN KEY (task_id) REFERENCES download_tasks(task_id)
);

-- 下载统计表
CREATE TABLE download_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,                      -- YYYY-MM-DD
    total_files INTEGER DEFAULT 0,
    total_size INTEGER DEFAULT 0,           -- 字节
    completed_files INTEGER DEFAULT 0,
    completed_size INTEGER DEFAULT 0,
    failed_files INTEGER DEFAULT 0,
    avg_speed REAL DEFAULT 0,               -- 平均速度 KB/s
    peak_speed REAL DEFAULT 0,              -- 峰值速度 KB/s
    total_time INTEGER DEFAULT 0,           -- 总下载时间(秒)

    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,

    UNIQUE(date)
);

-- 网络状态记录表
CREATE TABLE network_status_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    is_online BOOLEAN NOT NULL,
    is_stable BOOLEAN NOT NULL,
    download_speed REAL DEFAULT 0,          -- Mbps
    latency INTEGER DEFAULT 0,              -- ms
    packet_loss REAL DEFAULT 0,             -- 丢包率 %

    create_time INTEGER NOT NULL
);

-- 存储状态记录表
CREATE TABLE storage_status_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    total_space INTEGER NOT NULL,           -- 字节
    used_space INTEGER NOT NULL,
    free_space INTEGER NOT NULL,
    usage_percentage REAL NOT NULL,

    create_time INTEGER NOT NULL
);

-- 索引优化
CREATE INDEX idx_download_tasks_status ON download_tasks(status);
CREATE INDEX idx_download_tasks_priority ON download_tasks(priority, create_time);
CREATE INDEX idx_download_tasks_order_id ON download_tasks(order_id);
CREATE INDEX idx_download_progress_task_id ON download_progress(task_id);
CREATE INDEX idx_download_progress_status ON download_progress(status);
CREATE INDEX idx_download_statistics_date ON download_statistics(date);
CREATE INDEX idx_network_status_timestamp ON network_status_log(timestamp);
CREATE INDEX idx_storage_status_timestamp ON storage_status_log(timestamp);
```

### 📱 前端下载管理界面

#### 下载管理页面设计
```typescript
// 下载管理组件
const DownloadManager: React.FC = () => {
  const [downloadTasks, setDownloadTasks] = useState<DownloadTask[]>([]);
  const [statistics, setStatistics] = useState<DownloadStatistics | null>(null);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus | null>(null);

  return (
    <Box sx={{ p: 3 }}>
      {/* 下载概览卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6">下载队列</Typography>
              <Typography variant="h4" color="primary">
                {statistics?.pending || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                等待下载
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6">进行中</Typography>
              <Typography variant="h4" color="warning.main">
                {statistics?.processing || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                正在下载
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6">已完成</Typography>
              <Typography variant="h4" color="success.main">
                {statistics?.completed || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                下载完成
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6">下载速度</Typography>
              <Typography variant="h4" color="info.main">
                {formatSpeed(networkStatus?.downloadSpeed || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                当前速度
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 下载任务列表 */}
      <Card>
        <CardHeader
          title="下载任务"
          action={
            <Box>
              <Button
                variant="outlined"
                onClick={handlePauseAll}
                sx={{ mr: 1 }}
              >
                暂停全部
              </Button>
              <Button
                variant="contained"
                onClick={handleResumeAll}
              >
                恢复全部
              </Button>
            </Box>
          }
        />
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>文件名</TableCell>
                  <TableCell>大小</TableCell>
                  <TableCell>进度</TableCell>
                  <TableCell>速度</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {downloadTasks.map((task) => (
                  <TableRow key={task.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {task.fileName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          订单: {task.orderId}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {formatFileSize(task.fileSize)}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ width: '100%' }}>
                        <LinearProgress
                          variant="determinate"
                          value={task.progress}
                          sx={{ mb: 1 }}
                        />
                        <Typography variant="caption">
                          {task.progress.toFixed(1)}%
                          ({formatFileSize(task.downloadedSize)} / {formatFileSize(task.fileSize)})
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {formatSpeed(task.downloadSpeed)}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(task.status)}
                        color={getStatusColor(task.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleTaskAction(task.id, 'pause')}
                        disabled={task.status !== 'downloading'}
                      >
                        <PauseIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleTaskAction(task.id, 'resume')}
                        disabled={task.status !== 'paused'}
                      >
                        <PlayArrowIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleTaskAction(task.id, 'retry')}
                        disabled={task.status !== 'failed'}
                      >
                        <RefreshIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};
```

### 🎯 下载系统集成方案

#### 与订单流程集成
```typescript
// 订单处理流程中的下载集成
class OrderProcessingService {
  constructor(
    private downloadScheduler: DownloadScheduler,
    private orderService: OrderService,
    private fileService: FileService
  ) {}

  // 处理新订单
  async processNewOrder(order: Order): Promise<void> {
    try {
      // 1. 保存订单到本地数据库
      await this.orderService.saveOrder(order);

      // 2. 获取订单文件列表
      const files = await this.fileService.getOrderFiles(order.orderId);

      // 3. 添加到下载队列
      await this.downloadScheduler.addDownloadTask(order.orderId, files);

      // 4. 更新订单状态
      await this.orderService.updateOrderStatus(order.orderId, OrderStatus.DOWNLOADING);

      console.log(`订单 ${order.orderId} 已加入下载队列，包含 ${files.length} 个文件`);

    } catch (error) {
      console.error('处理新订单失败:', error);
      await this.orderService.updateOrderStatus(order.orderId, OrderStatus.DOWNLOAD_FAILED);
    }
  }

  // 下载完成回调
  async onDownloadComplete(taskId: string, result: DownloadResult): Promise<void> {
    try {
      const task = await this.getDownloadTask(taskId);

      if (result.success) {
        // 更新文件状态
        await this.fileService.updateFileStatus(task.fileId, '已下载', result.localPath);

        // 检查订单是否所有文件都下载完成
        const allFilesDownloaded = await this.checkAllFilesDownloaded(task.orderId);

        if (allFilesDownloaded) {
          // 更新订单状态并触发下一步处理
          await this.orderService.updateOrderStatus(task.orderId, OrderStatus.PENDING_PROCESS);
          await this.triggerFileProcessing(task.orderId);
        }

      } else {
        // 下载失败处理
        await this.fileService.updateFileStatus(task.fileId, '下载失败');
        await this.orderService.updateOrderStatus(task.orderId, OrderStatus.DOWNLOAD_FAILED);
      }

    } catch (error) {
      console.error('下载完成回调处理失败:', error);
    }
  }

  // 触发文件处理
  private async triggerFileProcessing(orderId: string): Promise<void> {
    // 发送IPC消息到文件处理服务
    ipcMain.emit('start-file-processing', { orderId });
  }
}
```

### 📊 性能监控与优化

#### 下载性能指标
```typescript
interface DownloadMetrics {
  // 基础指标
  totalDownloads: number;           // 总下载数
  successfulDownloads: number;      // 成功下载数
  failedDownloads: number;          // 失败下载数
  successRate: number;              // 成功率 %

  // 性能指标
  averageSpeed: number;             // 平均下载速度 KB/s
  peakSpeed: number;                // 峰值下载速度 KB/s
  averageFileSize: number;          // 平均文件大小 bytes
  totalDataTransferred: number;     // 总传输数据量 bytes

  // 时间指标
  averageDownloadTime: number;      // 平均下载时间 seconds
  totalDownloadTime: number;        // 总下载时间 seconds
  queueWaitTime: number;            // 平均队列等待时间 seconds

  // 错误指标
  networkErrors: number;            // 网络错误次数
  serverErrors: number;             // 服务器错误次数
  storageErrors: number;            // 存储错误次数
  timeoutErrors: number;            // 超时错误次数

  // 资源使用
  peakConcurrency: number;          // 峰值并发数
  averageConcurrency: number;       // 平均并发数
  diskSpaceUsed: number;            // 磁盘空间使用 bytes
  networkBandwidthUsed: number;     // 网络带宽使用 KB/s
}

// 性能监控服务
class DownloadPerformanceMonitor {
  private metrics: DownloadMetrics;
  private metricsHistory: DownloadMetrics[] = [];

  constructor(private db: DatabaseService) {
    this.metrics = this.initializeMetrics();
    this.startPeriodicCollection();
  }

  // 启动定期数据收集
  private startPeriodicCollection(): void {
    // 每分钟收集一次指标
    setInterval(async () => {
      await this.collectMetrics();
    }, 60000);

    // 每小时保存一次历史数据
    setInterval(async () => {
      await this.saveMetricsHistory();
    }, 3600000);
  }

  // 收集性能指标
  private async collectMetrics(): Promise<void> {
    try {
      // 从数据库查询统计数据
      const stats = await this.queryDownloadStatistics();

      this.metrics = {
        ...this.metrics,
        ...stats,
        timestamp: Date.now()
      };

      // 计算衍生指标
      this.calculateDerivedMetrics();

    } catch (error) {
      console.error('收集性能指标失败:', error);
    }
  }

  // 查询下载统计
  private async queryDownloadStatistics(): Promise<Partial<DownloadMetrics>> {
    const today = new Date().toISOString().split('T')[0];

    // 查询今日下载统计
    const todayStats = await this.db.get(`
      SELECT
        total_files,
        completed_files,
        failed_files,
        total_size,
        completed_size,
        avg_speed,
        peak_speed,
        total_time
      FROM download_statistics
      WHERE date = ?
    `, [today]);

    // 查询错误统计
    const errorStats = await this.db.get(`
      SELECT
        COUNT(CASE WHEN error_type = 'NETWORK_ERROR' THEN 1 END) as network_errors,
        COUNT(CASE WHEN error_type = 'SERVER_ERROR' THEN 1 END) as server_errors,
        COUNT(CASE WHEN error_type = 'STORAGE_ERROR' THEN 1 END) as storage_errors,
        COUNT(CASE WHEN error_type = 'TIMEOUT_ERROR' THEN 1 END) as timeout_errors
      FROM download_tasks
      WHERE DATE(create_time/1000, 'unixepoch') = ?
      AND status = 'failed'
    `, [today]);

    return {
      totalDownloads: todayStats?.total_files || 0,
      successfulDownloads: todayStats?.completed_files || 0,
      failedDownloads: todayStats?.failed_files || 0,
      averageSpeed: todayStats?.avg_speed || 0,
      peakSpeed: todayStats?.peak_speed || 0,
      totalDataTransferred: todayStats?.completed_size || 0,
      totalDownloadTime: todayStats?.total_time || 0,
      networkErrors: errorStats?.network_errors || 0,
      serverErrors: errorStats?.server_errors || 0,
      storageErrors: errorStats?.storage_errors || 0,
      timeoutErrors: errorStats?.timeout_errors || 0
    };
  }

  // 计算衍生指标
  private calculateDerivedMetrics(): void {
    if (this.metrics.totalDownloads > 0) {
      this.metrics.successRate = (this.metrics.successfulDownloads / this.metrics.totalDownloads) * 100;
    }

    if (this.metrics.successfulDownloads > 0) {
      this.metrics.averageFileSize = this.metrics.totalDataTransferred / this.metrics.successfulDownloads;
      this.metrics.averageDownloadTime = this.metrics.totalDownloadTime / this.metrics.successfulDownloads;
    }
  }

  // 获取性能报告
  getPerformanceReport(): PerformanceReport {
    return {
      current: this.metrics,
      trend: this.calculateTrend(),
      recommendations: this.generateRecommendations()
    };
  }

  // 计算趋势
  private calculateTrend(): TrendAnalysis {
    if (this.metricsHistory.length < 2) {
      return { direction: 'stable', change: 0 };
    }

    const current = this.metrics;
    const previous = this.metricsHistory[this.metricsHistory.length - 1];

    const speedChange = ((current.averageSpeed - previous.averageSpeed) / previous.averageSpeed) * 100;
    const successRateChange = current.successRate - previous.successRate;

    return {
      direction: speedChange > 5 ? 'improving' : speedChange < -5 ? 'declining' : 'stable',
      speedChange,
      successRateChange,
      change: speedChange
    };
  }

  // 生成优化建议
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.metrics.successRate < 95) {
      recommendations.push('下载成功率偏低，建议检查网络连接和服务器状态');
    }

    if (this.metrics.averageSpeed < 1000) { // 小于1MB/s
      recommendations.push('下载速度较慢，建议优化网络配置或增加并发数');
    }

    if (this.metrics.networkErrors > this.metrics.totalDownloads * 0.1) {
      recommendations.push('网络错误频繁，建议检查网络稳定性');
    }

    if (this.metrics.storageErrors > 0) {
      recommendations.push('存在存储错误，建议检查磁盘空间和权限');
    }

    return recommendations;
  }
}
```

### 🔧 配置管理

#### 下载系统配置
```typescript
interface DownloadSystemConfiguration {
  // 基础配置
  maxConcurrentDownloads: number;      // 最大并发下载数
  chunkSize: number;                   // 分片大小 (bytes)
  maxConcurrentChunks: number;         // 每个文件最大并发分片数

  // 超时配置
  connectionTimeout: number;           // 连接超时 (ms)
  downloadTimeout: number;             // 下载超时 (ms)
  chunkTimeout: number;                // 分片超时 (ms)

  // 重试配置
  maxRetries: number;                  // 最大重试次数
  retryDelays: number[];               // 重试延迟序列 (ms)
  retryOnErrors: string[];             // 可重试的错误类型

  // 存储配置
  downloadPath: string;                // 下载目录
  tempPath: string;                    // 临时目录
  minFreeSpace: number;                // 最小可用空间 (bytes)
  autoCleanup: boolean;                // 自动清理

  // 网络配置
  userAgent: string;                   // User-Agent
  headers: Record<string, string>;     // 自定义请求头
  proxy?: ProxyConfiguration;          // 代理配置

  // 性能配置
  adaptiveConcurrency: boolean;        // 自适应并发控制
  networkMonitoring: boolean;          // 网络监控
  performanceLogging: boolean;         // 性能日志

  // 安全配置
  checksumVerification: boolean;       // 校验和验证
  encryptionEnabled: boolean;          // 加密存储
  secureDownload: boolean;             // 安全下载模式
}

// 默认配置
const DEFAULT_DOWNLOAD_CONFIG: DownloadSystemConfiguration = {
  maxConcurrentDownloads: 3,
  chunkSize: 10 * 1024 * 1024,        // 10MB
  maxConcurrentChunks: 4,

  connectionTimeout: 30000,            // 30秒
  downloadTimeout: 300000,             // 5分钟
  chunkTimeout: 60000,                 // 1分钟

  maxRetries: 3,
  retryDelays: [1000, 3000, 5000],
  retryOnErrors: ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'SERVER_ERROR'],

  downloadPath: './downloads',
  tempPath: './temp',
  minFreeSpace: 5 * 1024 * 1024 * 1024, // 5GB
  autoCleanup: true,

  userAgent: 'PrintTerminal/1.0',
  headers: {
    'Accept': '*/*',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive'
  },

  adaptiveConcurrency: true,
  networkMonitoring: true,
  performanceLogging: true,

  checksumVerification: true,
  encryptionEnabled: false,
  secureDownload: true
};
```

### 📈 实施建议

#### 分阶段实施计划

**第一阶段 (1-2周)**: 基础下载功能
- 实现基本的HTTP下载功能
- 简单的队列管理
- 基础的错误处理和重试
- 文件完整性验证

**第二阶段 (2-3周)**: 高级功能
- 分片下载和断点续传
- 智能并发控制
- 网络状态监控
- 存储空间管理

**第三阶段 (1-2周)**: 性能优化
- 自适应下载策略
- 性能监控和分析
- 用户界面优化
- 系统集成测试

**第四阶段 (1周)**: 部署和监控
- 生产环境部署
- 监控系统配置
- 性能调优
- 用户培训

#### 关键成功因素
1. **稳定的网络处理**: 重点关注网络异常的处理和恢复
2. **高效的存储管理**: 合理规划磁盘空间使用和清理策略
3. **用户体验**: 提供清晰的下载进度和状态反馈
4. **系统集成**: 与订单处理流程无缝集成
5. **监控和维护**: 建立完善的监控和告警机制

### 🔄 完整业务流程设计

#### 核心业务流程
```
新订单同步 → 文件下载队列 → 分片并行下载 → 断点续传 → 完整性验证 → 本地存储 → Ghostscript文档处理 → 标签生成 → Ghostscript文件合并(标签页+处理后文件) → 打印执行 → 物流发货 → 状态回写
```

#### 详细流程阶段

**阶段1: 订单接收与文件下载 (Order Reception & File Download)**
1. **订单同步**: SyncService从云端拉取新订单数据
2. **文件队列**: 订单关联文件自动加入下载队列，按优先级排序
3. **并行下载**: 启动多文件分片并行下载(最大8个并发)
4. **断点续传**: 网络中断时保存进度，恢复后自动继续
5. **完整性验证**: SHA256校验确保文件下载完整性
6. **本地存储**: 文件保存到指定目录，更新本地数据库

**阶段2: 文件预处理 (File Preprocessing)**
7. **Ghostscript处理**: PDF优化、颜色管理、格式标准化
8. **文档分析**: 页数统计、尺寸检测、内容结构分析
9. **质量检查**: 文件可打印性验证，错误检测与修复

**阶段3: 标签生成与Ghostscript文件合并 (Label Generation & Ghostscript File Merging)**
10. **标签生成**: 根据订单信息生成个性化标签页PDF文件
11. **Ghostscript合并策略**: 确定标签页与文档的合并顺序和Ghostscript参数
12. **Ghostscript文件合并**: 使用Ghostscript专业PDF处理引擎将标签页与处理后文档合并为单一PDF
13. **Ghostscript最终优化**: 通过Ghostscript进行合并后文件的压缩优化和质量验证

**阶段4: 打印执行与物流发货 (Print Execution & Logistics Shipping)**
14. **打印队列**: 合并后文件加入打印队列，按优先级执行
15. **打印执行**: 发送到指定打印机，监控打印进度
16. **包装准备**: 打印完成后进行产品包装和快递单准备
17. **物流发货**: 扫描快递单条码，记录物流信息，更新订单状态
18. **状态回写**: 发货状态同步到本地数据库和云端
19. **后续处理**: 文件归档、清理临时文件、更新统计数据

#### Ghostscript文件合并处理详细设计

##### Ghostscript合并配置接口
```typescript
interface GhostscriptMergeConfiguration {
  // 合并模式
  mergeMode: 'prepend' | 'append' | 'custom';  // 标签页位置策略

  // 标签页配置
  labelPageSettings: {
    position: 'first' | 'last' | 'both';       // 标签页位置
    pageSize: 'A4' | 'A3' | 'auto';            // 标签页尺寸
    orientation: 'portrait' | 'landscape';      // 标签页方向
    includeBlankPage: boolean;                  // 是否包含分隔空白页
    customTemplate: string;                     // 自定义标签模板路径
  };

  // Ghostscript合并选项
  ghostscriptOptions: {
    device: 'pdfwrite';                         // Ghostscript输出设备
    pdfSettings: '/printer' | '/ebook' | '/screen'; // PDF优化设置
    compatibilityLevel: '1.4' | '1.5' | '1.6'; // PDF兼容级别
    embedAllFonts: boolean;                     // 嵌入所有字体
    subsetFonts: boolean;                       // 字体子集化
    compressFonts: boolean;                     // 字体压缩
    preserveBookmarks: boolean;                 // 保留PDF书签
    preserveMetadata: boolean;                  // 保留文档元数据
    optimizeSize: boolean;                      // 文件大小优化
  };

  // Ghostscript质量控制
  qualityControl: {
    maxFileSize: number;                        // 最大文件大小限制(bytes)
    colorImageResolution: number;               // 彩色图像分辨率DPI
    grayImageResolution: number;                // 灰度图像分辨率DPI
    monoImageResolution: number;                // 单色图像分辨率DPI
    jpegQuality: number;                        // JPEG质量 0-100
    compressionLevel: number;                   // 压缩级别 1-9
    colorProfile: string;                       // ICC颜色配置文件路径
    detectDuplicateImages: boolean;             // 检测重复图像
    useFlateCompression: boolean;               // 使用Flate压缩
  };
}
```

##### Ghostscript文件合并服务核心实现
```typescript
class GhostscriptMergeService {
  constructor(
    private ghostscriptService: GhostscriptService,
    private labelService: LabelService,
    private orderService: OrderService,
    private config: GhostscriptMergeConfiguration,
    private db: DatabaseService
  ) {}

  // 主要合并流程
  async mergeOrderFiles(orderId: string): Promise<MergeResult> {
    const startTime = Date.now();

    try {
      console.log(`开始合并订单 ${orderId} 的文件...`);

      // 1. 获取订单信息和相关文件
      const order = await this.orderService.getOrder(orderId);
      const processedFiles = await this.getProcessedFiles(orderId);
      const labelFile = await this.labelService.generateLabel(orderId);

      // 2. 文件完整性验证
      await this.validateAllFiles([...processedFiles, labelFile]);

      // 3. 确定合并策略和顺序
      const mergeSequence = this.determineMergeSequence(
        order,
        processedFiles,
        labelFile
      );

      // 4. 执行Ghostscript文件合并
      const mergedFilePath = await this.performGhostscriptMerge(
        orderId,
        mergeSequence
      );

      // 5. Ghostscript后处理优化
      const optimizedFilePath = await this.ghostscriptPostProcess(mergedFilePath);

      // 6. 最终质量验证
      const qualityCheck = await this.validateMergedFile(optimizedFilePath);

      if (!qualityCheck.isValid) {
        throw new Error(`合并文件质量检查失败: ${qualityCheck.errors.join(', ')}`);
      }

      // 7. 更新数据库记录
      await this.updateMergeRecord(orderId, optimizedFilePath, qualityCheck);

      // 8. 触发下一阶段处理
      await this.triggerPrintQueue(orderId, optimizedFilePath);

      return {
        success: true,
        mergedFilePath: optimizedFilePath,
        totalPages: qualityCheck.pageCount,
        fileSize: qualityCheck.fileSize,
        processingTime: Date.now() - startTime,
        compressionRatio: this.calculateCompressionRatio(processedFiles, optimizedFilePath)
      };

    } catch (error) {
      console.error(`文件合并失败 [${orderId}]:`, error);

      // 更新失败状态
      await this.orderService.updateOrderStatus(orderId, OrderStatus.PROCESSING, '合并失败');

      return {
        success: false,
        error: error.message,
        retryable: this.isRetryableError(error),
        processingTime: Date.now() - startTime
      };
    }
  }

  // 确定合并顺序策略
  private determineMergeSequence(
    order: Order,
    processedFiles: ProcessedFile[],
    labelFile: LabelFile
  ): MergeSequence {
    const sequence: MergeItem[] = [];

    // 根据订单配置和系统设置确定标签页位置
    const labelPosition = order.printConfig?.labelPosition ||
                         this.config.labelPageSettings.position;

    switch (labelPosition) {
      case 'first':
        // 标签页在最前面
        sequence.push({
          type: 'label',
          file: labelFile,
          order: 0,
          pageRange: 'all'
        });

        // 添加分隔空白页
        if (this.config.labelPageSettings.includeBlankPage) {
          sequence.push({
            type: 'blank',
            file: null,
            order: 0.5,
            pageSize: labelFile.pageSize
          });
        }

        // 添加处理后的文档
        processedFiles.forEach((file, index) => {
          sequence.push({
            type: 'content',
            file,
            order: index + 1,
            pageRange: 'all'
          });
        });
        break;

      case 'last':
        // 文档在前，标签页在最后
        processedFiles.forEach((file, index) => {
          sequence.push({
            type: 'content',
            file,
            order: index,
            pageRange: 'all'
          });
        });

        // 添加分隔空白页
        if (this.config.labelPageSettings.includeBlankPage) {
          sequence.push({
            type: 'blank',
            file: null,
            order: processedFiles.length - 0.5,
            pageSize: labelFile.pageSize
          });
        }

        sequence.push({
          type: 'label',
          file: labelFile,
          order: processedFiles.length,
          pageRange: 'all'
        });
        break;

      case 'both':
        // 前后都有标签页
        sequence.push({
          type: 'label',
          file: labelFile,
          order: 0,
          pageRange: 'all'
        });

        processedFiles.forEach((file, index) => {
          sequence.push({
            type: 'content',
            file,
            order: index + 1,
            pageRange: 'all'
          });
        });

        sequence.push({
          type: 'label',
          file: labelFile,
          order: processedFiles.length + 1,
          pageRange: 'all'
        });
        break;
    }

    return {
      items: sequence.sort((a, b) => a.order - b.order),
      totalItems: sequence.length,
      estimatedPages: this.calculateTotalPages(sequence)
    };
  }

  // 执行Ghostscript文件合并
  private async performGhostscriptMerge(
    orderId: string,
    sequence: MergeSequence
  ): Promise<string> {
    const outputPath = this.generateMergedFilePath(orderId);
    const tempDir = path.join(this.config.tempPath, `merge_${orderId}_${Date.now()}`);

    try {
      // 创建临时工作目录
      await fs.ensureDir(tempDir);

      // 构建Ghostscript合并命令
      const gsCommand = this.buildGhostscriptMergeCommand(
        sequence,
        outputPath,
        tempDir
      );

      console.log(`执行Ghostscript合并命令: ${gsCommand.join(' ')}`);

      // 执行合并操作
      const result = await this.ghostscriptService.executeCommand(gsCommand, {
        timeout: 300000, // 5分钟超时
        maxBuffer: 50 * 1024 * 1024 // 50MB缓冲区
      });

      if (!result.success) {
        throw new Error(`Ghostscript合并失败: ${result.error}`);
      }

      // 验证输出文件是否生成
      const exists = await fs.pathExists(outputPath);
      if (!exists) {
        throw new Error('合并后的文件未成功生成');
      }

      // 检查文件大小
      const stats = await fs.stat(outputPath);
      if (stats.size === 0) {
        throw new Error('合并后的文件为空');
      }

      console.log(`文件合并成功: ${outputPath} (${stats.size} bytes)`);
      return outputPath;

    } finally {
      // 清理临时目录
      await fs.remove(tempDir).catch(err =>
        console.error('清理临时目录失败:', err)
      );
    }
  }
}
```

这个下载系统设计充分考虑了大文件、高并发的业务需求，通过分片下载、断点续传、智能调度等技术手段，确保下载的稳定性和效率。特别是新增的文件合并阶段，实现了标签页与处理后文档的智能合并，形成了从订单接收到打印执行的完整业务闭环。
```
```
```
```
```

## 🎨 UI设计规范与标准

> **⚠️ 重要提醒**: 在重构现有模块和新建模块时，**必须严格遵循**以下UI设计规范。所有界面组件、色彩搭配、交互效果都应当与此规范保持一致，确保整个应用的视觉统一性和用户体验一致性。

### 🎯 设计系统核心原则

#### 设计理念
- **现代化**: 采用Material Design 3.0设计语言
- **一致性**: 统一的视觉语言和交互模式
- **可访问性**: 符合WCAG 2.1 AA级无障碍标准
- **响应式**: 适配桌面端和移动端设备
- **专业性**: 企业级应用的专业外观

### 🌈 色彩系统规范

#### 主色调定义 (CSS变量)
```css
:root {
    /* 主色调 - 参考modernize配色 */
    --primary-color: #5d87ff;        /* 主蓝色 - 主要操作按钮、链接 */
    --primary-light: #ecf2ff;        /* 浅蓝色 - 选中状态背景 */
    --primary-dark: #4570ea;         /* 深蓝色 - 按钮悬停状态 */
    --secondary-color: #49beff;      /* 辅助蓝色 - 次要元素 */
    --success-color: #67c23a;        /* 成功绿色 - 成功状态、完成标识 */
    --warning-color: #ffae1f;        /* 警告橙色 - 警告信息、待处理状态 */
    --danger-color: #fa896b;         /* 危险色 - 错误信息、删除操作 */
    --info-color: #539bff;           /* 信息蓝色 - 提示信息 */

    /* 文字颜色层级 */
    --text-primary: #2a3547;         /* 主要文字 - 标题、重要内容 */
    --text-regular: #5a6a85;         /* 常规文字 - 正文内容 */
    --text-secondary: #7c8fac;       /* 次要文字 - 辅助信息 */
    --text-placeholder: #adb5bd;     /* 占位文字 - 输入框提示 */

    /* 背景颜色系统 */
    --bg-color: #ffffff;             /* 主背景 - 卡片、弹窗背景 */
    --bg-page: #f5f5f9;              /* 页面背景 - 主内容区背景 */
    --bg-light: #f9f9fd;             /* 浅色背景 - 表头、工具栏 */
    --bg-overlay: rgba(0, 0, 0, 0.8); /* 遮罩背景 - 弹窗遮罩 */

    /* 边框颜色 */
    --border-base: #e5eaef;          /* 基础边框 - 输入框、分割线 */
    --border-light: #f1f5f9;         /* 浅色边框 - 表格边框 */
    --border-lighter: #f8fafc;       /* 最浅边框 - 卡片边框 */

    /* 阴影系统 */
    --shadow-base: 0 1px 4px rgba(0, 0, 0, 0.08);
    --shadow-light: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-hover: 0 4px 20px rgba(93, 135, 255, 0.15);
    --shadow-card: 0 0 2px rgba(145, 158, 171, 0.2), 0 12px 24px -4px rgba(145, 158, 171, 0.12);

    /* 圆角系统 */
    --border-radius-base: 7px;       /* 基础圆角 - 按钮、输入框 */
    --border-radius-small: 4px;      /* 小圆角 - 标签、小按钮 */
    --border-radius-large: 12px;     /* 大圆角 - 卡片、弹窗 */
}
```

#### 色彩使用规范
- **主色调**: 用于主要操作按钮、链接、选中状态
- **成功色**: 用于成功提示、完成状态、确认操作
- **警告色**: 用于警告信息、待处理状态、注意事项
- **危险色**: 用于错误信息、删除操作、危险警告
- **信息色**: 用于一般信息提示、帮助说明

### Material UI组件选择

#### 主要组件
- **AppBar** - 顶部导航栏
- **Drawer** - 侧边导航菜单
- **DataGrid** - 订单列表表格
- **Card** - 信息卡片展示
- **Stepper** - 订单状态步骤
- **Dialog** - 弹窗对话框
- **Snackbar** - 消息提示

### 🔤 字体系统规范

#### 字体族
```css
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
```

#### 字体大小层级
```css
/* 标题字体 */
--font-size-h1: 28px;    /* 页面主标题 */
--font-size-h2: 24px;    /* 区块标题 */
--font-size-h3: 20px;    /* 子标题 */
--font-size-h4: 16px;    /* 小标题 */

/* 正文字体 */
--font-size-base: 14px;  /* 基础字体大小 */
--font-size-small: 12px; /* 小字体 - 辅助信息 */
--font-size-mini: 11px;  /* 最小字体 - 表头、标签 */

/* 字重 */
--font-weight-light: 300;
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

### 📐 间距系统规范

#### 间距单位
```css
/* 基础间距单位 (4px基准) */
--spacing-xs: 4px;    /* 极小间距 */
--spacing-sm: 8px;    /* 小间距 */
--spacing-md: 12px;   /* 中等间距 */
--spacing-lg: 16px;   /* 大间距 */
--spacing-xl: 20px;   /* 超大间距 */
--spacing-xxl: 24px;  /* 最大间距 */
--spacing-xxxl: 32px; /* 区块间距 */
```

#### 组件内边距规范
- **按钮**: 8px 16px (小按钮: 6px 12px)
- **输入框**: 12px 16px
- **卡片**: 24px
- **表格单元格**: 16px 20px
- **弹窗**: 24px

#### 界面布局
```typescript
// 主布局组件
const MainLayout = () => {
  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar position="fixed">
        <Toolbar>
          <Typography variant="h6">云打印终端</Typography>
        </Toolbar>
      </AppBar>

      <Drawer variant="permanent">
        <NavigationMenu />
      </Drawer>

      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        <RouterView />
      </Box>
    </Box>
  );
};
```

### 🧩 组件设计规范

#### 按钮组件规范
```css
/* 主要按钮 */
.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-base);
    padding: 8px 16px;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    box-shadow: var(--shadow-light);
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
}

/* 次要按钮 */
.btn-secondary {
    background: var(--bg-color);
    color: var(--text-regular);
    border: 1px solid var(--border-base);
}

/* 成功按钮 */
.btn-success {
    background: var(--success-color);
    color: white;
}

/* 警告按钮 */
.btn-warning {
    background: var(--warning-color);
    color: white;
}

/* 危险按钮 */
.btn-danger {
    background: var(--danger-color);
    color: white;
}
```

#### 卡片组件规范
```css
.card {
    background: var(--bg-color);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-hover);
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-light);
}

.card-content {
    padding: 24px;
}
```

#### 表格组件规范
```css
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-color);
    border-radius: var(--border-radius-base);
    overflow: hidden;
}

.table th {
    padding: 16px 20px;
    background: var(--bg-light);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    font-size: var(--font-size-mini);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-bottom: 1px solid var(--border-light);
}

.table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-light);
    color: var(--text-regular);
    font-size: 13px;
}

.table tbody tr:hover {
    background: var(--bg-light);
}
```

#### 状态标签规范
```css
.status-chip {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: var(--font-size-small);
    font-weight: var(--font-weight-medium);
    gap: 4px;
}

.status-chip.pending {
    background: rgba(255, 174, 31, 0.1);
    color: var(--warning-color);
}

.status-chip.processing {
    background: rgba(93, 135, 255, 0.1);
    color: var(--primary-color);
}

.status-chip.printing {
    background: rgba(83, 155, 255, 0.1);
    color: var(--info-color);
}

.status-chip.completed {
    background: rgba(19, 222, 185, 0.1);
    color: var(--success-color);
}
```

### 📱 响应式设计规范

#### 断点系统
```css
/* 移动端 */
@media (max-width: 768px) {
    .sidebar { width: 100%; transform: translateX(-100%); }
    .main-content { margin-left: 0; }
    .stats-grid { grid-template-columns: 1fr; }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
    .sidebar { width: 240px; }
    .stats-grid { grid-template-columns: repeat(2, 1fr); }
}

/* 桌面端 */
@media (min-width: 1025px) {
    .sidebar { width: 280px; }
    .stats-grid { grid-template-columns: repeat(4, 1fr); }
}
```

### 🎭 动画与交互规范

#### 基础动画
```css
/* 淡入动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滑入动画 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 悬停效果 */
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}
```

#### 交互反馈
- **按钮点击**: 0.2s缓动动画
- **卡片悬停**: 轻微上移 + 阴影加深
- **页面切换**: 淡入淡出效果
- **数据加载**: 骨架屏 + 进度指示器

### 页面设计规范

#### 1. 订单管理页面
- **列表视图**: 使用DataGrid展示订单列表
- **筛选功能**: 状态、日期、客户筛选
- **批量操作**: 批量打印、批量发货
- **详情面板**: 右侧滑出详情面板

#### 2. 文件处理页面
- **文件列表**: 卡片式展示文件信息
- **预览功能**: 内嵌PDF预览器
- **处理进度**: 进度条显示处理状态
- **操作按钮**: 重新处理、下载等

#### 3. 打印队列页面
- **队列状态**: 实时显示队列状态
- **任务列表**: 表格展示打印任务
- **控制面板**: 暂停、恢复、清空队列
- **统计图表**: 打印量统计图表

#### 4. 物流发货管理页面
- **发货扫描区**: 快递单条码扫描界面
- **待发货列表**: 显示打印完成待发货的订单
- **发货记录**: 已发货订单的物流跟踪信息
- **物流统计**: 发货量统计和物流公司分布
- **扫描设备状态**: 显示扫描设备连接状态

##### 发货扫描界面设计
```typescript
const ShippingScanner = () => {
  return (
    <Card sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        📦 快递单扫描发货
      </Typography>

      {/* 扫描设备状态 */}
      <Box sx={{ mb: 2 }}>
        <Chip
          icon={<BluetoothIcon />}
          label={scannerConnected ? "扫描设备已连接" : "扫描设备未连接"}
          color={scannerConnected ? "success" : "error"}
          variant="outlined"
        />
      </Box>

      {/* 订单选择 */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>选择待发货订单</InputLabel>
        <Select value={selectedOrderId} onChange={handleOrderSelect}>
          {pendingOrders.map(order => (
            <MenuItem key={order.id} value={order.id}>
              {order.orderNo} - {order.userName}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* 扫描输入区 */}
      <TextField
        fullWidth
        label="扫描快递单条码"
        placeholder="请扫描快递单条码或手动输入"
        value={scannedBarcode}
        onChange={handleBarcodeInput}
        onKeyPress={handleScanSubmit}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <QrCodeScannerIcon />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <Button
                variant="contained"
                onClick={handleShipOrder}
                disabled={!selectedOrderId || !scannedBarcode}
              >
                确认发货
              </Button>
            </InputAdornment>
          )
        }}
        sx={{ mb: 2 }}
      />

      {/* 识别结果显示 */}
      {parseResult && (
        <Alert severity={parseResult.isValid ? "success" : "error"} sx={{ mb: 2 }}>
          {parseResult.isValid ? (
            <>
              <strong>识别成功:</strong> {parseResult.companyName} - {parseResult.trackingNumber}
            </>
          ) : (
            <>
              <strong>识别失败:</strong> {parseResult.error}
            </>
          )}
        </Alert>
      )}
    </Card>
  );
};
```

##### 待发货订单列表
```typescript
const PendingShipmentList = () => {
  const columns = [
    { field: 'orderNo', headerName: '订单号', width: 150 },
    { field: 'userName', headerName: '客户姓名', width: 120 },
    { field: 'printCompleteTime', headerName: '打印完成时间', width: 180 },
    { field: 'deliveryAddress', headerName: '收货地址', width: 300 },
    {
      field: 'actions',
      headerName: '操作',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Button
            size="small"
            variant="outlined"
            onClick={() => handleQuickShip(params.row.id)}
            startIcon={<LocalShippingIcon />}
          >
            快速发货
          </Button>
          <IconButton
            size="small"
            onClick={() => handleViewDetails(params.row.id)}
          >
            <VisibilityIcon />
          </IconButton>
        </Box>
      )
    }
  ];

  return (
    <Card sx={{ height: 400 }}>
      <CardHeader title="📋 待发货订单列表" />
      <CardContent>
        <DataGrid
          rows={pendingOrders}
          columns={columns}
          pageSize={10}
          checkboxSelection
          disableSelectionOnClick
          components={{
            Toolbar: GridToolbar
          }}
        />
      </CardContent>
    </Card>
  );
};
```

##### 物流跟踪界面
```typescript
const LogisticsTracking = () => {
  return (
    <Card sx={{ mt: 3 }}>
      <CardHeader title="🚚 物流跟踪信息" />
      <CardContent>
        <Grid container spacing={3}>
          {/* 物流统计卡片 */}
          <Grid item xs={12} md={3}>
            <StatCard
              title="今日发货"
              value={todayShipments}
              icon={<LocalShippingIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <StatCard
              title="运输中"
              value={inTransitCount}
              icon={<FlightIcon />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <StatCard
              title="已签收"
              value={deliveredCount}
              icon={<CheckCircleIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <StatCard
              title="异常件"
              value={exceptionCount}
              icon={<ErrorIcon />}
              color="error"
            />
          </Grid>

          {/* 物流公司分布图表 */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                物流公司分布
              </Typography>
              <PieChart
                data={logisticsCompanyData}
                height={300}
              />
            </Paper>
          </Grid>

          {/* 发货趋势图表 */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                发货趋势
              </Typography>
              <LineChart
                data={shipmentTrendData}
                height={300}
              />
            </Paper>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};
```

#### 5. 系统设置页面
- **分组设置**: 使用Accordion组织设置项
  - 文件管理设置
  - 打印机配置
  - Ghostscript配置
  - 云端同步设置
  - 系统性能设置
- **表单验证**: 实时验证设置参数
- **预览功能**: 设置效果实时预览
- **导入导出**: 配置文件导入导出
- **文件路径管理**: CDN文件下载路径、处理文件保存路径配置

#### 系统设置页面详细设计

##### 智能打印机管理页面
```typescript
interface PrinterManagementPage {
  // 打印机发现区域
  discovery: {
    autoDetectButton: Button;               // 自动检测按钮
    refreshButton: Button;                  // 刷新列表按钮
    manualAddButton: Button;                // 手动添加按钮
    detectionStatus: StatusIndicator;       // 检测状态
  };

  // 打印机列表区域
  printerList: {
    dataGrid: DataGrid<PrinterInfo>;        // 打印机列表
    columns: [
      'name',                               // 打印机名称
      'manufacturer',                       // 制造商
      'model',                              // 型号
      'status',                             // 状态
      'capabilities',                       // 能力标签
      'statistics',                         // 使用统计
      'actions'                             // 操作按钮
    ];
    filters: {
      statusFilter: Select;                 // 状态筛选
      capabilityFilter: MultiSelect;        // 能力筛选
      groupFilter: Select;                  // 分组筛选
    };
  };

  // 打印机详情面板
  detailPanel: {
    basicInfo: PrinterBasicInfo;            // 基本信息
    capabilities: PrinterCapabilities;      // 能力详情
    ghostscriptConfig: GhostscriptConfig;   // GS配置
    statistics: PrinterStatistics;          // 使用统计
    maintenanceInfo: MaintenanceInfo;       // 维护信息
  };

  // 打印机分组管理
  groupManagement: {
    groupList: List<PrinterGroup>;          // 分组列表
    createGroupButton: Button;              // 创建分组
    editGroupDialog: Dialog;                // 编辑分组对话框
    autoAssignRules: AutoAssignRulesEditor; // 自动分配规则编辑器
  };

  // 智能选择配置
  intelligentConfig: {
    enableToggle: Switch;                   // 启用智能选择
    strategySelect: Select;                 // 选择策略
    loadBalancingToggle: Switch;            // 负载均衡
    fallbackToggle: Switch;                 // 回退到手动
  };
}

// 打印机测试对话框
interface PrinterTestDialog {
  printerSelect: Select;                    // 打印机选择
  testOptions: {
    testPageType: RadioGroup;               // 测试页类型
    colorMode: RadioGroup;                  // 颜色模式
    paperSize: Select;                      // 纸张大小
  };
  testButton: Button;                       // 开始测试
  resultDisplay: TestResultDisplay;         // 测试结果显示
}
```

##### 文件管理设置组
```typescript
interface FileManagementSettings {
  // CDN下载设置
  cdnSettings: {
    downloadPath: string;               // 下载目录路径
    maxConcurrentDownloads: number;     // 最大并发下载数
    downloadTimeout: number;            // 下载超时时间
    autoRetry: boolean;                 // 自动重试
    retryAttempts: number;              // 重试次数
  };

  // 文件处理路径
  processingPaths: {
    inputDirectory: string;             // 输入文件目录
    outputDirectory: string;            // 输出文件目录
    tempDirectory: string;              // 临时文件目录
    archiveDirectory: string;           // 归档目录
  };

  // 自动清理设置
  autoCleanup: {
    enabled: boolean;                   // 启用自动清理
    tempFileRetention: number;          // 临时文件保留天数
    archiveRetention: number;           // 归档文件保留天数
    logRetention: number;               // 日志保留天数
  };

  // 存储管理
  storageManagement: {
    maxStorageSize: number;             // 最大存储大小(GB)
    warningThreshold: number;           // 警告阈值(%)
    compressionEnabled: boolean;        // 启用压缩
    encryptionEnabled: boolean;         // 启用加密
  };
}
```

##### 智能打印机配置组
```typescript
interface PrinterSettings {
  // 默认打印机
  defaultPrinter: string;

  // 打印机发现和管理
  printerDiscovery: {
    autoDetect: boolean;                    // 自动检测新打印机
    refreshInterval: number;                // 刷新间隔(秒)
    enableCapabilityProbing: boolean;       // 启用能力探测
  };

  // 智能选择配置
  intelligentSelection: {
    enabled: boolean;                       // 启用智能选择
    selectionStrategy: 'load_balance' | 'capability_match' | 'hybrid';
    fallbackToManual: boolean;              // 失败时回退到手动选择
  };

  // 打印机分组配置
  printerGroups: Array<{
    name: string;                           // 分组名称
    description: string;                    // 分组描述
    printers: string[];                     // 打印机名称列表
    autoAssignRules: {                      // 自动分配规则
      paperSize?: string[];                 // 纸张大小
      colorMode?: 'mono' | 'color';         // 颜色模式
      bindingType?: string[];               // 装订类型
      documentType?: string[];              // 文档类型
    };
  }>;

  // 打印机列表（增强版）
  printers: Array<{
    name: string;
    displayName: string;
    manufacturer: string;
    model: string;
    type: 'laser' | 'inkjet' | 'thermal' | 'wide_format';

    // 物理能力
    capabilities: {
      colorSupport: 'mono' | 'color';
      duplexSupport: boolean;
      maxPaperSize: string;
      supportedPaperSizes: string[];
      maxResolution: number;
      supportedResolutions: number[];
      bindingSupport: string[];
      specialFeatures: string[];
    };

    // Ghostscript配置
    ghostscriptConfig: {
      device: string;
      options: string[];
      iccProfile?: string;
    };

    // 使用统计
    statistics: {
      totalJobs: number;
      successfulJobs: number;
      failedJobs: number;
      averageJobTime: number;
      lastUsed: number;
    };

    // 默认设置
    defaultSettings: {
      quality: string;
      paperSize: string;
      colorMode: string;
      copies: number;
      duplex: boolean;
    };

    // 维护信息
    maintenance: {
      lastMaintenance: number;
      nextMaintenance: number;
      maintenanceAlerts: boolean;
    };
  }>;

  // 智能队列设置
  queueSettings: {
    maxConcurrentJobs: number;          // 最大并发打印任务
    retryFailedJobs: boolean;           // 重试失败任务
    jobTimeout: number;                 // 任务超时时间
    priorityLevels: number;             // 优先级层次
    loadBalancing: boolean;             // 负载均衡

    // 智能调度
    scheduling: {
      algorithm: 'fifo' | 'priority' | 'shortest_job_first' | 'intelligent';
      considerPrinterLoad: boolean;
      considerJobComplexity: boolean;
    };
  };

  // 打印机选择界面配置
  selectionUI: {
    showCapabilities: boolean;              // 显示打印机能力
    showStatistics: boolean;                // 显示使用统计
    showRecommendations: boolean;           // 显示推荐理由
    groupByCapability: boolean;             // 按能力分组显示
    enableQuickSelect: boolean;             // 启用快速选择
  };
}
```

##### Ghostscript配置组
```typescript
interface GhostscriptSettings {
  // 安装路径
  installPath: string;

  // 性能设置
  performance: {
    maxMemory: number;                  // 最大内存使用(MB)
    bufferSize: number;                 // 缓冲区大小(MB)
    enableMultiThreading: boolean;      // 启用多线程
    threadCount: number;                // 线程数量
  };

  // 字体设置
  fontSettings: {
    fontPath: string;                   // 字体路径
    enableCJKSupport: boolean;          // 启用CJK支持
    defaultCJKFont: string;             // 默认CJK字体
    fontSubstitution: boolean;          // 字体替换
  };

  // 颜色管理
  colorManagement: {
    defaultICCProfile: string;          // 默认ICC配置文件
    enableColorManagement: boolean;     // 启用颜色管理
    renderingIntent: string;            // 渲染意图
  };
}
```

##### 云端同步设置组
```typescript
interface SyncSettings {
  // 同步频率
  syncInterval: number;                 // 同步间隔(分钟)

  // 网络设置
  networkSettings: {
    timeout: number;                    // 网络超时
    retryAttempts: number;              // 重试次数
    useProxy: boolean;                  // 使用代理
    proxySettings?: {
      host: string;
      port: number;
      username?: string;
      password?: string;
    };
  };

  // 数据同步策略
  syncStrategy: {
    batchSize: number;                  // 批量大小
    prioritySync: boolean;              // 优先级同步
    conflictResolution: 'local' | 'remote' | 'manual'; // 冲突解决策略
  };
}
```

##### 系统性能设置组
```typescript
interface PerformanceSettings {
  // 系统资源
  systemResources: {
    maxCPUUsage: number;                // 最大CPU使用率(%)
    maxMemoryUsage: number;             // 最大内存使用率(%)
    diskSpaceWarning: number;           // 磁盘空间警告阈值(%)
  };

  // 日志设置
  logging: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    maxLogFileSize: number;             // 最大日志文件大小(MB)
    logRotation: boolean;               // 日志轮转
    retentionDays: number;              // 日志保留天数
  };

  // 监控设置
  monitoring: {
    enablePerformanceMonitoring: boolean; // 启用性能监控
    metricsInterval: number;            // 指标收集间隔(秒)
    alertThresholds: {
      cpuUsage: number;
      memoryUsage: number;
      diskUsage: number;
      errorRate: number;
    };
  };
}
```

## 📋 数据结构与字段规范

> **⚠️ 重要**: 以下数据结构基于小程序端的实际业务需求分析，在重构和新建模块时必须严格遵循这些字段定义，确保数据一致性和业务逻辑完整性。

### 🗂️ 订单数据结构

#### 核心订单信息 (Order)
```typescript
interface Order {
  // 基础标识信息
  id: string;                    // 订单唯一标识
  orderNo: string;               // 订单号 (格式: P2024073001)
  createTime: number;            // 创建时间戳
  status: string;                // 订单状态

  // 客户信息
  customerInfo: {
    name: string;                // 客户姓名
    phone: string;               // 客户电话
    openid?: string;             // 微信用户标识
  };

  // 文件信息
  files: Array<{
    fileID: string;              // 文件云存储ID
    fileDBID: string;            // 文件数据库ID
    fileName: string;            // 文件名称
    fileType: string;            // 文件类型 (pdf/word/excel/ppt)
    pageCount: number;           // 页数
    fileSize?: string;           // 文件大小
    uploadTime?: string;         // 上传时间
    convertedFileID?: string;    // 转换后文件ID
    convertedFileName?: string;  // 转换后文件名
    hasConverted?: boolean;      // 是否已转换
  }>;

  // 打印配置
  printConfig: PrintConfiguration;

  // 价格信息
  pricing: {
    printPrice: string;          // 打印费用
    bindingPrice: string;        // 装订费用
    deliveryPrice: string;       // 配送费用
    discount: string;            // 优惠金额
    totalPrice: string;          // 总价
  };

  // 配送信息
  delivery: {
    type: 'express' | 'self';    // 配送方式
    address?: AddressInfo;       // 收货地址 (快递配送时)
    storeInfo?: StoreInfo;       // 门店信息 (自取时)
  };

  // 物流信息
  logistics?: {
    company: string;             // 快递公司
    trackingNo: string;          // 快递单号
    status?: string;             // 物流状态
  };

  // 备注信息
  remark?: string;               // 客户备注

  // 打印码 (线下打印时使用)
  printCode?: string;
}
```

#### 打印配置结构 (PrintConfiguration)
```typescript
interface PrintConfiguration {
  // 基础打印设置
  copies: number;                // 打印份数
  color: '黑白' | '经济彩色' | '标准彩色';  // 颜色模式
  side: '单面打印' | '双面打印';    // 打印面数
  paperType: 'A4' | 'A3' | 'B5' | 'photo';  // 纸张类型
  paperQuality: 'normal' | 'eyeCare' | 'premium';  // 纸张质量

  // 装订设置
  binding: '不装订' | '订书钉' | '骑马订' | '胶装' | '铁圈装';

  // 缩印设置
  shrinkMode: '不缩印' | '缩印';
  shrinkType?: '二合一' | '四合一';  // 缩印时有效

  // 封面设置 (装订时有效)
  cover?: {
    paperType: '皮纹纸' | '铜版纸';
    color?: 'lightblue' | 'lightgreen' | 'lightred' | 'lightyellow' | 'beige';  // 皮纹纸颜色
    contentType: 'pure' | 'text' | 'firstPage' | 'firstPageWithBack' | 'firstLastPage' | 'upload';
    title?: string;              // 封面标题 (文字封面时)
    subtitle?: string;           // 封面副标题 (文字封面时)
    file?: string;               // 上传的封面文件 (上传封面时)
  };
}
```

#### 地址信息结构 (AddressInfo)
```typescript
interface AddressInfo {
  id: string;                    // 地址ID
  name: string;                  // 收件人姓名
  phone: string;                 // 收件人电话
  address: string;               // 详细地址
  isDefault: boolean;            // 是否默认地址
  region?: {                     // 地区信息
    province: string;
    city: string;
    district: string;
  };
}
```

#### 门店信息结构 (StoreInfo)
```typescript
interface StoreInfo {
  id: string;                    // 门店ID
  name: string;                  // 门店名称
  address: string;               // 门店地址
  phone: string;                 // 联系电话
  businessHours: string;         // 营业时间
  status: '营业中' | '休息中';    // 营业状态
  location?: {                   // 地理位置
    latitude: number;
    longitude: number;
  };
}
```

### 📊 订单状态管理

#### 状态枚举定义
```typescript
enum OrderStatus {
  PENDING_PAYMENT = 'pending_payment',      // 待付款
  PENDING_PROCESS = 'pending_process',      // 待处理
  DOWNLOADING = 'downloading',              // 文件下载中
  DOWNLOAD_FAILED = 'download_failed',      // 下载失败
  PROCESSING = 'processing',                // 处理中
  PENDING_PRINT = 'pending_print',          // 待打印
  PRINTING = 'printing',                    // 打印中
  PRINT_COMPLETED = 'print_completed',      // 打印完成
  PENDING_BINDING = 'pending_binding',      // 待装订
  BINDING = 'binding',                      // 装订中
  PENDING_DELIVERY = 'pending_delivery',    // 待发货
  SHIPPED = 'shipped',                      // 已发货
  IN_TRANSIT = 'in_transit',                // 运输中
  DELIVERED = 'delivered',                  // 已送达
  COMPLETED = 'completed',                  // 已完成
  CANCELLED = 'cancelled',                  // 已取消
  REFUNDED = 'refunded'                     // 已退款
}
```

#### 状态显示配置
```typescript
const StatusConfig = {
  [OrderStatus.PENDING_PAYMENT]: {
    label: '待付款',
    color: '#ff9500',
    icon: 'wallet',
    description: '等待客户付款'
  },
  [OrderStatus.PENDING_PROCESS]: {
    label: '待处理',
    color: '#ffae1f',
    icon: 'time',
    description: '订单已付款，等待处理'
  },
  [OrderStatus.DOWNLOADING]: {
    label: '文件下载中',
    color: '#5d87ff',
    icon: 'download',
    description: '正在下载订单文件'
  },
  [OrderStatus.DOWNLOAD_FAILED]: {
    label: '下载失败',
    color: '#fa896b',
    icon: 'error',
    description: '文件下载失败，需要重试'
  },
  [OrderStatus.PROCESSING]: {
    label: '处理中',
    color: '#5d87ff',
    icon: 'loading',
    description: '正在处理文件'
  },
  [OrderStatus.PENDING_PRINT]: {
    label: '待打印',
    color: '#539bff',
    icon: 'print',
    description: '文件处理完成，等待打印'
  },
  [OrderStatus.PRINTING]: {
    label: '打印中',
    color: '#49beff',
    icon: 'printer',
    description: '正在打印中'
  },
  [OrderStatus.PRINT_COMPLETED]: {
    label: '打印完成',
    color: '#13deb9',
    icon: 'check',
    description: '打印完成，准备发货'
  },
  [OrderStatus.PENDING_DELIVERY]: {
    label: '待发货',
    color: '#13deb9',
    icon: 'package',
    description: '打印完成，等待发货'
  },
  [OrderStatus.SHIPPED]: {
    label: '已发货',
    color: '#67c23a',
    icon: 'truck',
    description: '已发货，运输中'
  },
  [OrderStatus.IN_TRANSIT]: {
    label: '运输中',
    color: '#67c23a',
    icon: 'truck-moving',
    description: '包裹正在运输途中'
  },
  [OrderStatus.DELIVERED]: {
    label: '已送达',
    color: '#13deb9',
    icon: 'check-circle',
    description: '包裹已送达客户'
  },
  [OrderStatus.COMPLETED]: {
    label: '已完成',
    color: '#13deb9',
    icon: 'check-circle',
    description: '订单已完成'
  },
  [OrderStatus.CANCELLED]: {
    label: '已取消',
    color: '#fa896b',
    icon: 'close-circle',
    description: '订单已取消'
  }
};
```

### 🏷️ 标签页数据结构

#### 标签生成配置 (LabelConfiguration)
```typescript
interface LabelConfiguration {
  // 全局标签设置
  global: {
    showQRCode: boolean;              // 显示二维码
    showBarcode: boolean;             // 显示条形码
    showOrderInfo: boolean;           // 显示订单信息
    showShippingInfo: boolean;        // 显示收货信息
    showFileList: boolean;            // 显示文件列表
    showProductionInstructions: boolean; // 显示生产说明

    // 页面设置
    pageSize: 'A4' | 'A5' | 'A6';     // 标签页面大小
    margins: {
      top: number;                    // 上边距 (mm)
      right: number;                  // 右边距 (mm)
      bottom: number;                 // 下边距 (mm)
      left: number;                   // 左边距 (mm)
    };

    // 字体设置
    fontSize: {
      title: number;                  // 标题字体大小
      content: number;                // 内容字体大小
      small: number;                  // 小字体大小
    };
  };

  // 订单级别设置
  order: {
    template: 'standard' | 'compact' | 'detailed'; // 标签模板
    includeQRCode: boolean;           // 包含二维码
    includeBarcode: boolean;          // 包含条形码
    fontSize: number;                 // 字体大小
  };
}
```

#### 标签内容数据 (LabelData)
```typescript
interface LabelData {
  // 订单基础信息
  orderInfo: {
    orderNo: string;                  // 订单号
    createTime: string;               // 创建时间
    printCode?: string;               // 打印码
    qrCodeData: string;               // 二维码数据
    barcodeData: string;              // 条形码数据
  };

  // 客户信息
  customerInfo: {
    name: string;                     // 客户姓名
    phone: string;                    // 客户电话
  };

  // 收货信息
  shippingInfo?: {
    recipientName: string;            // 收件人姓名
    recipientPhone: string;           // 收件人电话
    address: string;                  // 收货地址
    deliveryType: string;             // 配送方式
  };

  // 文件信息
  fileList: Array<{
    fileName: string;                 // 文件名
    pageCount: number;                // 页数
    fileType: string;                 // 文件类型
  }>;

  // 打印配置摘要
  printSummary: {
    totalPages: number;               // 总页数
    copies: number;                   // 份数
    color: string;                    // 颜色模式
    side: string;                     // 打印面数
    paperType: string;                // 纸张类型
    binding: string;                  // 装订方式
  };

  // 生产说明
  productionInstructions: {
    specialRequirements?: string;     // 特殊要求
    remark?: string;                  // 备注信息
    urgentLevel?: 'normal' | 'urgent' | 'express'; // 紧急程度
  };

  // 价格信息
  pricing: {
    totalPrice: string;               // 总价
    breakdown?: {                     // 价格明细
      printPrice: string;
      bindingPrice: string;
      deliveryPrice: string;
      discount: string;
    };
  };
}
```

### 📋 订单处理完整流程

#### 🎯 核心处理流程
> **重要**: 订单管理从本地数据库读取订单后，需要按照以下顺序进行完整的处理流程，确保打印质量和客户需求的精确匹配。

```mermaid
graph TD
    A[从本地数据库读取订单] --> B[生成订单标签页]
    B --> C[分析打印配置]
    C --> D[Ghostscript文档处理]
    D --> E[颜色空间转换]
    E --> F[装订方式处理]
    F --> G[文档压缩优化]
    G --> H[生成最终打印文件]
    H --> I[加入打印队列]

    B --> B1[单面打印: 生成1页标签]
    B --> B2[双面打印: 生成标签页+空白页]

    D --> D1[PDF格式转换]
    D --> D2[分辨率优化]
    D --> D3[字体嵌入处理]

    F --> F1[骑马订: A4→A3拼合]
    F --> F2[胶装: 页面重排]
    F --> F3[铁圈装: 边距调整]
```

#### 1. 标签页生成 (LabelGenerationService)

##### 标签页生成规则
```typescript
interface LabelGenerationRules {
  // 基础规则
  singleSided: {
    labelPages: 1;              // 单面打印生成1页标签
    blankPages: 0;              // 无空白页
  };

  doubleSided: {
    labelPages: 1;              // 双面打印生成1页标签
    blankPages: 1;              // 加1页空白页
    arrangement: 'label-blank'; // 标签页在前，空白页在后
  };

  // 特殊装订要求
  bindingRequirements: {
    saddle: {                   // 骑马订
      minPages: 4;              // 最少4页（1张对折）
      pageMultiple: 4;          // 页数必须是4的倍数
      centerBinding: true;      // 中心装订
    };

    perfect: {                  // 胶装
      minPages: 8;              // 最少8页
      spineMargin: 5;           // 书脊边距(mm)
      coverPage: true;          // 需要封面
    };

    spiral: {                   // 铁圈装
      leftMargin: 15;           // 左边距(mm)
      holeSpacing: 6;           // 孔距(mm)
      reinforcement: true;      // 加强边缘
    };
  };
}
```

##### 标签页内容生成
```typescript
class LabelGenerationService {
  async generateOrderLabel(order: Order): Promise<LabelDocument> {
    const config = order.printConfig;

    // 1. 确定标签页数量
    const labelPageCount = this.calculateLabelPages(config);

    // 2. 生成标签内容
    const labelContent = await this.createLabelContent(order);

    // 3. 应用装订要求
    const finalDocument = await this.applyBindingRequirements(
      labelContent,
      config.binding,
      labelPageCount
    );

    return finalDocument;
  }

  private calculateLabelPages(config: PrintConfiguration): number {
    if (config.side === '单面打印') {
      return 1; // 只生成1页标签
    } else {
      return 2; // 生成标签页 + 空白页
    }
  }

  private async createLabelContent(order: Order): Promise<LabelContent> {
    return {
      // 订单基础信息
      orderInfo: {
        orderNo: order.orderNo,
        createTime: formatDate(order.createTime),
        qrCode: this.generateQRCode(order.id),
        barcode: this.generateBarcode(order.orderNo)
      },

      // 客户信息
      customerInfo: {
        name: order.customerInfo.name,
        phone: order.customerInfo.phone
      },

      // 文件列表
      fileList: order.files.map(file => ({
        fileName: file.fileName,
        pageCount: file.pageCount,
        fileType: file.fileType
      })),

      // 打印配置摘要
      printSummary: {
        totalPages: order.files.reduce((sum, file) => sum + file.pageCount, 0),
        copies: order.printConfig.copies,
        color: order.printConfig.color,
        side: order.printConfig.side,
        paperType: order.printConfig.paperType,
        binding: order.printConfig.binding
      },

      // 收货信息
      shippingInfo: order.delivery.type === 'express' ? {
        recipientName: order.delivery.address?.name,
        recipientPhone: order.delivery.address?.phone,
        address: order.delivery.address?.address
      } : null,

      // 特殊要求
      specialRequirements: order.remark || '无特殊要求'
    };
  }
}
```

### 🖨️ 打印队列数据结构

#### 2. Ghostscript文档处理 (GhostscriptProcessingService)

##### 核心处理功能
```typescript
class GhostscriptProcessingService {
  async processOrderDocuments(order: Order): Promise<ProcessedDocument[]> {
    const results: ProcessedDocument[] = [];

    for (const file of order.files) {
      // 1. 基础文档转换
      const convertedDoc = await this.convertDocument(file, order.printConfig);

      // 2. 颜色空间处理
      const colorProcessedDoc = await this.processColorSpace(convertedDoc, order.printConfig);

      // 3. 装订方式处理
      const bindingProcessedDoc = await this.processBinding(colorProcessedDoc, order.printConfig);

      // 4. 压缩优化
      const optimizedDoc = await this.optimizeForPrint(bindingProcessedDoc);

      results.push(optimizedDoc);
    }

    return results;
  }

  // 文档格式转换和基础处理
  private async convertDocument(file: OrderFile, config: PrintConfiguration): Promise<ProcessedDocument> {
    const gsOptions = this.buildGhostscriptOptions(file, config);

    return await this.ghostscript.process({
      inputFile: file.filePath,
      outputFile: `processed_${file.fileDBID}.pdf`,
      device: 'pdfwrite',
      options: gsOptions
    });
  }

  // 颜色空间转换
  private async processColorSpace(doc: ProcessedDocument, config: PrintConfiguration): Promise<ProcessedDocument> {
    const colorOptions = {
      // 根据打印配置选择颜色处理
      colorMode: config.color === '黑白' ? 'grayscale' : 'color',

      // ICC颜色管理
      iccProfiles: {
        source: 'sRGB.icc',
        destination: this.getDeviceProfile(config.paperType)
      },

      // 颜色转换参数
      renderingIntent: config.color === '标准彩色' ? 'perceptual' : 'relative_colorimetric',

      // 专色处理
      spotColorHandling: config.color === '标准彩色' ? 'preserve' : 'convert'
    };

    return await this.ghostscript.processColor(doc, colorOptions);
  }

  // 装订方式处理
  private async processBinding(doc: ProcessedDocument, config: PrintConfiguration): Promise<ProcessedDocument> {
    switch (config.binding) {
      case '骑马订':
        return await this.processSaddleStitching(doc);
      case '胶装':
        return await this.processPerfectBinding(doc);
      case '铁圈装':
        return await this.processSpiralBinding(doc);
      default:
        return doc; // 不装订，直接返回
    }
  }

  // 骑马订处理 - A4拼合成A3
  private async processSaddleStitching(doc: ProcessedDocument): Promise<ProcessedDocument> {
    // 骑马订需要将A4页面拼合成A3，实现中心装订
    const impositionOptions = {
      // 页面拼合设置
      sourcePageSize: 'A4',
      targetPageSize: 'A3',

      // 骑马订特殊要求
      imposition: 'saddle_stitch',
      centerFold: true,

      // 页面排列 (确保装订后页码正确)
      pageArrangement: 'booklet',

      // 边距调整
      margins: {
        top: 10,    // mm
        bottom: 10,
        left: 15,   // 装订边
        right: 10
      },

      // 确保页数是4的倍数
      padBlankPages: true
    };

    return await this.ghostscript.processImposition(doc, impositionOptions);
  }

  // 胶装处理
  private async processPerfectBinding(doc: ProcessedDocument): Promise<ProcessedDocument> {
    const bindingOptions = {
      // 胶装边距设置
      spineMargin: 5,     // 书脊边距
      trimMargin: 3,      // 裁切边距

      // 页面重排
      pageOrder: 'sequential',

      // 封面处理
      addCover: true,
      coverTemplate: 'standard'
    };

    return await this.ghostscript.processBinding(doc, bindingOptions);
  }

  // 铁圈装处理
  private async processSpiralBinding(doc: ProcessedDocument): Promise<ProcessedDocument> {
    const spiralOptions = {
      // 打孔边距
      leftMargin: 15,     // 左边距增加用于打孔

      // 孔位标记
      addHoleMarks: true,
      holeSpacing: 6,     // 孔距
      holeDiameter: 4,    // 孔径

      // 边缘加强
      reinforcement: true
    };

    return await this.ghostscript.processSpiral(doc, spiralOptions);
  }
}
```

#### 3. 文档压缩优化 (DocumentOptimizationService)

##### 数字打印优化
```typescript
class DocumentOptimizationService {
  async optimizeForDigitalPrint(doc: ProcessedDocument, config: PrintConfiguration): Promise<OptimizedDocument> {
    // 1. 分辨率优化
    const resolutionOptimized = await this.optimizeResolution(doc, config);

    // 2. 压缩优化
    const compressionOptimized = await this.optimizeCompression(resolutionOptimized, config);

    // 3. 字体优化
    const fontOptimized = await this.optimizeFonts(compressionOptimized);

    // 4. 文件大小控制
    const sizeOptimized = await this.controlFileSize(fontOptimized);

    return sizeOptimized;
  }

  private async optimizeResolution(doc: ProcessedDocument, config: PrintConfiguration): Promise<ProcessedDocument> {
    // 根据纸张类型和打印质量确定最佳分辨率
    const targetDPI = this.getOptimalDPI(config);

    const resolutionOptions = {
      imageDPI: targetDPI,
      vectorDPI: targetDPI,

      // 图像处理
      imageDownsampling: config.paperQuality === 'normal' ? 'bicubic' : 'none',
      imageCompression: config.color === '黑白' ? 'ccitt' : 'jpeg',

      // 文本处理
      textAntialiasing: config.paperQuality === 'premium',
      subpixelRendering: true
    };

    return await this.ghostscript.processResolution(doc, resolutionOptions);
  }

  private getOptimalDPI(config: PrintConfiguration): number {
    // 根据配置确定最佳DPI
    const dpiMatrix = {
      'A4': {
        'normal': 300,
        'eyeCare': 600,
        'premium': 1200
      },
      'A3': {
        'normal': 300,
        'eyeCare': 600,
        'premium': 1200
      },
      'photo': {
        'normal': 600,
        'eyeCare': 1200,
        'premium': 2400
      }
    };

    return dpiMatrix[config.paperType]?.[config.paperQuality] || 300;
  }
}
```

#### 打印任务 (PrintJob)
```typescript
interface PrintJob {
  // 基础信息
  id: string;                       // 任务ID
  orderId: string;                  // 关联订单ID
  printerName: string;              // 打印机名称
  documentName: string;             // 文档名称

  // 任务状态
  status: 'pending' | 'processing' | 'printing' | 'completed' | 'failed' | 'cancelled';

  // 处理阶段
  processingStage: 'label_generation' | 'document_processing' | 'color_conversion' | 'binding_processing' | 'optimization' | 'ready_to_print';

  // 打印参数 (基于订单配置生成)
  printSettings: {
    pages: number;                  // 总页数 (包含标签页)
    copies: number;                 // 份数
    colorMode: 'color' | 'grayscale' | 'monochrome';
    paperSize: string;              // 纸张大小
    orientation: 'portrait' | 'landscape'; // 方向
    duplex: 'none' | 'long' | 'short';     // 双面打印
    quality: 'draft' | 'normal' | 'high';  // 打印质量
    binding: string;                // 装订方式
  };

  // 处理结果
  processedFiles: Array<{
    type: 'label' | 'document';     // 文件类型
    filePath: string;               // 处理后文件路径
    originalFile?: string;          // 原始文件路径
    processingLog: string[];        // 处理日志
  }>;

  // 时间信息
  createdAt: Date;                  // 创建时间
  startedAt?: Date;                 // 开始时间
  completedAt?: Date;               // 完成时间

  // 错误信息
  errorMessage?: string;            // 错误消息
  retryCount?: number;              // 重试次数
}
```

#### 4. 完整处理流程服务 (OrderProcessingService)

##### 订单处理主流程
```typescript
class OrderProcessingService {
  constructor(
    private labelService: LabelGenerationService,
    private ghostscriptService: GhostscriptProcessingService,
    private optimizationService: DocumentOptimizationService,
    private printQueueService: PrintQueueService,
    private logisticsService: LogisticsService,
    private orderService: OrderService
  ) {}

  async processOrder(orderId: string): Promise<ProcessingResult> {
    try {
      // 1. 从本地数据库读取订单
      const order = await this.orderService.getOrderDetails(orderId);

      // 2. 生成订单标签页
      const labelDocument = await this.labelService.generateOrderLabel(order);

      // 3. 处理用户上传的文档
      const processedDocuments = await this.ghostscriptService.processOrderDocuments(order);

      // 4. 优化所有文档
      const optimizedDocuments = await Promise.all(
        processedDocuments.map(doc =>
          this.optimizationService.optimizeForDigitalPrint(doc, order.printConfig)
        )
      );

      // 5. 合并标签页和文档
      const finalDocument = await this.mergeDocuments(labelDocument, optimizedDocuments, order.printConfig);

      // 6. 创建打印任务
      const printJob = await this.createPrintJob(order, finalDocument);

      // 7. 加入打印队列
      await this.printQueueService.addJob(printJob);

      // 8. 更新订单状态
      await this.orderService.updateOrderStatus(orderId, OrderStatus.PENDING_PRINT);

      return {
        success: true,
        orderId,
        printJobId: printJob.id,
        processedFiles: finalDocument.files,
        message: '订单处理完成，已加入打印队列'
      };

    } catch (error) {
      // 错误处理
      await this.orderService.updateOrderStatus(orderId, OrderStatus.PROCESSING, `处理失败: ${error.message}`);

      return {
        success: false,
        orderId,
        error: error.message,
        message: '订单处理失败'
      };
    }
  }

  private async mergeDocuments(
    labelDoc: LabelDocument,
    processedDocs: OptimizedDocument[],
    config: PrintConfiguration
  ): Promise<FinalDocument> {
    // 根据打印配置合并文档
    const mergeOptions = {
      // 标签页位置
      labelPosition: 'first',  // 标签页在最前面

      // 页面顺序
      pageOrder: this.calculatePageOrder(config),

      // 装订要求
      bindingAdjustments: this.getBindingAdjustments(config.binding)
    };

    return await this.ghostscriptService.mergeDocuments([labelDoc, ...processedDocs], mergeOptions);
  }

  private calculatePageOrder(config: PrintConfiguration): string[] {
    const order = ['label']; // 标签页始终在第一页

    if (config.side === '双面打印') {
      order.push('label_blank'); // 双面打印时添加空白页
    }

    order.push('documents'); // 然后是用户文档

    return order;
  }
}
```

#### 智能打印机接口定义

##### 订单分析接口 (OrderAnalysis)
```typescript
interface OrderAnalysis {
  paperSize: string;                        // 纸张大小
  requiresColor: boolean;                   // 需要彩色打印
  requiresDuplex: boolean;                  // 需要双面打印
  bindingType?: string;                     // 装订类型
  hasPhotos: boolean;                       // 包含照片
  hasSpotColors: boolean;                   // 包含专色
  spotColors: string[];                     // 专色列表
  isTextHeavy: boolean;                     // 文本密集
  requiresColorManagement: boolean;         // 需要颜色管理
  quality: 'draft' | 'standard' | 'high';  // 质量要求
  documentType: 'text' | 'photo' | 'mixed'; // 文档类型
  estimatedPages: number;                   // 预估页数
  fileSize: number;                         // 文件大小
}

// 打印机选择结果
interface PrinterSelection {
  printer: PrinterCapabilities;            // 选中的打印机
  reason: string;                           // 选择原因
  confidence: number;                       // 置信度 (0-1)
  alternatives: PrinterCapabilities[];     // 备选打印机
}

// 打印机信息（用于UI显示）
interface PrinterInfo {
  name: string;
  displayName: string;
  manufacturer: string;
  model: string;
  isOnline: boolean;
  currentJobs: number;
  capabilities: {
    colorSupport: 'mono' | 'color';
    duplexSupport: boolean;
    paperSizes: string[];
    bindingSupport: string[];
    maxResolution: number;
  };
  suitableFor: string[];                    // 适用性标签
}

// 打印机统计信息
interface PrinterStatistics {
  totalJobs: number;                        // 总任务数
  successfulJobs: number;                   // 成功任务数
  failedJobs: number;                       // 失败任务数
  averageJobTime: number;                   // 平均任务时间
  lastUsed: number;                         // 最后使用时间
  uptime: number;                           // 在线时间
  maintenanceAlerts: number;                // 维护警告数
}

// 打印机分组
interface PrinterGroup {
  name: string;                             // 分组名称
  description: string;                      // 分组描述
  printers: string[];                       // 打印机名称列表
  autoAssignRules: {                        // 自动分配规则
    paperSize?: string[];
    colorMode?: 'mono' | 'color';
    bindingType?: string[];
    documentType?: string[];
  };
  priority: number;                         // 分组优先级
}
```

#### 打印机配置 (PrinterConfiguration)
```typescript
interface PrinterConfiguration {
  // 打印机信息
  name: string;                     // 打印机名称
  displayName: string;              // 显示名称
  isDefault: boolean;               // 是否默认打印机
  isOnline: boolean;                // 是否在线

  // Ghostscript设备支持
  ghostscriptDevice: string;        // GS设备名称 (如: 'pdfwrite', 'ps2write')

  // 支持的功能
  capabilities: {
    supportColor: boolean;          // 支持彩色
    supportDuplex: boolean;         // 支持双面
    supportedPaperSizes: string[];  // 支持的纸张大小
    supportedQualities: string[];   // 支持的打印质量
    maxCopies: number;              // 最大份数
    supportedBindings: string[];    // 支持的装订方式
    maxPageSize: string;            // 最大页面尺寸
  };

  // ICC颜色配置文件
  colorProfiles: {
    sRGB: string;                   // sRGB配置文件路径
    CMYK: string;                   // CMYK配置文件路径
    deviceProfile: string;          // 设备配置文件路径
  };

  // 默认设置
  defaultSettings: {
    paperSize: string;              // 默认纸张大小
    quality: string;                // 默认质量
    colorMode: string;              // 默认颜色模式
    duplex: string;                 // 默认双面设置
    resolution: number;             // 默认分辨率(DPI)
  };

  // 状态信息
  status: {
    state: 'idle' | 'printing' | 'error' | 'offline';
    queueLength: number;            // 队列长度
    currentJob?: string;            // 当前任务
    lastError?: string;             // 最后错误
    inkLevels?: {                   // 墨水余量
      cyan: number;
      magenta: number;
      yellow: number;
      black: number;
    };
  };
}
```

### ⚠️ 开发规范要求

#### 强制性要求
1. **所有新建组件必须使用上述CSS变量系统**
2. **禁止硬编码颜色值，必须使用预定义的CSS变量**
3. **所有按钮、卡片、表格必须遵循统一的样式规范**
4. **响应式设计必须支持移动端、平板端、桌面端三种布局**
5. **动画效果必须使用统一的缓动函数和时长**
6. **数据结构必须严格按照上述接口定义实现**
7. **订单状态流转必须遵循预定义的状态机**

#### 数据一致性要求
1. **订单数据结构必须与小程序端保持完全一致**
2. **所有枚举值必须使用预定义的常量**
3. **时间格式统一使用ISO 8601标准**
4. **价格字段统一使用字符串类型，保留两位小数**
5. **文件大小统一使用字节为单位，显示时转换**

#### 代码审查检查点
- [ ] 是否使用了CSS变量而非硬编码颜色
- [ ] 组件样式是否符合设计规范
- [ ] 响应式布局是否正确实现
- [ ] 动画效果是否流畅自然
- [ ] 无障碍性是否符合标准
- [ ] 数据结构是否符合接口定义
- [ ] 状态管理是否正确实现
- [ ] 错误处理是否完善

#### 设计一致性验证
- [ ] 色彩搭配是否与设计系统一致
- [ ] 字体大小和间距是否规范
- [ ] 组件状态变化是否有适当反馈
- [ ] 整体视觉风格是否统一
- [ ] 数据展示格式是否一致
- [ ] 交互行为是否符合用户预期

## 🛡️ 错误处理与监控系统

### 统一错误处理服务 (ErrorHandlingService)

#### 🎯 核心职责
```typescript
class ErrorHandlingService {
  // 统一错误处理
  handleError(error: Error, context: ErrorContext): void

  // 错误分类和上报
  categorizeAndReport(error: Error): ErrorReport

  // 错误恢复策略
  attemptRecovery(error: Error, context: ErrorContext): Promise<boolean>

  // 获取错误统计
  getErrorStatistics(timeRange?: DateRange): ErrorStatistics
}

interface ErrorContext {
  service: string;                  // 服务名称
  method: string;                   // 方法名称
  orderId?: string;                 // 关联订单ID
  userId?: string;                  // 用户ID
  timestamp: Date;                  // 错误时间
  additionalData?: any;             // 附加数据
}

interface ErrorReport {
  id: string;                       // 错误ID
  category: 'network' | 'file' | 'ghostscript' | 'database' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;                  // 错误消息
  stack?: string;                   // 错误堆栈
  context: ErrorContext;            // 错误上下文
  recoverable: boolean;             // 是否可恢复
  userImpact: string;               // 用户影响描述
}
```

### 系统监控服务 (MonitoringService)

#### 📊 性能监控
```typescript
class MonitoringService {
  // 性能指标收集
  collectPerformanceMetrics(): PerformanceMetrics

  // 系统健康检查
  performHealthCheck(): HealthCheckResult

  // 资源使用监控
  monitorResourceUsage(): ResourceUsage

  // 业务指标统计
  getBusinessMetrics(timeRange: DateRange): BusinessMetrics
}

interface PerformanceMetrics {
  cpu: {
    usage: number;                  // CPU使用率 (%)
    loadAverage: number[];          // 负载平均值
  };
  memory: {
    used: number;                   // 已使用内存 (MB)
    total: number;                  // 总内存 (MB)
    usage: number;                  // 内存使用率 (%)
  };
  disk: {
    used: number;                   // 已使用磁盘空间 (GB)
    total: number;                  // 总磁盘空间 (GB)
    usage: number;                  // 磁盘使用率 (%)
  };
  network: {
    downloadSpeed: number;          // 下载速度 (MB/s)
    uploadSpeed: number;            // 上传速度 (MB/s)
    latency: number;                // 网络延迟 (ms)
  };
}
```

### 🔗 IPC通信接口规范

#### 主进程与渲染进程通信接口
```typescript
// 订单管理相关IPC接口
interface OrderIPCInterface {
  // 获取订单列表
  'order:get-list': (params: OrderListParams) => Promise<OrderListResult>;

  // 获取订单详情
  'order:get-details': (orderId: string) => Promise<Order>;

  // 更新订单状态
  'order:update-status': (orderId: string, status: OrderStatus, notes?: string) => Promise<boolean>;

  // 处理订单
  'order:process': (orderId: string) => Promise<ProcessingResult>;
}

// 物流发货相关IPC接口
interface LogisticsIPCInterface {
  // 扫描发货
  'logistics:scan-and-ship': (orderId: string, barcode: string, operatorName: string) => Promise<ShipmentResult>;

  // 获取待发货订单列表
  'logistics:get-pending-shipments': () => Promise<Order[]>;

  // 查询物流轨迹
  'logistics:query-tracking': (trackingNumber: string, companyCode: string) => Promise<TrackingResult>;

  // 获取发货统计
  'logistics:get-statistics': (dateRange?: DateRange) => Promise<ShippingStatistics>;

  // 批量更新发货状态
  'logistics:batch-update-status': () => Promise<BatchUpdateResult>;

  // 获取物流公司列表
  'logistics:get-companies': () => Promise<LogisticsCompany[]>;

  // 扫描设备状态
  'logistics:get-scanner-status': () => Promise<ScannerDevice[]>;
}

// 打印队列相关IPC接口
interface PrintQueueIPCInterface {
  // 添加打印任务
  'print:add-job': (jobConfig: PrintJobConfig) => Promise<string>;

  // 获取队列状态
  'print:get-queue-status': () => Promise<QueueStatus>;

  // 暂停/恢复队列
  'print:pause-queue': () => Promise<void>;
  'print:resume-queue': () => Promise<void>;

  // 获取打印任务列表
  'print:get-job-list': () => Promise<PrintJob[]>;
}

// 文件处理相关IPC接口
interface FileProcessingIPCInterface {
  // 开始文件处理
  'file:start-processing': (orderId: string) => Promise<void>;

  // 获取处理进度
  'file:get-processing-progress': (orderId: string) => Promise<ProcessingProgress>;

  // 预览文件
  'file:preview': (fileId: string) => Promise<string>; // 返回预览图片路径

  // 下载文件
  'file:download': (fileId: string, url: string) => Promise<DownloadResult>;
}

// 系统设置相关IPC接口
interface SystemIPCInterface {
  // 获取系统配置
  'system:get-config': () => Promise<SystemConfig>;

  // 更新系统配置
  'system:update-config': (config: Partial<SystemConfig>) => Promise<boolean>;

  // 获取系统状态
  'system:get-status': () => Promise<SystemStatus>;

  // 重启服务
  'system:restart-service': (serviceName: string) => Promise<boolean>;
}

// 事件通知接口
interface EventNotificationInterface {
  // 订单状态变更通知
  'event:order-status-changed': (data: { orderId: string; oldStatus: string; newStatus: string; timestamp: number });

  // 打印完成通知
  'event:print-completed': (data: { orderId: string; jobId: string; timestamp: number });

  // 发货完成通知
  'event:shipment-completed': (data: { orderId: string; shipmentId: string; trackingNumber: string; timestamp: number });

  // 文件下载完成通知
  'event:download-completed': (data: { orderId: string; fileId: string; success: boolean; timestamp: number });

  // 系统错误通知
  'event:system-error': (data: { error: SystemError; timestamp: number });
}
```

#### IPC通信实现示例
```typescript
// 主进程中的IPC处理器注册
class IPCHandlerRegistry {
  constructor(
    private orderService: OrderService,
    private logisticsService: LogisticsService,
    private printQueueService: PrintQueueService,
    private fileService: FileService
  ) {
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // 订单管理IPC处理器
    ipcMain.handle('order:get-list', async (event, params: OrderListParams) => {
      return await this.orderService.getOrderList(params);
    });

    ipcMain.handle('order:process', async (event, orderId: string) => {
      return await this.orderService.processOrder(orderId);
    });

    // 物流发货IPC处理器
    ipcMain.handle('logistics:scan-and-ship', async (event, orderId: string, barcode: string, operatorName: string) => {
      return await this.logisticsService.scanAndShip(orderId, barcode, operatorName);
    });

    ipcMain.handle('logistics:get-pending-shipments', async (event) => {
      return await this.logisticsService.getPendingShipments();
    });

    // 智能打印队列IPC处理器
    ipcMain.handle('print:add-job', async (event, jobConfig: PrintJobConfig) => {
      return await this.printQueueService.addPrintJob(jobConfig);
    });

    ipcMain.handle('print:get-queue-status', async (event) => {
      return await this.printQueueService.getQueueStatus();
    });

    // 打印机管理IPC处理器
    ipcMain.handle('printer:get-available', async (event) => {
      return await this.printQueueService.getAvailablePrinters();
    });

    ipcMain.handle('printer:detect-system', async (event) => {
      return await this.printQueueService.detectSystemPrinters();
    });

    ipcMain.handle('printer:get-capabilities', async (event, printerName: string) => {
      return await this.printQueueService.getPrinterCapabilities(printerName);
    });

    ipcMain.handle('printer:test-connection', async (event, printerName: string) => {
      return await this.printQueueService.testPrinterConnection(printerName);
    });

    // 智能选择IPC处理器
    ipcMain.handle('printer:suggest-optimal', async (event, orderId: string) => {
      return await this.printQueueService.suggestOptimalPrinter(orderId);
    });

    ipcMain.handle('printer:preview-selection', async (event, orderId: string, printerName: string) => {
      return await this.printQueueService.previewPrinterSelection(orderId, printerName);
    });

    // 事件通知发送
    this.setupEventNotifications();
  }

  private setupEventNotifications(): void {
    // 监听订单状态变更
    this.orderService.on('statusChanged', (data) => {
      this.broadcastToRenderers('event:order-status-changed', data);
    });

    // 监听打印完成
    this.printQueueService.on('printCompleted', (data) => {
      this.broadcastToRenderers('event:print-completed', data);
    });

    // 监听发货完成
    this.logisticsService.on('shipmentCompleted', (data) => {
      this.broadcastToRenderers('event:shipment-completed', data);
    });
  }

  private broadcastToRenderers(channel: string, data: any): void {
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send(channel, data);
    });
  }
}
```

### 日志管理系统

#### 📝 结构化日志
```typescript
interface LogEntry {
  timestamp: Date;                  // 时间戳
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  service: string;                  // 服务名称
  message: string;                  // 日志消息
  orderId?: string;                 // 关联订单ID
  userId?: string;                  // 用户ID
  metadata?: any;                   // 元数据
  traceId?: string;                 // 追踪ID
}

class LoggingService {
  // 记录日志
  log(level: LogLevel, message: string, metadata?: any): void

  // 查询日志
  queryLogs(filters: LogFilters): Promise<LogEntry[]>

  // 日志轮转
  rotateLogs(): Promise<void>

  // 导出日志
  exportLogs(timeRange: DateRange): Promise<string>
}
```

## 🔄 Ghostscript深度集成

### 🎯 核心设计理念
> **重要**: Ghostscript作为系统的核心引擎，负责所有PDF/PostScript文档的处理、优化、颜色管理和数字印刷输出。

#### 集成优势
1. **专业级文档处理**: 支持PostScript Level 3和PDF 2.0标准
2. **数字印刷优化**: 专门针对数字印刷设备的优化处理
3. **颜色管理**: ICC配置文件支持，精确颜色控制
4. **中文字体支持**: 完整的CJK字体处理方案
5. **装订处理**: 智能拼版和装订方式处理

### 核心引擎架构
```typescript
class GhostscriptEngine {
  // 文档处理引擎
  private documentProcessor: GhostscriptDocumentProcessor;

  // 数字印刷引擎
  private digitalPrintProcessor: GhostscriptDigitalPrintProcessor;

  // 颜色管理引擎
  private colorManager: GhostscriptColorManager;

  // 预印刷引擎
  private prepressProcessor: GhostscriptPrepressProcessor;

  // 中文字体管理器
  private cjkFontManager: GhostscriptCJKFontManager;

  // 装订处理器
  private bindingProcessor: GhostscriptBindingProcessor;

  // 初始化引擎
  async initialize(): Promise<void>

  // 处理文档
  async processDocument(inputPath: string, options: ProcessingOptions): Promise<ProcessingResult>

  // 生成预览
  async generatePreview(inputPath: string, options: PreviewOptions): Promise<string>

  // 颜色分离
  async performColorSeparation(inputPath: string, options: SeparationOptions): Promise<SeparationResult>

  // 装订处理
  async processBinding(inputPath: string, bindingType: BindingType, options: BindingOptions): Promise<string>
}
```

### 🖨️ 数字印刷专业功能

#### 数字印刷处理器
```typescript
class GhostscriptDigitalPrintProcessor {
  // PDF优化 - 针对数字印刷设备
  async optimizeForDigitalPrint(inputPath: string, options: DigitalPrintOptions): Promise<string> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-dPDFSETTINGS=/printer',           // 打印机优化
      '-dColorImageResolution=300',        // 彩色图像300DPI
      '-dGrayImageResolution=300',         // 灰度图像300DPI
      '-dMonoImageResolution=1200',        // 单色图像1200DPI
      '-dDownsampleColorImages=true',      // 降采样彩色图像
      '-dDownsampleGrayImages=true',       // 降采样灰度图像
      '-dCompressPages=true',              // 压缩页面
      '-dUseFlateCompression=true',        // 使用Flate压缩
      '-dOptimize=true',                   // 优化PDF结构
      '-dPreserveOverprintSettings=true',  // 保留叠印设置
      '-dPreserveHalftoneInfo=true',       // 保留网点信息
      '-dTransferFunctionInfo=/Preserve',  // 保留传递函数
      '-dColorConversionStrategy=/LeaveColorUnchanged', // 保持颜色不变
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }

  // CMYK颜色空间转换
  async convertToCMYK(inputPath: string, iccProfile?: string): Promise<string> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-sColorConversionStrategy=CMYK',
      '-dProcessColorModel=/DeviceCMYK',
      '-dConvertCMYKImagesToRGB=false',
      '-dConvertImagesToIndexed=false',
      '-dAutoRotatePages=/None',
      iccProfile ? `-sDefaultCMYKProfile=${iccProfile}` : '',
      `-sOutputFile=${outputPath}`,
      inputPath
    ].filter(Boolean);

    return this.executeGhostscript(gsCommand);
  }

  // 专色分离
  async separateSpotColors(inputPath: string, spotColors: string[]): Promise<SeparationResult> {
    const separations: { [color: string]: string } = {};

    // 为每个专色创建分离版
    for (const spotColor of spotColors) {
      const outputPath = `${inputPath}_${spotColor}.pdf`;
      const gsCommand = [
        'gs',
        '-dNOPAUSE',
        '-dBATCH',
        '-dSAFER',
        '-sDEVICE=pdfwrite',
        '-dProcessColorModel=/DeviceGray',
        `-sSpotColorName=${spotColor}`,
        '-dExtractSpotColor=true',
        `-sOutputFile=${outputPath}`,
        inputPath
      ];

      await this.executeGhostscript(gsCommand);
      separations[spotColor] = outputPath;
    }

    return { separations, composite: inputPath };
  }

  // 印刷标记添加
  async addPrintMarks(inputPath: string, marks: PrintMarks): Promise<string> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      marks.cropMarks ? '-dCropMarks=true' : '',
      marks.bleedMarks ? '-dBleedMarks=true' : '',
      marks.registrationMarks ? '-dRegistrationMarks=true' : '',
      marks.colorBars ? '-dColorBars=true' : '',
      marks.pageInfo ? '-dPageInfo=true' : '',
      `-sOutputFile=${outputPath}`,
      inputPath
    ].filter(Boolean);

    return this.executeGhostscript(gsCommand);
  }
}
```

### 🎨 颜色管理系统

#### ICC颜色管理
```typescript
class GhostscriptColorManager {
  // ICC配置文件路径
  private iccProfiles = {
    'ISO Coated v2 (ECI)': './icc/ISOcoated_v2_eci.icc',
    'GRACoL2006_Coated1v2': './icc/GRACoL2006_Coated1v2.icc',
    'Japan Color 2001 Coated': './icc/JapanColor2001Coated.icc',
    'Fogra39': './icc/Fogra39.icc',
    'sRGB': './icc/sRGB.icc'
  };

  // 颜色空间转换
  async convertColorSpace(inputPath: string, targetProfile: string, intent: RenderingIntent = 'RelativeColorimetric'): Promise<string> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-dUseCIEColor=true',
      `-sDefaultCMYKProfile=${this.iccProfiles[targetProfile]}`,
      `-sRenderingIntent=${intent}`,
      '-dColorConversionStrategy=UseDeviceIndependentColor',
      '-dProcessColorModel=/DeviceCMYK',
      '-dConvertCMYKImagesToRGB=false',
      '-dPreserveOverprintSettings=true',
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }

  // 颜色校准
  async calibrateColors(inputPath: string, calibrationData: ColorCalibration): Promise<string> {
    // 生成颜色校准PostScript代码
    const calibrationPS = this.generateCalibrationPS(calibrationData);
    const tempCalibrationFile = './temp/calibration.ps';

    await fs.writeFile(tempCalibrationFile, calibrationPS);

    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      tempCalibrationFile,  // 先加载校准文件
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }

  // 生成颜色校准PostScript代码
  private generateCalibrationPS(calibration: ColorCalibration): string {
    return `
%!PS-Adobe-3.0
% 颜色校准设置
/ColorCalibration <<
  /CyanAdjustment ${calibration.cyan}
  /MagentaAdjustment ${calibration.magenta}
  /YellowAdjustment ${calibration.yellow}
  /BlackAdjustment ${calibration.black}
  /Gamma ${calibration.gamma}
  /Brightness ${calibration.brightness}
  /Contrast ${calibration.contrast}
>> def

% 应用颜色校准
{
  ColorCalibration /CyanAdjustment get mul
  ColorCalibration /MagentaAdjustment get mul
  ColorCalibration /YellowAdjustment get mul
  ColorCalibration /BlackAdjustment get mul
} bind def
/setcmykcolor load 1 4 roll 4 -1 roll exec setcmykcolor

% 伽马校正
ColorCalibration /Gamma get dup dup
{} settransfer
    `;
  }

  // 专色管理
  async manageSpotColors(inputPath: string, spotColorMap: SpotColorMapping): Promise<string> {
    const spotColorPS = this.generateSpotColorPS(spotColorMap);
    const tempSpotColorFile = './temp/spotcolors.ps';

    await fs.writeFile(tempSpotColorFile, spotColorPS);

    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      tempSpotColorFile,
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }
}
```

### 🔤 中文字体支持解决方案

#### CJK字体管理器
```typescript
class GhostscriptCJKFontManager {
  // Windows中文字体路径
  private readonly windowsFonts = {
    'SimSun': 'C:/Windows/Fonts/simsun.ttc',
    'SimHei': 'C:/Windows/Fonts/simhei.ttf',
    'KaiTi': 'C:/Windows/Fonts/kaiti.ttf',
    'FangSong': 'C:/Windows/Fonts/simfang.ttf',
    'Microsoft YaHei': 'C:/Windows/Fonts/msyh.ttc',
    'Microsoft JhengHei': 'C:/Windows/Fonts/msjh.ttc'
  };

  // 初始化中文字体支持
  async initializeCJKSupport(): Promise<void> {
    // 1. 创建Fontmap配置
    await this.createCJKFontmap();

    // 2. 设置字体路径
    await this.setupFontPath();

    // 3. 配置CID字体
    await this.configureCIDFonts();

    // 4. 验证字体可用性
    await this.validateFonts();
  }

  // 创建CJK Fontmap配置
  private async createCJKFontmap(): Promise<void> {
    const fontmapContent = `
% CJK字体映射配置
% 简体中文字体
/SimSun << /FileType /TrueType /Path (C:/Windows/Fonts/simsun.ttc) /SubfontID 0 /CSI [(GB1) 6] >> ;
/SimHei << /FileType /TrueType /Path (C:/Windows/Fonts/simhei.ttf) /SubfontID 0 /CSI [(GB1) 6] >> ;
/KaiTi << /FileType /TrueType /Path (C:/Windows/Fonts/kaiti.ttf) /SubfontID 0 /CSI [(GB1) 6] >> ;
/FangSong << /FileType /TrueType /Path (C:/Windows/Fonts/simfang.ttf) /SubfontID 0 /CSI [(GB1) 6] >> ;

% 微软雅黑字体族
/Microsoft-YaHei << /FileType /TrueType /Path (C:/Windows/Fonts/msyh.ttc) /SubfontID 0 /CSI [(GB1) 6] >> ;
/Microsoft-YaHei-Bold << /FileType /TrueType /Path (C:/Windows/Fonts/msyhbd.ttc) /SubfontID 0 /CSI [(GB1) 6] >> ;

% CID字体别名
/STSong-Light /SimSun ;
/STHeiti-Regular /SimHei ;
/STKaiti-Regular /KaiTi ;
/STFangsong-Light /FangSong ;

% Adobe字体别名
/AdobeSongStd-Light /SimSun ;
/AdobeHeitiStd-Regular /SimHei ;
/AdobeKaitiStd-Regular /KaiTi ;
/AdobeFangsongStd-Regular /FangSong ;

% 繁体中文字体（如果可用）
/Microsoft-JhengHei << /FileType /TrueType /Path (C:/Windows/Fonts/msjh.ttc) /SubfontID 0 /CSI [(CNS1) 6] >> ;

% 日文字体
/MS-Mincho << /FileType /TrueType /Path (C:/Windows/Fonts/msmincho.ttc) /SubfontID 0 /CSI [(Japan1) 6] >> ;
/MS-Gothic << /FileType /TrueType /Path (C:/Windows/Fonts/msgothic.ttc) /SubfontID 0 /CSI [(Japan1) 6] >> ;

% 韩文字体
/Batang << /FileType /TrueType /Path (C:/Windows/Fonts/batang.ttc) /SubfontID 0 /CSI [(Korea1) 2] >> ;
/Gulim << /FileType /TrueType /Path (C:/Windows/Fonts/gulim.ttc) /SubfontID 0 /CSI [(Korea1) 2] >> ;
`;

    const fontmapPath = './gs/lib/Fontmap.CJK';
    await fs.writeFile(fontmapPath, fontmapContent, 'utf8');
  }

  // 配置CID字体资源
  private async configureCIDFonts(): Promise<void> {
    // 创建CIDFont资源目录
    const cidFontDir = './gs/Resource/CIDFont';
    await fs.ensureDir(cidFontDir);

    // 创建CMap资源目录
    const cmapDir = './gs/Resource/CMap';
    await fs.ensureDir(cmapDir);

    // 下载并安装Adobe CMap资源
    await this.installAdobeCMaps();
  }

  // 处理中文PDF文档
  async processCJKDocument(inputPath: string, options: CJKProcessingOptions): Promise<string> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-sFONTMAP=./gs/lib/Fontmap.CJK',           // 使用CJK字体映射
      '-sFONTPATH=C:/Windows/Fonts',              // 设置字体路径
      '-dSubsetFonts=false',                      // 不子集化字体
      '-dEmbedAllFonts=true',                     // 嵌入所有字体
      '-dCompressFonts=true',                     // 压缩字体
      '-dAutoRotatePages=/None',                  // 不自动旋转
      '-dPreserveAnnots=true',                    // 保留注释
      '-dPreserveMarkedContent=true',             // 保留标记内容
      '-dPDFSETTINGS=/printer',                   // 打印机设置
      '-dColorConversionStrategy=/LeaveColorUnchanged', // 保持颜色
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }

  // 字体替换和修复
  async repairCJKFonts(inputPath: string): Promise<string> {
    // 1. 分析文档中的字体使用情况
    const fontAnalysis = await this.analyzeFonts(inputPath);

    // 2. 创建字体替换映射
    const fontSubstitution = this.createFontSubstitution(fontAnalysis);

    // 3. 应用字体替换
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-sFONTMAP=./gs/lib/Fontmap.CJK',
      '-sFONTPATH=C:/Windows/Fonts',
      '-dSubsetFonts=false',
      '-dEmbedAllFonts=true',
      fontSubstitution,  // 字体替换PostScript代码
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }

  // 分析文档字体
  private async analyzeFonts(inputPath: string): Promise<FontAnalysis> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=bbox',
      '-dTextAlphaBits=4',
      '-c',
      `
      % 字体分析PostScript代码
      /FontAnalysis {
        currentpagedevice /PageSize get aload pop
        2 div exch 2 div exch translate
        0 0 moveto
        currentfont /FontName get ==
        currentfont /FontType get ==
        currentfont /Encoding get ==
      } def
      `,
      '-f',
      inputPath
    ];

    const result = await this.executeGhostscript(gsCommand);
    return this.parseFontAnalysis(result);
  }
}
```

### 📐 装订处理系统

#### 装订处理器
```typescript
class GhostscriptBindingProcessor {
  // 骑马订处理 (A4 → A3)
  async processSaddleStitching(inputPath: string, options: SaddleStitchingOptions): Promise<string> {
    // 1. 计算页面数量和排版
    const pageCount = await this.getPageCount(inputPath);
    const signatures = Math.ceil(pageCount / 4) * 4; // 确保是4的倍数

    // 2. 生成拼版PostScript
    const impositionPS = this.generateSaddleStitchingPS(signatures, options);
    const tempImpositionFile = './temp/saddle_stitching.ps';

    await fs.writeFile(tempImpositionFile, impositionPS);

    // 3. 执行拼版
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-sPAPERSIZE=a3',                    // 输出A3尺寸
      '-dFIXEDMEDIA',                      // 固定纸张尺寸
      '-dPDFFitPage',                      // 适应页面
      '-dAutoRotatePages=/None',           // 不自动旋转
      tempImpositionFile,                  // 拼版脚本
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }

  // 胶装处理
  async processPerfectBinding(inputPath: string, options: PerfectBindingOptions): Promise<string> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-dAutoRotatePages=/None',
      '-dPreserveAnnots=true',
      // 胶装特殊处理：添加装订边距
      `-c "<< /PageOffset [${options.bindingMargin} 0] >> setpagedevice"`,
      `-sOutputFile=${outputPath}`,
      inputPath
    ];

    return this.executeGhostscript(gsCommand);
  }

  // 铁圈装处理
  async processSpiralBinding(inputPath: string, options: SpiralBindingOptions): Promise<string> {
    const gsCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-dAutoRotatePages=/None',
      // 铁圈装：左侧留出打孔边距
      `-c "<< /PageOffset [${options.punchMargin} 0] >> setpagedevice"`,
      // 添加打孔标记
      options.addPunchMarks ? '-dPunchMarks=true' : '',
      `-sOutputFile=${outputPath}`,
      inputPath
    ].filter(Boolean);

    return this.executeGhostscript(gsCommand);
  }

  // 生成骑马订拼版PostScript
  private generateSaddleStitchingPS(signatures: number, options: SaddleStitchingOptions): string {
    return `
%!PS-Adobe-3.0
% 骑马订拼版处理

% 设置A3页面
<< /PageSize [842 1191] >> setpagedevice

% 定义拼版函数
/SaddleStitchImposition {
  /pagenum exch def
  /totalPages ${signatures} def

  % 计算页面位置
  pagenum 4 mod 0 eq {
    % 第4页：右下
    421 0 translate
    0.707 0.707 scale
  } {
    pagenum 4 mod 1 eq {
      % 第1页：左下
      0 0 translate
      0.707 0.707 scale
    } {
      pagenum 4 mod 2 eq {
        % 第2页：左上
        0 595.5 translate
        0.707 0.707 scale
      } {
        % 第3页：右上
        421 595.5 translate
        0.707 0.707 scale
      } ifelse
    } ifelse
  } ifelse
} def

% 应用拼版
1 1 ${signatures} {
  SaddleStitchImposition
  showpage
} for
    `;
  }
}
```

### 🔧 Ghostscript配置与优化

#### 系统配置
```typescript
class GhostscriptConfiguration {
  // 初始化Ghostscript环境
  async initializeEnvironment(): Promise<void> {
    // 1. 设置环境变量
    process.env.GS_LIB = './gs/lib;./gs/Resource/Init;./gs/Resource/Font;./gs/Resource/CIDFont';
    process.env.GS_FONTPATH = 'C:/Windows/Fonts;./fonts';
    process.env.GS_OPTIONS = '-dSAFER -dNOPAUSE -dBATCH';

    // 2. 创建必要目录
    await this.createDirectories();

    // 3. 安装ICC配置文件
    await this.installICCProfiles();

    // 4. 配置中文字体支持
    await this.setupCJKSupport();
  }

  // 性能优化配置
  getOptimizedGSOptions(operation: 'preview' | 'print' | 'archive'): string[] {
    const baseOptions = [
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-dMaxBitmap=500000000',        // 500MB位图缓存
      '-dBufferSpace=1000000000',     // 1GB缓冲区
      '-dBandBufferSpace=500000000',  // 500MB带缓冲区
      '-dNOGC',                       // 禁用垃圾回收（提高性能）
      '-dNOOUTERSAVE',               // 禁用外部保存
    ];

    switch (operation) {
      case 'preview':
        return [
          ...baseOptions,
          '-sDEVICE=png16m',
          '-r150',                    // 预览用150DPI
          '-dTextAlphaBits=4',
          '-dGraphicsAlphaBits=4',
          '-dDownsampleColorImages=true',
          '-dColorImageResolution=150'
        ];

      case 'print':
        return [
          ...baseOptions,
          '-sDEVICE=pdfwrite',
          '-dPDFSETTINGS=/printer',
          '-dColorImageResolution=300',
          '-dGrayImageResolution=300',
          '-dMonoImageResolution=1200',
          '-dPreserveOverprintSettings=true',
          '-dPreserveHalftoneInfo=true'
        ];

      case 'archive':
        return [
          ...baseOptions,
          '-sDEVICE=pdfwrite',
          '-dPDFSETTINGS=/prepress',
          '-dColorImageResolution=300',
          '-dGrayImageResolution=300',
          '-dMonoImageResolution=1200',
          '-dPDFA=2',                 // PDF/A-2标准
          '-dPDFACompatibilityPolicy=1'
        ];

      default:
        return baseOptions;
    }
  }

  // 中文字体环境配置
  private async setupCJKSupport(): Promise<void> {
    // 创建CJK字体配置文件
    const cjkConfig = `
% CJK字体配置
systemdict begin
  /CJKFontPath (C:/Windows/Fonts) def
  /CJKFontMap (./gs/lib/Fontmap.CJK) def

  % 设置默认CJK字体
  /DefaultCJKFont {
    /SimSun findfont
    12 scalefont
    setfont
  } def

  % 字体回退机制
  /FontNotFound {
    pop
    /SimSun findfont
    exch scalefont
    setfont
  } def
end
`;

    await fs.writeFile('./gs/lib/cjk_init.ps', cjkConfig);
  }
}
```

### 📊 性能监控与优化

#### 性能监控器
```typescript
class GhostscriptPerformanceMonitor {
  // 监控Ghostscript执行性能
  async monitorExecution(command: string[], inputFile: string): Promise<PerformanceMetrics> {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    try {
      const result = await this.executeGhostscript(command);
      const endTime = Date.now();
      const endMemory = process.memoryUsage();

      const metrics: PerformanceMetrics = {
        executionTime: endTime - startTime,
        memoryUsage: {
          peak: Math.max(startMemory.heapUsed, endMemory.heapUsed),
          delta: endMemory.heapUsed - startMemory.heapUsed
        },
        inputFileSize: await this.getFileSize(inputFile),
        outputFileSize: result.outputFile ? await this.getFileSize(result.outputFile) : 0,
        compressionRatio: 0,
        processingSpeed: 0 // 页/秒
      };

      if (metrics.outputFileSize > 0) {
        metrics.compressionRatio = metrics.inputFileSize / metrics.outputFileSize;
      }

      const pageCount = await this.getPageCount(inputFile);
      metrics.processingSpeed = pageCount / (metrics.executionTime / 1000);

      return metrics;
    } catch (error) {
      throw new Error(`Ghostscript执行失败: ${error.message}`);
    }
  }

  // 优化建议
  generateOptimizationSuggestions(metrics: PerformanceMetrics): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    if (metrics.executionTime > 30000) { // 超过30秒
      suggestions.push({
        type: 'performance',
        message: '处理时间过长，建议增加内存缓冲区大小',
        recommendation: '添加 -dMaxBitmap=1000000000 参数'
      });
    }

    if (metrics.memoryUsage.peak > 1000000000) { // 超过1GB
      suggestions.push({
        type: 'memory',
        message: '内存使用过高，建议启用分带处理',
        recommendation: '添加 -dBandBufferSpace=100000000 参数'
      });
    }

    if (metrics.compressionRatio < 0.5) { // 压缩率低于50%
      suggestions.push({
        type: 'compression',
        message: '压缩效果不佳，建议调整压缩设置',
        recommendation: '使用 -dUseFlateCompression=true 参数'
      });
    }

    return suggestions;
  }
}
```

### 🛠️ 错误处理与恢复

#### Ghostscript错误处理
```typescript
class GhostscriptErrorHandler {
  // 处理常见错误
  async handleError(error: GhostscriptError, context: ProcessingContext): Promise<RecoveryResult> {
    switch (error.type) {
      case 'font_not_found':
        return this.handleFontError(error, context);

      case 'memory_error':
        return this.handleMemoryError(error, context);

      case 'color_space_error':
        return this.handleColorSpaceError(error, context);

      case 'cjk_encoding_error':
        return this.handleCJKEncodingError(error, context);

      default:
        return this.handleGenericError(error, context);
    }
  }

  // 处理中文字体错误
  private async handleCJKEncodingError(error: GhostscriptError, context: ProcessingContext): Promise<RecoveryResult> {
    // 1. 尝试字体替换
    const fontSubstitution = await this.createFontSubstitution(context.inputFile);

    // 2. 重新处理文档
    const recoveryCommand = [
      'gs',
      '-dNOPAUSE',
      '-dBATCH',
      '-dSAFER',
      '-sDEVICE=pdfwrite',
      '-sFONTMAP=./gs/lib/Fontmap.CJK',
      '-sFONTPATH=C:/Windows/Fonts',
      '-dSubsetFonts=false',
      '-dEmbedAllFonts=true',
      '-c', fontSubstitution,
      `-sOutputFile=${context.outputFile}`,
      context.inputFile
    ];

    try {
      await this.executeGhostscript(recoveryCommand);
      return {
        success: true,
        message: '中文字体问题已修复',
        recoveryMethod: 'font_substitution'
      };
    } catch (recoveryError) {
      return {
        success: false,
        message: '无法修复中文字体问题',
        originalError: error,
        recoveryError
      };
    }
  }
}
```

### 📋 数据类型定义

#### 核心接口定义
```typescript
// 处理选项
interface ProcessingOptions {
  quality: 'draft' | 'normal' | 'high' | 'prepress';
  colorSpace: 'RGB' | 'CMYK' | 'Grayscale';
  resolution: number;
  compression: boolean;
  embedFonts: boolean;
  preserveTransparency: boolean;
  iccProfile?: string;
}

// 数字印刷选项
interface DigitalPrintOptions extends ProcessingOptions {
  printMarks: PrintMarks;
  bleedSize: number;
  trimSize: { width: number; height: number };
  colorManagement: ColorManagementOptions;
  bindingType: BindingType;
}

// 印刷标记
interface PrintMarks {
  cropMarks: boolean;
  bleedMarks: boolean;
  registrationMarks: boolean;
  colorBars: boolean;
  pageInfo: boolean;
}

// 颜色管理选项
interface ColorManagementOptions {
  sourceProfile: string;
  destinationProfile: string;
  renderingIntent: RenderingIntent;
  blackPointCompensation: boolean;
  preserveOverprint: boolean;
}

// 渲染意图
type RenderingIntent = 'Perceptual' | 'RelativeColorimetric' | 'Saturation' | 'AbsoluteColorimetric';

// 装订类型
type BindingType = 'saddle_stitching' | 'perfect_binding' | 'spiral_binding' | 'wire_binding';

// 装订选项
interface BindingOptions {
  bindingMargin: number;
  gutterSize: number;
  signatures?: number;
}

// 骑马订选项
interface SaddleStitchingOptions extends BindingOptions {
  foldMarks: boolean;
  centerlineGuides: boolean;
}

// 胶装选项
interface PerfectBindingOptions extends BindingOptions {
  spineWidth: number;
  coverType: 'soft' | 'hard';
}

// 铁圈装选项
interface SpiralBindingOptions extends BindingOptions {
  punchMargin: number;
  addPunchMarks: boolean;
  holeSpacing: number;
}

// CJK处理选项
interface CJKProcessingOptions {
  fontEmbedding: 'subset' | 'full' | 'none';
  fontSubstitution: boolean;
  encodingFix: boolean;
  unicodeNormalization: boolean;
}

// 字体分析结果
interface FontAnalysis {
  fonts: FontInfo[];
  missingFonts: string[];
  cjkFonts: CJKFontInfo[];
  encodingIssues: EncodingIssue[];
}

// 字体信息
interface FontInfo {
  name: string;
  type: 'Type1' | 'TrueType' | 'CIDFont' | 'Type3';
  embedded: boolean;
  subset: boolean;
  encoding: string;
}

// CJK字体信息
interface CJKFontInfo extends FontInfo {
  language: 'Chinese' | 'Japanese' | 'Korean';
  variant: 'Simplified' | 'Traditional' | 'Kanji' | 'Hangul';
  characterSet: string;
}

// 编码问题
interface EncodingIssue {
  fontName: string;
  issue: 'missing_characters' | 'wrong_encoding' | 'corrupted_font';
  severity: 'low' | 'medium' | 'high';
  suggestion: string;
}

// 颜色分离结果
interface SeparationResult {
  separations: { [colorName: string]: string };
  composite: string;
  spotColors: string[];
  processColors: string[];
}

// 专色映射
interface SpotColorMapping {
  [spotColorName: string]: {
    cmykEquivalent: [number, number, number, number];
    pantoneNumber?: string;
    density: number;
  };
}

// 颜色校准
interface ColorCalibration {
  cyan: number;
  magenta: number;
  yellow: number;
  black: number;
  gamma: number;
  brightness: number;
  contrast: number;
}

// 性能指标
interface PerformanceMetrics {
  executionTime: number;
  memoryUsage: {
    peak: number;
    delta: number;
  };
  inputFileSize: number;
  outputFileSize: number;
  compressionRatio: number;
  processingSpeed: number; // 页/秒
}

// 优化建议
interface OptimizationSuggestion {
  type: 'performance' | 'memory' | 'compression' | 'quality';
  message: string;
  recommendation: string;
  priority: 'low' | 'medium' | 'high';
}

// Ghostscript错误
interface GhostscriptError {
  type: 'font_not_found' | 'memory_error' | 'color_space_error' | 'cjk_encoding_error' | 'generic_error';
  message: string;
  code: number;
  context: string;
}

// 恢复结果
interface RecoveryResult {
  success: boolean;
  message: string;
  recoveryMethod?: string;
  originalError?: GhostscriptError;
  recoveryError?: Error;
}

// 处理上下文
interface ProcessingContext {
  inputFile: string;
  outputFile: string;
  options: ProcessingOptions;
  tempDir: string;
}

// 文件路径配置
interface FilePathConfiguration {
  // CDN文件下载路径
  cdnDownloadPath: string;              // CDN文件本地下载目录

  // 文件处理路径
  processingPaths: {
    inputDir: string;                   // 输入文件目录
    outputDir: string;                  // 输出文件目录
    tempDir: string;                    // 临时文件目录
    archiveDir: string;                 // 归档文件目录
  };

  // 标签文件路径
  labelPaths: {
    templateDir: string;                // 标签模板目录
    generatedDir: string;               // 生成的标签目录
  };

  // 系统文件路径
  systemPaths: {
    logDir: string;                     // 日志文件目录
    configDir: string;                  // 配置文件目录
    backupDir: string;                  // 备份文件目录
    cacheDir: string;                   // 缓存文件目录
  };

  // 自动清理设置
  cleanupSettings: {
    enableAutoCleanup: boolean;         // 启用自动清理
    tempFileRetentionDays: number;      // 临时文件保留天数
    archiveRetentionDays: number;       // 归档文件保留天数
    logRetentionDays: number;           // 日志文件保留天数
  };
}

// CDN下载配置
interface CDNDownloadConfiguration {
  // 下载设置
  downloadSettings: {
    maxConcurrentDownloads: number;     // 最大并发下载数
    downloadTimeout: number;            // 下载超时时间(秒)
    retryAttempts: number;              // 重试次数
    retryDelay: number;                 // 重试延迟(秒)
  };

  // 存储设置
  storageSettings: {
    maxStorageSize: number;             // 最大存储大小(GB)
    compressionEnabled: boolean;        // 启用压缩存储
    encryptionEnabled: boolean;         // 启用加密存储
  };

  // 缓存策略
  cacheStrategy: {
    enableCache: boolean;               // 启用缓存
    cacheExpiration: number;            // 缓存过期时间(小时)
    maxCacheSize: number;               // 最大缓存大小(MB)
  };

  // 网络设置
  networkSettings: {
    useProxy: boolean;                  // 使用代理
    proxyHost?: string;                 // 代理主机
    proxyPort?: number;                 // 代理端口
    proxyAuth?: {                       // 代理认证
      username: string;
      password: string;
    };
  };
}

// 文件下载结果
interface DownloadResult {
  success: boolean;
  localPath?: string;
  fileSize?: number;
  checksum?: string;
  error?: string;
}

// 批量下载结果
interface BatchDownloadResult {
  totalFiles: number;
  successCount: number;
  failedCount: number;
  results: Array<{
    fileUrl: string;
    result: DownloadResult;
  }>;
}

// 清理结果
interface CleanupResult {
  deletedFiles: number;
  freedSpace: number; // 字节
  errors: string[];
}

// 存储信息
interface StorageInfo {
  totalSpace: number;     // 总空间(字节)
  usedSpace: number;      // 已用空间(字节)
  freeSpace: number;      // 可用空间(字节)
  usagePercentage: number; // 使用率(%)
}

// 优化结果
interface OptimizationResult {
  optimizedFiles: number;
  spaceSaved: number;     // 节省的空间(字节)
  compressionRatio: number;
}

// 完整性验证结果
interface IntegrityResult {
  isValid: boolean;
  expectedChecksum?: string;
  actualChecksum?: string;
  error?: string;
}

// 验证结果
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
```

### 🚀 集成示例

#### 完整处理流程示例
```typescript
class PrintTerminalGhostscriptIntegration {
  private gsEngine: GhostscriptEngine;
  private cjkManager: GhostscriptCJKFontManager;
  private colorManager: GhostscriptColorManager;
  private bindingProcessor: GhostscriptBindingProcessor;

  async processOrderDocument(orderId: string, files: OrderFile[]): Promise<ProcessingResult> {
    try {
      // 1. 初始化Ghostscript环境
      await this.gsEngine.initialize();

      // 2. 处理每个文件
      const processedFiles: ProcessedFile[] = [];

      for (const file of files) {
        // 2.1 分析文件
        const analysis = await this.analyzeFile(file.path);

        // 2.2 处理中文字体问题
        if (analysis.hasCJKContent) {
          file.path = await this.cjkManager.processCJKDocument(file.path, {
            fontEmbedding: 'full',
            fontSubstitution: true,
            encodingFix: true,
            unicodeNormalization: true
          });
        }

        // 2.3 颜色管理
        if (file.printOptions.colorSpace === 'CMYK') {
          file.path = await this.colorManager.convertToCMYK(
            file.path,
            file.printOptions.iccProfile
          );
        }

        // 2.4 数字印刷优化
        file.path = await this.gsEngine.processDocument(file.path, {
          quality: 'high',
          colorSpace: file.printOptions.colorSpace,
          resolution: 300,
          compression: true,
          embedFonts: true,
          preserveTransparency: false
        });

        // 2.5 装订处理
        if (file.printOptions.bindingType === 'saddle_stitching') {
          file.path = await this.bindingProcessor.processSaddleStitching(file.path, {
            bindingMargin: 5,
            gutterSize: 10,
            foldMarks: true,
            centerlineGuides: true
          });
        }

        processedFiles.push({
          originalPath: file.originalPath,
          processedPath: file.path,
          status: 'completed'
        });
      }

      // 3. 生成最终输出
      const finalOutput = await this.generateFinalOutput(processedFiles, orderId);

      return {
        success: true,
        outputPath: finalOutput,
        processedFiles,
        metrics: await this.collectMetrics()
      };

    } catch (error) {
      // 错误处理和恢复
      const recovery = await this.handleProcessingError(error, orderId);

      if (recovery.success) {
        return recovery.result;
      } else {
        throw new Error(`文档处理失败: ${error.message}`);
      }
    }
  }
}
```

### 🔧 部署配置指南

#### Ghostscript安装与配置
```bash
# 1. 安装Ghostscript (Windows)
# 下载并安装 Ghostscript 10.0+
# 官方下载地址: https://www.ghostscript.com/download/gsdnld.html

# 2. 设置环境变量
set GS_LIB=C:\Program Files\gs\gs10.00.0\lib;C:\Program Files\gs\gs10.00.0\Resource\Init
set GS_FONTPATH=C:\Windows\Fonts;C:\Program Files\gs\gs10.00.0\Resource\Font
set PATH=%PATH%;C:\Program Files\gs\gs10.00.0\bin

# 3. 验证安装
gs -version
```

#### 中文字体配置脚本
```powershell
# setup_cjk_fonts.ps1
# 自动配置中文字体支持

param(
    [string]$GhostscriptPath = "C:\Program Files\gs\gs10.00.0",
    [string]$FontPath = "C:\Windows\Fonts"
)

Write-Host "配置Ghostscript中文字体支持..." -ForegroundColor Green

# 1. 创建CJK字体映射文件
$FontmapContent = @"
% CJK字体映射配置 - 自动生成
% 简体中文字体
/SimSun << /FileType /TrueType /Path ($FontPath/simsun.ttc) /SubfontID 0 /CSI [(GB1) 6] >> ;
/SimHei << /FileType /TrueType /Path ($FontPath/simhei.ttf) /SubfontID 0 /CSI [(GB1) 6] >> ;
/KaiTi << /FileType /TrueType /Path ($FontPath/kaiti.ttf) /SubfontID 0 /CSI [(GB1) 6] >> ;
/FangSong << /FileType /TrueType /Path ($FontPath/simfang.ttf) /SubfontID 0 /CSI [(GB1) 6] >> ;

% 微软雅黑字体族
/Microsoft-YaHei << /FileType /TrueType /Path ($FontPath/msyh.ttc) /SubfontID 0 /CSI [(GB1) 6] >> ;
/Microsoft-YaHei-Bold << /FileType /TrueType /Path ($FontPath/msyhbd.ttc) /SubfontID 0 /CSI [(GB1) 6] >> ;

% CID字体别名
/STSong-Light /SimSun ;
/STHeiti-Regular /SimHei ;
/STKaiti-Regular /KaiTi ;
/STFangsong-Light /FangSong ;

% Adobe字体别名
/AdobeSongStd-Light /SimSun ;
/AdobeHeitiStd-Regular /SimHei ;
/AdobeKaitiStd-Regular /KaiTi ;
/AdobeFangsongStd-Regular /FangSong ;
"@

$FontmapPath = Join-Path $GhostscriptPath "lib\Fontmap.CJK"
$FontmapContent | Out-File -FilePath $FontmapPath -Encoding UTF8

Write-Host "已创建CJK字体映射文件: $FontmapPath" -ForegroundColor Yellow

# 2. 创建CJK初始化脚本
$CJKInitContent = @"
%!PS-Adobe-3.0
% CJK字体初始化脚本

systemdict begin
  % 设置CJK字体路径
  /CJKFontPath ($FontPath) def
  /CJKFontMap ($GhostscriptPath/lib/Fontmap.CJK) def

  % 默认CJK字体设置
  /DefaultCJKFont {
    /SimSun findfont
    12 scalefont
    setfont
  } def

  % 字体回退机制
  /FontNotFound {
    pop
    (警告: 字体未找到，使用SimSun替代) print
    /SimSun findfont
    exch scalefont
    setfont
  } def

  % 中文编码修复
  /FixCJKEncoding {
    currentfont /Encoding get
    dup type /arraytype eq {
      % 修复常见的中文编码问题
      dup length 256 eq {
        % GB2312/GBK编码处理
        pop /GB2312Encoding load
      } if
    } if
  } def
end
"@

$CJKInitPath = Join-Path $GhostscriptPath "lib\cjk_init.ps"
$CJKInitContent | Out-File -FilePath $CJKInitPath -Encoding UTF8

Write-Host "已创建CJK初始化脚本: $CJKInitPath" -ForegroundColor Yellow

# 3. 验证字体可用性
Write-Host "验证中文字体..." -ForegroundColor Green

$TestFonts = @("simsun.ttc", "simhei.ttf", "kaiti.ttf", "msyh.ttc")
foreach ($font in $TestFonts) {
    $fontPath = Join-Path $FontPath $font
    if (Test-Path $fontPath) {
        Write-Host "✓ $font 可用" -ForegroundColor Green
    } else {
        Write-Host "✗ $font 未找到" -ForegroundColor Red
    }
}

# 4. 测试Ghostscript CJK支持
$TestCommand = "gs -dNOPAUSE -dBATCH -sFONTMAP=`"$FontmapPath`" -c `"/SimSun findfont 12 scalefont setfont 100 100 moveto (测试中文) show showpage`" -sDEVICE=png16m -sOutputFile=cjk_test.png"

Write-Host "执行CJK测试..." -ForegroundColor Green
Write-Host $TestCommand -ForegroundColor Cyan

try {
    Invoke-Expression $TestCommand
    if (Test-Path "cjk_test.png") {
        Write-Host "✓ CJK字体配置成功！" -ForegroundColor Green
        Remove-Item "cjk_test.png" -Force
    }
} catch {
    Write-Host "✗ CJK字体配置失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "CJK字体配置完成！" -ForegroundColor Green
```

#### ICC配置文件安装
```typescript
// icc_profile_installer.ts
class ICCProfileInstaller {
  private readonly profileSources = {
    'ISO Coated v2 (ECI)': 'https://www.eci.org/doku.php?id=en:downloads',
    'GRACoL2006_Coated1v2': 'https://www.idealliance.org/specifications/gracol',
    'Japan Color 2001 Coated': 'https://www.japancolor.org/',
    'Fogra39': 'https://www.fogra.org/en/fogra-standardization/color-standards'
  };

  async installICCProfiles(): Promise<void> {
    const iccDir = './gs/iccprofiles';
    await fs.ensureDir(iccDir);

    // 下载并安装常用ICC配置文件
    for (const [name, url] of Object.entries(this.profileSources)) {
      try {
        console.log(`安装ICC配置文件: ${name}`);
        // 实际实现中需要下载对应的ICC文件
        // await this.downloadICCProfile(url, path.join(iccDir, `${name}.icc`));
      } catch (error) {
        console.warn(`ICC配置文件安装失败: ${name}`, error);
      }
    }

    // 创建ICC配置文件索引
    await this.createICCIndex(iccDir);
  }

  private async createICCIndex(iccDir: string): Promise<void> {
    const indexContent = `
% ICC配置文件索引
systemdict begin
  /ICCProfileDir (${iccDir}) def

  /ICCProfiles <<
    /ISOCoatedv2 (${iccDir}/ISOcoated_v2_eci.icc)
    /GRACoL2006 (${iccDir}/GRACoL2006_Coated1v2.icc)
    /JapanColor2001 (${iccDir}/JapanColor2001Coated.icc)
    /Fogra39 (${iccDir}/Fogra39.icc)
    /sRGB (${iccDir}/sRGB.icc)
  >> def
end
`;

    await fs.writeFile(path.join(iccDir, 'icc_index.ps'), indexContent);
  }
}
```

### 📋 使用说明

#### 基本使用流程
```typescript
// 1. 初始化Ghostscript引擎
const gsEngine = new GhostscriptEngine();
await gsEngine.initialize();

// 2. 处理中文PDF文档
const cjkManager = new GhostscriptCJKFontManager();
await cjkManager.initializeCJKSupport();

const processedFile = await cjkManager.processCJKDocument('./input.pdf', {
  fontEmbedding: 'full',
  fontSubstitution: true,
  encodingFix: true,
  unicodeNormalization: true
});

// 3. 数字印刷优化
const digitalProcessor = new GhostscriptDigitalPrintProcessor();
const optimizedFile = await digitalProcessor.optimizeForDigitalPrint(processedFile, {
  quality: 'high',
  colorSpace: 'CMYK',
  resolution: 300,
  compression: true,
  embedFonts: true,
  preserveTransparency: false,
  printMarks: {
    cropMarks: true,
    bleedMarks: true,
    registrationMarks: true,
    colorBars: false,
    pageInfo: true
  }
});

// 4. 装订处理（骑马订）
const bindingProcessor = new GhostscriptBindingProcessor();
const finalFile = await bindingProcessor.processSaddleStitching(optimizedFile, {
  bindingMargin: 5,
  gutterSize: 10,
  foldMarks: true,
  centerlineGuides: true
});

console.log('文档处理完成:', finalFile);
```

### ⚠️ 重要注意事项

#### 中文字体问题解决方案
1. **确保系统字体完整**: 检查Windows字体目录中是否包含SimSun、SimHei等基础中文字体
2. **正确配置字体映射**: 使用提供的PowerShell脚本自动配置CJK字体映射
3. **字体嵌入策略**: 对于包含中文的PDF，建议使用完整字体嵌入而非子集化
4. **编码问题处理**: 启用Unicode标准化和编码修复功能

#### 性能优化建议
1. **内存配置**: 根据文档大小调整Ghostscript内存参数
2. **并发处理**: 避免同时处理多个大型文档
3. **临时文件管理**: 及时清理处理过程中的临时文件
4. **缓存策略**: 对常用ICC配置文件和字体进行缓存

#### 质量控制要点
1. **颜色管理**: 使用标准ICC配置文件确保颜色准确性
2. **分辨率设置**: 根据输出设备选择合适的分辨率
3. **压缩平衡**: 在文件大小和质量之间找到平衡点
4. **装订精度**: 确保装订处理的精确性和一致性

#### SQLite编译问题解决方案 (Windows环境)

**问题描述**: 在Windows环境下安装`better-sqlite3`时，可能遇到`Error: Could not locate the bindings file.`的运行时错误。

**问题原因**: `better-sqlite3`是一个C++原生模块，`npm install`默认下载和编译的是适配标准Node.js环境的版本，而不是Electron内置的特定版本Node.js。

**解决方案**:

1. **安装electron-rebuild工具**:
   ```bash
   npm install --save-dev electron-rebuild
   ```

2. **执行重新编译**:
   ```bash
   # Windows PowerShell
   .\node_modules\.bin\electron-rebuild.cmd -f -w better-sqlite3

   # 或使用npx
   npx electron-rebuild -f -w better-sqlite3
   ```

3. **配置package.json自动重编译**:
   ```json
   {
     "scripts": {
       "rebuild": "electron-rebuild -f -w better-sqlite3",
       "postinstall": "npm run rebuild"
     }
   }
   ```

4. **验证安装成功**:
   ```javascript
   // 测试SQLite模块是否正常工作
   try {
     const Database = require('better-sqlite3');
     const db = new Database(':memory:');
     console.log('✅ SQLite模块安装成功');
     db.close();
   } catch (error) {
     console.error('❌ SQLite模块安装失败:', error.message);
   }
   ```

**备用方案**: 如果编译仍然失败，可以实现SQLite和内存数据库的双模式支持：

```typescript
// 数据库服务双模式实现
let Database: any = null;
try {
  Database = require('better-sqlite3');
} catch (error) {
  console.warn('SQLite模块加载失败，将使用内存数据库:', error.message);
}

export class DatabaseService {
  private useSQLite: boolean = Database !== null;

  async initialize(): Promise<void> {
    if (this.useSQLite) {
      await this.initializeSQLite();
    } else {
      await this.initializeMemoryDb();
    }
  }
}
```

**成功标志**:
- npm install过程中显示"Rebuilding native modules"
- 应用启动时控制台显示"SQLite数据库初始化完成"
- 订单数据能正常存储和读取
```
```
```
```
}
```

### 智能设备选择
```typescript
class IntelligentDeviceSelector {
  selectOptimalDevice(document: Document, printer: Printer): GhostscriptDevice {
    const analysis = this.analyzeDocument(document);
    const capabilities = this.getPrinterCapabilities(printer);

    // 根据文档特征和打印机能力选择最佳GS设备
    if (analysis.hasPhotos && capabilities.supportsHighRes) {
      return new GhostscriptDevice('tiff48nc', { resolution: 2400 });
    }

    if (analysis.hasSpotColors) {
      return new GhostscriptDevice('tiffsep', { separations: true });
    }

    return new GhostscriptDevice('tiff24nc', { resolution: 600 });
  }
}
```

### 颜色管理系统
```typescript
class ColorManagementSystem {
  // ICC配置文件管理
  private iccProfiles: Map<string, ICCProfile>;

  // 应用颜色管理
  async applyColorManagement(
    inputFile: string,
    outputFile: string,
    profile: ColorProfile
  ): Promise<void> {
    const gsArgs = [
      '-dNOPAUSE', '-dBATCH',
      '-sDEVICE=pdfwrite',
      `-sDefaultRGBProfile=${profile.rgbProfile}`,
      `-sDefaultCMYKProfile=${profile.cmykProfile}`,
      `-sOutputICCProfile=${profile.outputProfile}`,
      `-dRenderIntent=${profile.renderIntent}`,
      `-sOutputFile=${outputFile}`,
      inputFile
    ];

    await this.executeGhostscript(gsArgs);
  }
}
```

## 📱 小程序参数映射

### 参数映射表
| 小程序参数 | 打印端字段 | 数据类型 | 说明 |
|-----------|-----------|---------|------|
| copies | printConfig.copies | number | 打印份数 |
| color | printConfig.colorMode | string | 颜色模式 |
| side | printConfig.printSide | string | 单双面 |
| paperType | printConfig.paperSize | string | 纸张尺寸 |
| binding | bindingConfig.type | string | 装订方式 |
| deliveryType | deliveryInfo.type | string | 配送方式 |

### 参数解析服务
```typescript
class ParameterMappingService {
  // 解析小程序参数
  parseMiniProgramParams(params: any): PrintConfig {
    return {
      copies: params.copies || 1,
      colorMode: this.mapColorMode(params.color),
      printSide: this.mapPrintSide(params.side),
      paperSize: this.mapPaperSize(params.paperType),
      quality: this.mapQuality(params.paperQuality),
      binding: this.mapBinding(params.binding),
      // ... 更多参数映射
    };
  }

  // 颜色模式映射
  private mapColorMode(color: string): ColorMode {
    const mapping = {
      '黑白': 'grayscale',
      '经济彩色': 'color_economy',
      '标准彩色': 'color_standard'
    };
    return mapping[color] || 'grayscale';
  }
}
```

## 📋 智能打印机管理功能总结

### 🎯 核心功能实现

#### 1. **智能打印机选择系统**
- **自动选择**: 基于订单特征（纸张大小、颜色模式、双面打印、装订类型）自动选择最佳打印机
- **手动选择**: 提供打印机列表供用户手动选择，显示打印机能力和适用性标签
- **负载均衡**: 考虑打印机当前任务数和最后使用时间，实现智能负载分配

#### 2. **15台打印机管理**
- **自动检测**: 跨平台检测系统安装的打印机（Windows/Mac/Linux）
- **能力探测**: 通过Ghostscript和系统API探测打印机能力
- **预定义配置**: 内置主流打印机型号的优化配置（HP LaserJet、Canon PIXMA、Epson SureColor等）
- **实时监控**: 监控打印机在线状态、任务队列、使用统计

#### 3. **Ghostscript驱动优化**
- **统一打印API**: 基于Ghostscript的跨平台打印引擎
- **智能设备选择**: 根据文档特征和打印机能力选择最佳Ghostscript设备
- **参数优化**: 自动调整Ghostscript参数以匹配打印机能力和订单要求

#### 4. **打印机分组管理**
- **智能分组**: 按能力自动分组（彩色打印组、黑白打印组、骑马订打印组等）
- **自动分配规则**: 基于文档特征自动分配到对应分组
- **手动管理**: 支持手动创建、编辑、删除打印机分组

### 🏗️ 模块架构决策

#### **打印机管理功能放置位置：系统设置模块**

**理由分析：**
1. **配置性质**: 打印机管理主要是配置和管理功能，符合系统设置的定位
2. **权限控制**: 打印机配置通常需要管理员权限，适合放在系统设置中
3. **使用频率**: 打印机配置不是日常高频操作，更适合作为设置项
4. **功能内聚**: 与其他系统配置（Ghostscript配置、文件路径配置等）形成内聚的设置模块

**具体实现：**
- **主入口**: 系统设置 → 打印机管理
- **子功能**:
  - 打印机发现和检测
  - 打印机列表和详情
  - 打印机分组管理
  - 智能选择配置
  - 打印机测试工具

### 🔄 业务流程集成

#### **新的完整业务流程**
```
新订单同步 → 文件下载 → Ghostscript文档处理 → 标签生成 →
Ghostscript文件合并 → 智能打印机选择 → 打印执行 → 物流发货 → 状态回写
```

#### **智能打印机选择流程**
1. **订单分析**: 解析订单的打印要求（纸张、颜色、装订等）
2. **打印机筛选**: 根据要求筛选符合条件的打印机
3. **负载均衡**: 在符合条件的打印机中选择负载最轻的
4. **Ghostscript优化**: 根据选中打印机优化Ghostscript参数
5. **任务分配**: 将打印任务分配给选中的打印机

### 📊 性能优化

#### **关键性能指标**
- **打印机检测速度**: <5秒完成15台打印机检测
- **智能选择准确率**: >90%的自动选择符合最佳实践
- **负载均衡效果**: 打印机负载差异<20%
- **打印任务分配延迟**: <1秒完成打印机选择和任务分配

#### **优化策略**
- **缓存机制**: 缓存打印机能力信息，减少重复检测
- **异步处理**: 打印机状态监控和统计更新采用异步处理
- **智能预测**: 基于历史数据预测打印机负载，提前进行负载均衡

## 🚀 实施计划

### 📋 总体时间规划：12周完整重构（增加智能打印机管理）

### 第一阶段：基础架构重构 (2周)

#### 🎯 目标：建立稳固的技术基础
1. **数据库重构** (3天)
   - 设计新的数据表结构
   - 编写数据迁移脚本
   - 索引优化和性能测试
   - **验收标准**: 所有数据表创建成功，迁移脚本测试通过

2. **核心服务重构** (5天)
   - OrderService重构（仅本地数据库访问）
   - FileDownloadService实现
   - ConfigurationService实现
   - SyncService基础框架
   - **验收标准**: 所有服务单元测试通过，IPC通信正常

3. **Ghostscript基础集成** (4天)
   - 核心引擎封装
   - 基本文档处理功能
   - 错误处理机制
   - **验收标准**: 能够成功处理PDF文档，生成预览

#### ⚠️ 风险控制
- **数据备份**: 每日自动备份现有数据
- **回滚方案**: 保留原有代码分支，确保可快速回滚
- **渐进式迁移**: 新旧系统并行运行，逐步切换

### 第二阶段：业务逻辑重构 (4周)

#### 🎯 目标：实现核心业务功能
1. **订单管理模块** (1周)
   - 订单列表和详情页面
   - 状态流转管理
   - 订单处理完整流程
   - **验收标准**: 订单CRUD操作正常，状态流转符合业务规则

2. **智能打印机管理模块** (1.5周)
   - 打印机自动检测和能力探测
   - 智能打印机选择算法
   - 15台打印机负载均衡管理
   - 打印机分组和配置管理
   - **验收标准**: 自动检测成功率>95%，智能选择准确率>90%

3. **文件处理模块** (1周)
   - 文件下载和存储管理
   - Ghostscript文档处理流水线
   - 预览生成和缓存
   - **验收标准**: 文件下载成功率>99%，处理速度提升50%

4. **标签生成模块** (1周)
   - 标签模板系统
   - 文件合并功能
   - 装订方式处理（骑马订、胶装、铁圈装）
   - **验收标准**: 标签生成正确，装订处理符合要求

5. **物流发货模块** (0.5周)
   - 扫描设备集成
   - 快递单条码识别
   - 发货状态管理
   - 物流轨迹跟踪
   - **验收标准**: 扫描发货功能正常，状态同步准确

#### 📊 性能基准
- **订单查询**: <100ms
- **文件下载**: >10MB/s
- **文档处理**: <30s/100页
- **标签生成**: <5s

### 第三阶段：界面重构 (2周)

#### 🎯 目标：现代化用户界面
1. **Material UI集成** (3天)
   - 组件库引入和配置
   - 主题定制（参考modernize设计）
   - CSS变量系统实现
   - **验收标准**: UI组件库正常工作，主题风格一致

2. **页面重构** (7天)
   - 订单管理页面（列表、详情、状态管理）
   - 文件处理页面（上传、预览、处理状态）
   - 打印队列页面（任务管理、进度监控）
   - 物流发货管理页面（扫描发货、轨迹跟踪、统计分析）
   - 系统设置页面（配置管理、设备设置）
   - **验收标准**: 所有页面响应式设计，用户体验流畅

#### 🎨 设计要求
- **响应式**: 支持1920x1080到1366x768分辨率
- **无障碍**: 符合WCAG 2.1 AA标准
- **性能**: 页面加载时间<2s

### 第四阶段：高级功能 (2周)

#### 🎯 目标：系统优化和高级特性
1. **打印队列优化** (1周)
   - 智能调度算法（优先级、设备能力匹配）
   - 错误处理和重试机制
   - 并发打印支持
   - **验收标准**: 打印成功率>98%，队列处理效率提升30%

2. **Ghostscript高级功能** (1周)
   - ICC颜色管理系统
   - 智能设备选择和能力检测
   - 多线程处理优化
   - **验收标准**: 颜色准确度提升，处理速度提升60%

#### 🚀 性能目标
- **并发处理**: 支持5个打印任务同时处理
- **内存使用**: <500MB常驻内存
- **CPU使用**: 空闲时<5%，处理时<80%

### 第五阶段：测试和部署 (1周)

#### 🎯 目标：质量保证和生产部署
1. **全面测试** (4天)
   - 单元测试覆盖率>90%
   - 集成测试（所有模块协同工作）
   - 端到端测试（完整业务流程）
   - 性能测试（压力测试、内存泄漏检测）
   - **验收标准**: 所有测试通过，性能指标达标

2. **部署准备** (2天)
   - 生产环境配置
   - 数据迁移验证
   - 用户培训材料
   - 应急预案制定
   - **验收标准**: 部署流程验证通过，回滚方案可用

3. **上线监控** (1天)
   - 系统监控配置
   - 错误日志收集
   - 性能指标监控
   - **验收标准**: 监控系统正常，告警机制有效

2. **性能优化**
   - 数据库查询优化
   - 内存使用优化
   - 响应速度优化

## 📋 验收标准

### 功能验收
- [ ] 订单同步功能正常
- [ ] 文件处理功能完整
- [ ] 标签生成功能正确
- [ ] 打印队列运行稳定
- [ ] 界面美观易用

### 性能验收
- [ ] 订单列表加载时间 < 2秒
- [ ] 文件处理速度提升50%
- [ ] 内存使用优化30%
- [ ] 界面响应时间 < 500ms

### 稳定性验收
- [ ] 7x24小时稳定运行
- [ ] 错误恢复机制完善
- [ ] 数据一致性保证
- [ ] 离线功能正常

## 🔧 开发环境配置

### 环境要求
- Node.js 20.x
- npm 9.x+
- Ghostscript 10.0+
- SQLite 3.x

### 安装步骤
```bash
# 1. 安装依赖
npm install

# 2. 配置Ghostscript
npm run setup-ghostscript

# 3. 初始化数据库
npm run init-database

# 4. 启动开发环境
npm run dev
```

### 开发工具
- **IDE**: VS Code
- **调试**: Electron DevTools
- **测试**: Vitest
- **构建**: Electron Builder

## 📝 详细实施指南

### 阶段一：数据库重构实施

#### 1.1 数据库迁移脚本
```typescript
// src/main/database/migrations/001_initial_schema.ts
export class InitialSchemaMigration {
  async up(db: Database): Promise<void> {
    // 创建订单表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id TEXT UNIQUE NOT NULL,
        order_no TEXT NOT NULL,
        user_name TEXT NOT NULL,
        user_phone TEXT NOT NULL,
        status TEXT DEFAULT '待处理',
        print_config TEXT NOT NULL,
        binding_config TEXT,
        print_price REAL DEFAULT 0,
        binding_price REAL DEFAULT 0,
        delivery_price REAL DEFAULT 0,
        original_price REAL DEFAULT 0,
        discount REAL DEFAULT 0,
        total_price REAL DEFAULT 0,
        delivery_type TEXT DEFAULT 'express',
        delivery_address TEXT,
        create_time INTEGER NOT NULL,
        update_time INTEGER NOT NULL,
        cloud_sync_status TEXT DEFAULT '待同步',
        cloud_sync_time INTEGER,
        remarks TEXT,
        internal_notes TEXT
      )
    `);

    // 创建文件表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        cloud_file_id TEXT,
        file_name TEXT NOT NULL,
        original_name TEXT,
        file_size INTEGER,
        file_type TEXT,
        local_path TEXT,
        cloud_url TEXT,
        download_status TEXT DEFAULT '待下载',
        status TEXT DEFAULT '待处理',
        page_count INTEGER,
        gs_processed_path TEXT,
        gs_preview_path TEXT,
        gs_analysis_result TEXT,
        process_history TEXT,
        create_time INTEGER NOT NULL,
        update_time INTEGER NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders(id)
      )
    `);

    // 创建打印任务表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS print_jobs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        job_id TEXT UNIQUE NOT NULL,
        order_id INTEGER NOT NULL,
        file_id INTEGER NOT NULL,
        printer_name TEXT NOT NULL,
        status TEXT DEFAULT '队列中',
        priority INTEGER DEFAULT 5,
        print_config TEXT NOT NULL,
        copies INTEGER DEFAULT 1,
        gs_device TEXT,
        gs_options TEXT,
        start_time INTEGER,
        end_time INTEGER,
        progress INTEGER DEFAULT 0,
        error_message TEXT,
        create_time INTEGER NOT NULL,
        update_time INTEGER NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders(id),
        FOREIGN KEY (file_id) REFERENCES files(id)
      )
    `);

    // 创建索引
    await db.exec('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_orders_create_time ON orders(create_time)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_files_order_id ON files(order_id)');
    await db.exec('CREATE INDEX IF NOT EXISTS idx_print_jobs_status ON print_jobs(status)');
  }

  async down(db: Database): Promise<void> {
    await db.exec('DROP TABLE IF EXISTS print_jobs');
    await db.exec('DROP TABLE IF EXISTS files');
    await db.exec('DROP TABLE IF EXISTS orders');
  }
}
```

#### 1.2 数据迁移工具
```typescript
// src/main/database/migrator.ts
export class DatabaseMigrator {
  private migrations: Migration[] = [
    new InitialSchemaMigration(),
    // 后续迁移...
  ];

  async migrate(db: Database): Promise<void> {
    // 创建迁移记录表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        executed_at INTEGER NOT NULL
      )
    `);

    // 获取已执行的迁移
    const executedMigrations = await db.all(
      'SELECT name FROM migrations ORDER BY id'
    );
    const executedNames = executedMigrations.map(m => m.name);

    // 执行未执行的迁移
    for (const migration of this.migrations) {
      const migrationName = migration.constructor.name;
      if (!executedNames.includes(migrationName)) {
        console.log(`执行迁移: ${migrationName}`);
        await migration.up(db);
        await db.run(
          'INSERT INTO migrations (name, executed_at) VALUES (?, ?)',
          [migrationName, Date.now()]
        );
      }
    }
  }
}
```

### 阶段二：核心服务重构实施

#### 2.1 订单服务重构
```typescript
// src/main/services/order-service.ts
export class OrderService {
  constructor(
    private db: DatabaseService,
    private parameterMapping: ParameterMappingService
  ) {}

  async getOrderList(options: QueryOptions): Promise<PaginatedResult<Order>> {
    const { page = 1, pageSize = 20, status, dateRange, keyword } = options;

    let sql = 'SELECT * FROM orders WHERE 1=1';
    const params: any[] = [];

    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }

    if (dateRange) {
      sql += ' AND create_time BETWEEN ? AND ?';
      params.push(dateRange.start, dateRange.end);
    }

    if (keyword) {
      sql += ' AND (order_no LIKE ? OR user_name LIKE ? OR user_phone LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    sql += ' ORDER BY create_time DESC LIMIT ? OFFSET ?';
    params.push(pageSize, (page - 1) * pageSize);

    const orders = await this.db.all(sql, params);
    const total = await this.getOrderCount(options);

    return {
      data: orders.map(this.mapDbOrderToOrder),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async createOrderFromMiniProgram(miniProgramData: any): Promise<Order> {
    const printConfig = await this.parameterMapping.parseMiniProgramParams(
      miniProgramData.printParams
    );

    const order: Partial<Order> = {
      orderId: miniProgramData.orderId,
      orderNo: miniProgramData.orderNo,
      userName: miniProgramData.userInfo.name,
      userPhone: miniProgramData.userInfo.phone,
      status: '待处理',
      printConfig: JSON.stringify(printConfig),
      bindingConfig: JSON.stringify(miniProgramData.bindingConfig),
      printPrice: miniProgramData.pricing.printPrice,
      bindingPrice: miniProgramData.pricing.bindingPrice,
      deliveryPrice: miniProgramData.pricing.deliveryPrice,
      originalPrice: miniProgramData.pricing.originalPrice,
      discount: miniProgramData.pricing.discount,
      totalPrice: miniProgramData.pricing.totalPrice,
      deliveryType: miniProgramData.delivery.type,
      deliveryAddress: miniProgramData.delivery.address,
      remarks: miniProgramData.remarks,
      createTime: Date.now(),
      updateTime: Date.now()
    };

    const result = await this.db.run(
      `INSERT INTO orders (
        order_id, order_no, user_name, user_phone, status,
        print_config, binding_config, print_price, binding_price,
        delivery_price, original_price, discount, total_price,
        delivery_type, delivery_address, remarks, create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        order.orderId, order.orderNo, order.userName, order.userPhone, order.status,
        order.printConfig, order.bindingConfig, order.printPrice, order.bindingPrice,
        order.deliveryPrice, order.originalPrice, order.discount, order.totalPrice,
        order.deliveryType, order.deliveryAddress, order.remarks,
        order.createTime, order.updateTime
      ]
    );

    return { ...order, id: result.lastID } as Order;
  }

  async updateOrderStatus(
    orderId: string,
    status: OrderStatus,
    notes?: string
  ): Promise<boolean> {
    const updateTime = Date.now();

    await this.db.run(
      'UPDATE orders SET status = ?, update_time = ?, internal_notes = ? WHERE order_id = ?',
      [status, updateTime, notes || '', orderId]
    );

    // 标记订单需要同步到云端 (不直接访问云端)
    await this.markOrderForSync(orderId, 'update_status', { status, notes, updateTime });
  }

  // 标记订单需要同步 - 直接写入同步队列表
  private async markOrderForSync(orderId: string, action: string = 'update_status', data: any = {}): Promise<void> {
    const now = Date.now();
    const itemId = `${orderId}_${action}_${now}`;

    await this.db.run(
      `INSERT INTO sync_queue (item_id, item_type, order_id, action, data, priority, create_time, update_time)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [itemId, 'order_status', orderId, action, JSON.stringify(data), 5, now, now]
      priority: 'normal',
      retryCount: 0,
      maxRetries: 3,
      status: 'pending'
    };

    await this.db.run(
      'INSERT INTO sync_queue (type, order_id, action, data, priority, retry_count, max_retries, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [syncItem.type, syncItem.orderId, syncItem.action, JSON.stringify(syncItem.data), syncItem.priority, syncItem.retryCount, syncItem.maxRetries, syncItem.status, Date.now()]
    );

    return true;
  }

  private mapDbOrderToOrder(dbOrder: any): Order {
    return {
      id: dbOrder.id,
      orderId: dbOrder.order_id,
      orderNo: dbOrder.order_no,
      userName: dbOrder.user_name,
      userPhone: dbOrder.user_phone,
      status: dbOrder.status,
      printConfig: JSON.parse(dbOrder.print_config || '{}'),
      bindingConfig: JSON.parse(dbOrder.binding_config || '{}'),
      printPrice: dbOrder.print_price,
      bindingPrice: dbOrder.binding_price,
      deliveryPrice: dbOrder.delivery_price,
      originalPrice: dbOrder.original_price,
      discount: dbOrder.discount,
      totalPrice: dbOrder.total_price,
      deliveryType: dbOrder.delivery_type,
      deliveryAddress: dbOrder.delivery_address,
      remarks: dbOrder.remarks,
      internalNotes: dbOrder.internal_notes,
      createTime: dbOrder.create_time,
      updateTime: dbOrder.update_time,
      cloudSyncStatus: dbOrder.cloud_sync_status,
      cloudSyncTime: dbOrder.cloud_sync_time
    };
  }
}
```

## 📋 Ghostscript文件合并核心要点总结

### 🎯 关键技术特性

#### 1. **完整业务流程中的Ghostscript应用**
```
新订单同步 → 文件下载 → Ghostscript文档处理 → 标签生成 → Ghostscript文件合并 → 打印执行 → 物流发货 → 状态回写
```

**详细流程说明**：
- **新订单同步**: 从云端拉取新订单到本地数据库
- **文件下载**: 分片并行下载订单相关文件
- **Ghostscript文档处理**: PDF优化、颜色管理、格式转换
- **标签生成**: 根据订单信息生成个性化标签页
- **Ghostscript文件合并**: 将标签页与处理后文档合并为单一PDF
- **打印执行**: 发送合并后文件到指定打印机
- **物流发货**: 扫描快递单条码，记录物流信息，更新发货状态
- **状态回写**: 发货状态同步到本地数据库和云端，客户可查看物流进度

#### 2. **Ghostscript文件合并的核心优势**
- **专业PDF处理**: 使用Ghostscript专业PDF引擎确保合并质量
- **高级压缩优化**: 通过Ghostscript实现文件大小和质量的最佳平衡
- **字体处理**: 完善的中文字体支持和字体嵌入/子集化
- **颜色管理**: ICC颜色配置文件支持，确保打印颜色准确性
- **质量控制**: 多层次的PDF验证和错误检测机制

#### 3. **Ghostscript合并命令示例**
```bash
gs -dNOPAUSE -dBATCH -dSAFER \
   -sDEVICE=pdfwrite \
   -dPDFSETTINGS=/printer \
   -dCompatibilityLevel=1.4 \
   -dEmbedAllFonts=true \
   -dSubsetFonts=true \
   -dCompressFonts=true \
   -dColorImageResolution=300 \
   -dJPEGQ=85 \
   -sOutputFile=merged_output.pdf \
   label.pdf document1.pdf document2.pdf
```

#### 4. **实施关键点**
1. **统一使用Ghostscript**: 所有PDF处理和合并操作都通过Ghostscript实现
2. **配置标准化**: 建立统一的Ghostscript参数配置标准
3. **质量保证**: 每次合并后都进行Ghostscript验证
4. **性能优化**: 合理配置Ghostscript参数以平衡质量和性能
5. **错误处理**: 完善的Ghostscript错误检测和恢复机制

通过Ghostscript实现的文件合并确保了整个打印端系统的专业性和可靠性，为高质量的数字印刷服务提供了坚实的技术基础。

## 🔍 Business Flow深度优化建议

> **📋 基于Business Flow分析的系统优化方案**
> 本章节基于对现有业务流程的深入分析，提出了关键的逻辑优化和代码实现改进建议，旨在提升系统的可靠性、性能和用户体验。

### 🎯 优化优先级分级

#### **高优先级优化** (核心业务逻辑)
1. **订单状态流转逻辑优化** - 提升业务流程可靠性
2. **Ghostscript处理流程的错误恢复** - 确保文档处理稳定性
3. **数据同步机制的可靠性优化** - 保证数据一致性

#### **中优先级优化** (系统性能提升)
4. **文件下载系统的容错机制** - 提高下载成功率
5. **打印队列的智能调度优化** - 优化资源分配
6. **系统监控和告警机制** - 主动发现和处理问题

#### **低优先级优化** (用户体验改善)
7. **物流发货流程的自动化优化** - 减少人工干预
8. **用户体验优化** - 提升操作友好性

---

## 🔄 1. 订单状态流转逻辑优化

### 📊 当前问题分析
- **状态粒度不足**: 缺少中间状态的细化管理，难以准确跟踪订单进度
- **异常处理缺失**: 没有失败状态和状态回退机制
- **状态转换规则不完善**: 缺少严格的状态转换验证

### 🎯 优化方案

#### 增强状态枚举定义
```typescript
enum OrderStatus {
  // 基础状态
  PENDING_PAYMENT = 'pending_payment',
  PENDING_PROCESS = 'pending_process',

  // 下载阶段
  DOWNLOADING = 'downloading',
  DOWNLOAD_FAILED = 'download_failed',

  // 处理阶段
  PROCESSING = 'processing',
  PROCESSING_FAILED = 'processing_failed',      // 新增

  // 打印阶段
  PENDING_PRINT = 'pending_print',
  PRINTING = 'printing',
  PRINT_COMPLETED = 'print_completed',
  PRINT_FAILED = 'print_failed',               // 新增

  // 装订阶段
  PENDING_BINDING = 'pending_binding',
  BINDING = 'binding',
  BINDING_FAILED = 'binding_failed',           // 新增

  // 发货阶段
  PENDING_DELIVERY = 'pending_delivery',
  SHIPPED = 'shipped',
  IN_TRANSIT = 'in_transit',
  DELIVERED = 'delivered',
  DELIVERY_FAILED = 'delivery_failed',         // 新增

  // 终态
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}
```

#### 状态管理器实现
```typescript
class OrderStatusManager {
  // 定义状态转换规则
  private statusTransitions: Map<OrderStatus, OrderStatus[]> = new Map([
    [OrderStatus.PENDING_PROCESS, [OrderStatus.DOWNLOADING, OrderStatus.CANCELLED]],
    [OrderStatus.DOWNLOADING, [OrderStatus.PROCESSING, OrderStatus.DOWNLOAD_FAILED]],
    [OrderStatus.DOWNLOAD_FAILED, [OrderStatus.DOWNLOADING, OrderStatus.CANCELLED]],
    [OrderStatus.PROCESSING, [OrderStatus.PENDING_PRINT, OrderStatus.PROCESSING_FAILED]],
    [OrderStatus.PROCESSING_FAILED, [OrderStatus.PROCESSING, OrderStatus.CANCELLED]],
    [OrderStatus.PENDING_PRINT, [OrderStatus.PRINTING, OrderStatus.CANCELLED]],
    [OrderStatus.PRINTING, [OrderStatus.PRINT_COMPLETED, OrderStatus.PRINT_FAILED]],
    [OrderStatus.PRINT_FAILED, [OrderStatus.PRINTING, OrderStatus.CANCELLED]],
    [OrderStatus.PRINT_COMPLETED, [OrderStatus.PENDING_BINDING, OrderStatus.PENDING_DELIVERY]],
    [OrderStatus.PENDING_BINDING, [OrderStatus.BINDING, OrderStatus.CANCELLED]],
    [OrderStatus.BINDING, [OrderStatus.PENDING_DELIVERY, OrderStatus.BINDING_FAILED]],
    [OrderStatus.BINDING_FAILED, [OrderStatus.BINDING, OrderStatus.CANCELLED]],
    [OrderStatus.PENDING_DELIVERY, [OrderStatus.SHIPPED, OrderStatus.CANCELLED]],
    [OrderStatus.SHIPPED, [OrderStatus.IN_TRANSIT, OrderStatus.DELIVERY_FAILED]],
    [OrderStatus.IN_TRANSIT, [OrderStatus.DELIVERED, OrderStatus.DELIVERY_FAILED]],
    [OrderStatus.DELIVERY_FAILED, [OrderStatus.SHIPPED, OrderStatus.REFUNDED]],
    [OrderStatus.DELIVERED, [OrderStatus.COMPLETED]]
  ]);

  // 验证状态转换是否合法
  validateStatusTransition(currentStatus: OrderStatus, targetStatus: OrderStatus): boolean {
    const allowedTransitions = this.statusTransitions.get(currentStatus) || [];
    return allowedTransitions.includes(targetStatus);
  }

  // 状态转换执行
  async transitionStatus(
    orderId: string,
    targetStatus: OrderStatus,
    reason?: string,
    metadata?: any
  ): Promise<StatusTransitionResult> {
    const order = await this.orderService.getOrder(orderId);

    // 验证转换合法性
    if (!this.validateStatusTransition(order.status, targetStatus)) {
      return {
        success: false,
        error: `非法状态转换: ${order.status} -> ${targetStatus}`,
        currentStatus: order.status
      };
    }

    try {
      // 执行状态转换
      await this.orderService.updateOrderStatus(orderId, targetStatus, reason);

      // 记录状态转换历史
      await this.recordStatusHistory(orderId, {
        fromStatus: order.status,
        toStatus: targetStatus,
        reason,
        metadata,
        timestamp: Date.now(),
        operator: 'SYSTEM'
      });

      // 触发状态转换后的业务逻辑
      await this.triggerStatusTransitionHandlers(orderId, order.status, targetStatus);

      return {
        success: true,
        previousStatus: order.status,
        currentStatus: targetStatus
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        currentStatus: order.status
      };
    }
  }

  // 状态回退机制
  async rollbackStatus(
    orderId: string,
    targetStatus: OrderStatus,
    reason: string
  ): Promise<boolean> {
    const order = await this.orderService.getOrder(orderId);
    const statusHistory = await this.getStatusHistory(orderId);

    // 验证回退目标状态是否在历史中存在
    const hasBeenInTargetStatus = statusHistory.some(h => h.toStatus === targetStatus);
    if (!hasBeenInTargetStatus) {
      throw new Error(`订单从未处于状态: ${targetStatus}`);
    }

    // 执行回退
    const result = await this.transitionStatus(orderId, targetStatus, `回退: ${reason}`);

    if (result.success) {
      // 记录回退操作
      await this.recordRollbackOperation(orderId, {
        fromStatus: order.status,
        toStatus: targetStatus,
        reason,
        timestamp: Date.now(),
        operator: 'SYSTEM_ROLLBACK'
      });
    }

    return result.success;
  }

  // 状态转换后的业务逻辑触发器
  private async triggerStatusTransitionHandlers(
    orderId: string,
    fromStatus: OrderStatus,
    toStatus: OrderStatus
  ): Promise<void> {
    const handlers = this.getStatusTransitionHandlers(toStatus);

    for (const handler of handlers) {
      try {
        await handler.execute(orderId, fromStatus, toStatus);
      } catch (error) {
        console.error(`状态转换处理器执行失败: ${handler.name}`, error);
        // 记录错误但不阻断流程
      }
    }
  }

  // 获取状态转换处理器
  private getStatusTransitionHandlers(status: OrderStatus): StatusTransitionHandler[] {
    const handlerMap = {
      [OrderStatus.DOWNLOADING]: [new FileDownloadHandler()],
      [OrderStatus.PROCESSING]: [new DocumentProcessingHandler()],
      [OrderStatus.PRINTING]: [new PrintJobHandler()],
      [OrderStatus.SHIPPED]: [new LogisticsNotificationHandler()],
      [OrderStatus.COMPLETED]: [new OrderCompletionHandler()]
    };

    return handlerMap[status] || [];
  }
}

// 状态转换处理器接口
interface StatusTransitionHandler {
  name: string;
  execute(orderId: string, fromStatus: OrderStatus, toStatus: OrderStatus): Promise<void>;
}

// 状态转换结果
interface StatusTransitionResult {
  success: boolean;
  previousStatus?: OrderStatus;
  currentStatus: OrderStatus;
  error?: string;
}

// 状态历史记录
interface StatusHistoryRecord {
  fromStatus: OrderStatus;
  toStatus: OrderStatus;
  reason?: string;
  metadata?: any;
  timestamp: number;
  operator: string;
}
```

#### 状态转换处理器实现示例
```typescript
// 文件下载处理器
class FileDownloadHandler implements StatusTransitionHandler {
  name = 'FileDownloadHandler';

  async execute(orderId: string, fromStatus: OrderStatus, toStatus: OrderStatus): Promise<void> {
    if (toStatus === OrderStatus.DOWNLOADING) {
      // 启动文件下载任务
      const order = await this.orderService.getOrder(orderId);
      await this.fileDownloadService.startDownload(order);
    }
  }
}

// 文档处理处理器
class DocumentProcessingHandler implements StatusTransitionHandler {
  name = 'DocumentProcessingHandler';

  async execute(orderId: string, fromStatus: OrderStatus, toStatus: OrderStatus): Promise<void> {
    if (toStatus === OrderStatus.PROCESSING) {
      // 启动Ghostscript文档处理
      const order = await this.orderService.getOrder(orderId);
      await this.ghostscriptService.startProcessing(order);
    }
  }
}

// 打印任务处理器
class PrintJobHandler implements StatusTransitionHandler {
  name = 'PrintJobHandler';

  async execute(orderId: string, fromStatus: OrderStatus, toStatus: OrderStatus): Promise<void> {
    if (toStatus === OrderStatus.PRINTING) {
      // 创建并启动打印任务
      const order = await this.orderService.getOrder(orderId);
      const printJob = await this.printQueueService.createPrintJob(order);
      await this.printQueueService.startPrinting(printJob.id);
    }
  }
}
```

### 📈 优化效果预期
- **状态跟踪精度提升**: 从8个状态增加到17个状态，提供更精确的进度跟踪
- **异常处理能力**: 新增6个失败状态，提供完整的异常处理流程
- **状态回退支持**: 支持订单状态回退，提高系统容错能力
- **业务逻辑解耦**: 通过处理器模式实现状态转换与业务逻辑的解耦

---

## 🛠️ 2. Ghostscript处理流程的错误恢复

### 📊 当前问题分析
- **错误处理单一**: Ghostscript处理失败时缺少分类处理和自动恢复
- **中文字体问题**: 中文字体缺失或编码问题处理不完善
- **内存管理**: 大文件处理时容易出现内存溢出

### 🎯 优化方案

#### 错误分类和恢复策略
```typescript
// Ghostscript错误类型定义
enum GhostscriptErrorType {
  FONT_NOT_FOUND = 'font_not_found',
  MEMORY_ERROR = 'memory_error',
  ENCODING_ERROR = 'encoding_error',
  COLOR_SPACE_ERROR = 'color_space_error',
  FILE_CORRUPTION = 'file_corruption',
  PERMISSION_ERROR = 'permission_error',
  DEVICE_ERROR = 'device_error',
  GENERIC_ERROR = 'generic_error'
}

// 错误恢复策略
interface RecoveryStrategy {
  type: GhostscriptErrorType;
  maxRetries: number;
  recoveryMethods: RecoveryMethod[];
  fallbackOptions: FallbackOption[];
}

class GhostscriptErrorRecoveryService {
  private recoveryStrategies: Map<GhostscriptErrorType, RecoveryStrategy> = new Map([
    [GhostscriptErrorType.FONT_NOT_FOUND, {
      type: GhostscriptErrorType.FONT_NOT_FOUND,
      maxRetries: 3,
      recoveryMethods: [
        new FontSubstitutionMethod(),
        new FontEmbeddingMethod(),
        new FontDownloadMethod()
      ],
      fallbackOptions: [
        new RasterizationFallback(),
        new SimpleFontFallback()
      ]
    }],
    [GhostscriptErrorType.MEMORY_ERROR, {
      type: GhostscriptErrorType.MEMORY_ERROR,
      maxRetries: 2,
      recoveryMethods: [
        new MemoryOptimizationMethod(),
        new BandProcessingMethod(),
        new FileSegmentationMethod()
      ],
      fallbackOptions: [
        new LowQualityProcessingFallback(),
        new ExternalProcessingFallback()
      ]
    }],
    [GhostscriptErrorType.ENCODING_ERROR, {
      type: GhostscriptErrorType.ENCODING_ERROR,
      maxRetries: 3,
      recoveryMethods: [
        new EncodingConversionMethod(),
        new UnicodeNormalizationMethod(),
        new CharsetDetectionMethod()
      ],
      fallbackOptions: [
        new TextExtractionFallback(),
        new ImageConversionFallback()
      ]
    }]
  ]);

  // 主要错误恢复入口
  async processWithRecovery(
    inputFile: string,
    options: ProcessingOptions
  ): Promise<ProcessingResult> {
    let lastError: Error;
    let attemptCount = 0;
    const maxGlobalRetries = 5;

    while (attemptCount < maxGlobalRetries) {
      try {
        // 尝试标准处理
        return await this.standardProcess(inputFile, options);

      } catch (error) {
        lastError = error;
        attemptCount++;

        // 分析错误类型
        const errorType = this.analyzeError(error);
        const strategy = this.recoveryStrategies.get(errorType);

        if (!strategy) {
          // 未知错误类型，尝试通用恢复
          const result = await this.attemptGenericRecovery(inputFile, options, error);
          if (result.success) {
            return result;
          }
          continue;
        }

        // 执行特定错误类型的恢复策略
        const recoveryResult = await this.executeRecoveryStrategy(
          inputFile,
          options,
          strategy,
          error
        );

        if (recoveryResult.success) {
          return recoveryResult;
        }

        // 如果恢复失败，尝试回退选项
        const fallbackResult = await this.executeFallbackOptions(
          inputFile,
          options,
          strategy,
          error
        );

        if (fallbackResult.success) {
          return fallbackResult;
        }
      }
    }

    // 所有恢复尝试都失败
    throw new Error(`Ghostscript处理失败，已尝试${attemptCount}次恢复: ${lastError.message}`);
  }

  // 执行恢复策略
  private async executeRecoveryStrategy(
    inputFile: string,
    options: ProcessingOptions,
    strategy: RecoveryStrategy,
    originalError: Error
  ): Promise<ProcessingResult> {
    for (const method of strategy.recoveryMethods) {
      try {
        console.log(`尝试恢复方法: ${method.name}`);

        const modifiedOptions = await method.modifyOptions(options, originalError);
        const result = await this.standardProcess(inputFile, modifiedOptions);

        // 记录成功的恢复方法
        await this.recordSuccessfulRecovery(inputFile, method.name, originalError);

        return result;

      } catch (recoveryError) {
        console.warn(`恢复方法失败: ${method.name}`, recoveryError);
        // 继续尝试下一个恢复方法
      }
    }

    return { success: false, error: '所有恢复方法都失败' };
  }

  // 中文字体错误恢复
  private async recoverFromFontError(
    inputFile: string,
    options: ProcessingOptions,
    originalError: Error
  ): Promise<ProcessingResult> {
    const recoveryAttempts = [
      // 1. 字体替换恢复
      async () => {
        const fontSubstitutionOptions = {
          ...options,
          fontSubstitution: true,
          fallbackFont: 'SimSun',
          fontMapping: this.getChineseFontMapping()
        };
        return await this.standardProcess(inputFile, fontSubstitutionOptions);
      },

      // 2. 字体嵌入恢复
      async () => {
        const fontEmbeddingOptions = {
          ...options,
          embedAllFonts: true,
          subsetFonts: false,
          fontPath: 'C:/Windows/Fonts'
        };
        return await this.standardProcess(inputFile, fontEmbeddingOptions);
      },

      // 3. 字体下载和安装
      async () => {
        await this.downloadMissingFonts(originalError);
        return await this.standardProcess(inputFile, options);
      },

      // 4. 文本转图像回退
      async () => {
        const rasterizationOptions = {
          ...options,
          rasterizeText: true,
          textResolution: 300
        };
        return await this.standardProcess(inputFile, rasterizationOptions);
      }
    ];

    for (let i = 0; i < recoveryAttempts.length; i++) {
      try {
        console.log(`尝试字体错误恢复方法 ${i + 1}/${recoveryAttempts.length}`);
        const result = await recoveryAttempts[i]();

        console.log(`字体错误恢复成功，使用方法 ${i + 1}`);
        return result;

      } catch (recoveryError) {
        console.warn(`字体错误恢复方法 ${i + 1} 失败:`, recoveryError);

        if (i === recoveryAttempts.length - 1) {
          // 最后一次尝试也失败了
          throw new Error(`字体错误恢复失败: ${recoveryError.message}`);
        }
      }
    }
  }

  // 内存错误恢复
  private async recoverFromMemoryError(
    inputFile: string,
    options: ProcessingOptions,
    originalError: Error
  ): Promise<ProcessingResult> {
    const memoryRecoveryAttempts = [
      // 1. 减少内存使用
      async () => {
        const lowMemoryOptions = {
          ...options,
          maxMemory: '256m',
          bufferSize: '64m',
          bandBufferSpace: '32m',
          enableGC: true
        };
        return await this.standardProcess(inputFile, lowMemoryOptions);
      },

      // 2. 分带处理
      async () => {
        const bandProcessingOptions = {
          ...options,
          useBandProcessing: true,
          bandHeight: 100,
          maxBitmap: '100m'
        };
        return await this.standardProcess(inputFile, bandProcessingOptions);
      },

      // 3. 文件分段处理
      async () => {
        const segments = await this.segmentFile(inputFile);
        const processedSegments = [];

        for (const segment of segments) {
          const result = await this.standardProcess(segment, options);
          processedSegments.push(result.outputFile);
        }

        // 合并分段结果
        return await this.mergeSegments(processedSegments);
      },

      // 4. 降低质量处理
      async () => {
        const lowQualityOptions = {
          ...options,
          resolution: Math.max(150, options.resolution / 2),
          imageCompression: 'high',
          colorImageResolution: 150
        };
        return await this.standardProcess(inputFile, lowQualityOptions);
      }
    ];

    for (let i = 0; i < memoryRecoveryAttempts.length; i++) {
      try {
        console.log(`尝试内存错误恢复方法 ${i + 1}/${memoryRecoveryAttempts.length}`);
        const result = await memoryRecoveryAttempts[i]();

        console.log(`内存错误恢复成功，使用方法 ${i + 1}`);
        return result;

      } catch (recoveryError) {
        console.warn(`内存错误恢复方法 ${i + 1} 失败:`, recoveryError);

        if (i === memoryRecoveryAttempts.length - 1) {
          throw new Error(`内存错误恢复失败: ${recoveryError.message}`);
        }
      }
    }
  }

  // 编码错误恢复
  private async recoverFromEncodingError(
    inputFile: string,
    options: ProcessingOptions,
    originalError: Error
  ): Promise<ProcessingResult> {
    const encodingRecoveryAttempts = [
      // 1. 编码转换
      async () => {
        const convertedFile = await this.convertFileEncoding(inputFile, 'UTF-8');
        return await this.standardProcess(convertedFile, options);
      },

      // 2. Unicode标准化
      async () => {
        const normalizedFile = await this.normalizeUnicode(inputFile);
        return await this.standardProcess(normalizedFile, options);
      },

      // 3. 字符集检测和修复
      async () => {
        const detectedEncoding = await this.detectCharset(inputFile);
        const repairedFile = await this.repairCharset(inputFile, detectedEncoding);
        return await this.standardProcess(repairedFile, options);
      },

      // 4. 文本提取重建
      async () => {
        const extractedText = await this.extractText(inputFile);
        const rebuiltFile = await this.rebuildPDFFromText(extractedText, options);
        return await this.standardProcess(rebuiltFile, options);
      }
    ];

    for (let i = 0; i < encodingRecoveryAttempts.length; i++) {
      try {
        console.log(`尝试编码错误恢复方法 ${i + 1}/${encodingRecoveryAttempts.length}`);
        const result = await encodingRecoveryAttempts[i]();

        console.log(`编码错误恢复成功，使用方法 ${i + 1}`);
        return result;

      } catch (recoveryError) {
        console.warn(`编码错误恢复方法 ${i + 1} 失败:`, recoveryError);

        if (i === encodingRecoveryAttempts.length - 1) {
          throw new Error(`编码错误恢复失败: ${recoveryError.message}`);
        }
      }
    }
  }

  // 错误类型分析
  private analyzeError(error: Error): GhostscriptErrorType {
    const errorMessage = error.message.toLowerCase();

    if (errorMessage.includes('font') || errorMessage.includes('字体')) {
      return GhostscriptErrorType.FONT_NOT_FOUND;
    }

    if (errorMessage.includes('memory') || errorMessage.includes('内存')) {
      return GhostscriptErrorType.MEMORY_ERROR;
    }

    if (errorMessage.includes('encoding') || errorMessage.includes('编码')) {
      return GhostscriptErrorType.ENCODING_ERROR;
    }

    if (errorMessage.includes('color') || errorMessage.includes('颜色')) {
      return GhostscriptErrorType.COLOR_SPACE_ERROR;
    }

    if (errorMessage.includes('corrupt') || errorMessage.includes('损坏')) {
      return GhostscriptErrorType.FILE_CORRUPTION;
    }

    if (errorMessage.includes('permission') || errorMessage.includes('权限')) {
      return GhostscriptErrorType.PERMISSION_ERROR;
    }

    if (errorMessage.includes('device') || errorMessage.includes('设备')) {
      return GhostscriptErrorType.DEVICE_ERROR;
    }

    return GhostscriptErrorType.GENERIC_ERROR;
  }

  // 获取中文字体映射
  private getChineseFontMapping(): FontMapping {
    return {
      // 常见中文字体映射
      '宋体': 'SimSun',
      '黑体': 'SimHei',
      '楷体': 'KaiTi',
      '仿宋': 'FangSong',
      '微软雅黑': 'Microsoft YaHei',

      // Adobe字体映射
      'AdobeSongStd-Light': 'SimSun',
      'AdobeHeitiStd-Regular': 'SimHei',
      'AdobeKaitiStd-Regular': 'KaiTi',
      'AdobeFangsongStd-Regular': 'FangSong',

      // 其他常见字体
      'STSong-Light': 'SimSun',
      'STHeiti-Regular': 'SimHei',
      'STKaiti-Regular': 'KaiTi',
      'STFangsong-Light': 'FangSong'
    };
  }

  // 记录成功的恢复方法
  private async recordSuccessfulRecovery(
    inputFile: string,
    recoveryMethod: string,
    originalError: Error
  ): Promise<void> {
    const recoveryRecord = {
      inputFile,
      recoveryMethod,
      originalError: originalError.message,
      timestamp: Date.now(),
      success: true
    };

    await this.db.run(
      'INSERT INTO ghostscript_recovery_log (input_file, recovery_method, original_error, timestamp, success) VALUES (?, ?, ?, ?, ?)',
      [recoveryRecord.inputFile, recoveryRecord.recoveryMethod, recoveryRecord.originalError, recoveryRecord.timestamp, recoveryRecord.success]
    );
  }
}

// 恢复方法接口
interface RecoveryMethod {
  name: string;
  modifyOptions(options: ProcessingOptions, error: Error): Promise<ProcessingOptions>;
}

// 字体替换恢复方法
class FontSubstitutionMethod implements RecoveryMethod {
  name = 'FontSubstitution';

  async modifyOptions(options: ProcessingOptions, error: Error): Promise<ProcessingOptions> {
    return {
      ...options,
      fontSubstitution: true,
      fallbackFont: 'SimSun',
      fontMapping: {
        // 从错误信息中提取缺失字体并映射
        ...this.extractMissingFontsFromError(error)
      }
    };
  }

  private extractMissingFontsFromError(error: Error): FontMapping {
    // 从错误信息中提取缺失的字体名称
    const fontRegex = /font[:\s]+([^\s,]+)/gi;
    const matches = error.message.match(fontRegex);

    const mapping: FontMapping = {};
    if (matches) {
      matches.forEach(match => {
        const fontName = match.replace(/font[:\s]+/i, '');
        mapping[fontName] = 'SimSun'; // 默认替换为宋体
      });
    }

    return mapping;
  }
}

// 内存优化恢复方法
class MemoryOptimizationMethod implements RecoveryMethod {
  name = 'MemoryOptimization';

  async modifyOptions(options: ProcessingOptions, error: Error): Promise<ProcessingOptions> {
    return {
      ...options,
      maxMemory: '256m',
      bufferSize: '64m',
      bandBufferSpace: '32m',
      enableGC: true,
      useBandProcessing: true
    };
  }
}
```

### 📈 优化效果预期
- **错误恢复成功率**: 从30%提升到85%
- **中文字体问题解决率**: 从60%提升到95%
- **大文件处理稳定性**: 内存溢出问题减少80%
- **处理时间**: 虽然增加了恢复逻辑，但总体处理时间减少20%（减少重新处理次数）

---

## 🔄 3. 数据同步机制的可靠性优化

### 📊 当前问题分析
- **同步失败处理简单**: 重试机制不够智能，缺少指数退避策略
- **数据一致性检查缺失**: 无法检测本地和云端数据的不一致
- **网络异常处理**: 网络中断时的数据保护机制不完善

### 🎯 优化方案

#### 智能同步服务
```typescript
class ReliableSyncService {
  private syncQueue: SyncQueueManager;
  private consistencyChecker: DataConsistencyChecker;
  private networkMonitor: NetworkMonitor;

  constructor() {
    this.syncQueue = new SyncQueueManager();
    this.consistencyChecker = new DataConsistencyChecker();
    this.networkMonitor = new NetworkMonitor();
  }

  // 智能同步策略
  async syncWithIntelligentRetry(syncItem: SyncQueueItem): Promise<SyncResult> {
    const maxRetries = syncItem.maxRetries || 5;
    let retryCount = 0;
    let lastError: Error;

    while (retryCount < maxRetries) {
      try {
        // 检查网络状态
        const networkStatus = await this.networkMonitor.checkConnectivity();
        if (!networkStatus.isConnected) {
          throw new Error('网络连接不可用');
        }

        // 执行同步操作
        const result = await this.performSync(syncItem);

        // 验证同步结果
        const verificationResult = await this.verifySyncResult(syncItem, result);
        if (!verificationResult.isValid) {
          throw new Error(`同步结果验证失败: ${verificationResult.error}`);
        }

        // 同步成功，更新队列状态
        await this.syncQueue.markAsCompleted(syncItem.id, result);

        return {
          success: true,
          result,
          retryCount,
          syncTime: Date.now() - syncItem.createTime
        };

      } catch (error) {
        lastError = error;
        retryCount++;

        // 分析错误类型并决定重试策略
        const errorAnalysis = this.analyzeErrorAndGetRetryStrategy(error);

        if (!errorAnalysis.shouldRetry) {
          console.log(`同步项 ${syncItem.id} 不可重试错误: ${error.message}`);
          break;
        }

        // 更新重试计数
        await this.syncQueue.updateRetryCount(syncItem.id, retryCount);

        // 计算重试延迟（指数退避 + 抖动）
        const baseDelay = Math.min(1000 * Math.pow(2, retryCount), 30000);
        const jitter = Math.random() * 1000; // 添加抖动避免雷群效应
        const delay = baseDelay + jitter;

        console.log(`同步项 ${syncItem.id} 第${retryCount}次重试，延迟${delay}ms`);
        await this.sleep(delay);
      }
    }

    // 同步失败，标记为需要人工处理
    await this.syncQueue.markForManualHandling(syncItem.id, lastError);

    return {
      success: false,
      error: lastError.message,
      retryCount,
      requiresManualIntervention: true
    };
  }

  // 错误分析和重试策略
  private analyzeErrorAndGetRetryStrategy(error: Error): RetryStrategy {
    const errorMessage = error.message.toLowerCase();

    // 网络相关错误 - 可重试
    if (errorMessage.includes('network') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('connection')) {
      return {
        shouldRetry: true,
        category: 'network',
        maxRetries: 5,
        baseDelay: 2000
      };
    }

    // 服务器错误 - 可重试
    if (errorMessage.includes('server error') ||
        errorMessage.includes('503') ||
        errorMessage.includes('502')) {
      return {
        shouldRetry: true,
        category: 'server',
        maxRetries: 3,
        baseDelay: 5000
      };
    }

    // 认证错误 - 需要重新认证
    if (errorMessage.includes('unauthorized') ||
        errorMessage.includes('401') ||
        errorMessage.includes('403')) {
      return {
        shouldRetry: true,
        category: 'auth',
        maxRetries: 2,
        baseDelay: 1000,
        requiresReauth: true
      };
    }

    // 数据格式错误 - 不可重试
    if (errorMessage.includes('invalid data') ||
        errorMessage.includes('format error') ||
        errorMessage.includes('validation')) {
      return {
        shouldRetry: false,
        category: 'data',
        requiresManualFix: true
      };
    }

    // 默认策略
    return {
      shouldRetry: true,
      category: 'unknown',
      maxRetries: 2,
      baseDelay: 3000
    };
  }

  // 数据一致性检查
  async performConsistencyCheck(): Promise<ConsistencyReport> {
    console.log('开始数据一致性检查...');

    const localOrders = await this.orderService.getAllOrders();
    const cloudOrders = await this.cloudService.getAllOrders();

    const inconsistencies: DataInconsistency[] = [];
    const checkedItems = 0;

    // 检查本地订单在云端的状态
    for (const localOrder of localOrders) {
      const cloudOrder = cloudOrders.find(o => o.orderId === localOrder.orderId);

      if (!cloudOrder) {
        inconsistencies.push({
          type: 'missing_in_cloud',
          orderId: localOrder.orderId,
          description: '本地订单在云端不存在',
          severity: 'high',
          localData: localOrder,
          cloudData: null,
          suggestedAction: 'upload_to_cloud'
        });
      } else {
        // 检查状态一致性
        if (localOrder.status !== cloudOrder.status) {
          inconsistencies.push({
            type: 'status_mismatch',
            orderId: localOrder.orderId,
            description: `订单状态不一致: 本地(${localOrder.status}) vs 云端(${cloudOrder.status})`,
            severity: 'medium',
            localData: { status: localOrder.status, updateTime: localOrder.updateTime },
            cloudData: { status: cloudOrder.status, updateTime: cloudOrder.updateTime },
            suggestedAction: this.determineSyncDirection(localOrder, cloudOrder)
          });
        }

        // 检查更新时间一致性
        if (Math.abs(localOrder.updateTime - cloudOrder.updateTime) > 60000) { // 1分钟差异
          inconsistencies.push({
            type: 'timestamp_mismatch',
            orderId: localOrder.orderId,
            description: '更新时间差异过大',
            severity: 'low',
            localData: { updateTime: localOrder.updateTime },
            cloudData: { updateTime: cloudOrder.updateTime },
            suggestedAction: 'sync_latest'
          });
        }
      }
    }

    // 检查云端订单在本地的状态
    for (const cloudOrder of cloudOrders) {
      const localOrder = localOrders.find(o => o.orderId === cloudOrder.orderId);

      if (!localOrder) {
        inconsistencies.push({
          type: 'missing_in_local',
          orderId: cloudOrder.orderId,
          description: '云端订单在本地不存在',
          severity: 'medium',
          localData: null,
          cloudData: cloudOrder,
          suggestedAction: 'download_from_cloud'
        });
      }
    }

    const report: ConsistencyReport = {
      timestamp: Date.now(),
      totalLocalOrders: localOrders.length,
      totalCloudOrders: cloudOrders.length,
      inconsistencyCount: inconsistencies.length,
      inconsistencies,
      overallHealth: inconsistencies.length === 0 ? 'healthy' :
                    inconsistencies.filter(i => i.severity === 'high').length > 0 ? 'critical' : 'warning'
    };

    // 记录检查结果
    await this.recordConsistencyCheck(report);

    return report;
  }

  // 自动修复数据不一致
  async autoFixInconsistencies(report: ConsistencyReport): Promise<FixResult> {
    const fixResults: IndividualFixResult[] = [];
    let successCount = 0;
    let failureCount = 0;

    for (const inconsistency of report.inconsistencies) {
      try {
        const fixResult = await this.fixInconsistency(inconsistency);
        fixResults.push(fixResult);

        if (fixResult.success) {
          successCount++;
        } else {
          failureCount++;
        }

      } catch (error) {
        fixResults.push({
          inconsistencyId: inconsistency.orderId,
          success: false,
          error: error.message,
          action: 'failed'
        });
        failureCount++;
      }
    }

    return {
      totalProcessed: report.inconsistencies.length,
      successCount,
      failureCount,
      fixResults
    };
  }

  // 修复单个数据不一致
  private async fixInconsistency(inconsistency: DataInconsistency): Promise<IndividualFixResult> {
    switch (inconsistency.suggestedAction) {
      case 'upload_to_cloud':
        // 上传本地数据到云端
        await this.syncQueue.addSyncItem({
          type: 'order_upload',
          orderId: inconsistency.orderId,
          data: inconsistency.localData,
          priority: 'high'
        });
        return {
          inconsistencyId: inconsistency.orderId,
          success: true,
          action: 'uploaded_to_cloud'
        };

      case 'download_from_cloud':
        // 从云端下载数据到本地
        const cloudData = inconsistency.cloudData;
        await this.orderService.createOrUpdateOrder(cloudData);
        return {
          inconsistencyId: inconsistency.orderId,
          success: true,
          action: 'downloaded_from_cloud'
        };

      case 'sync_latest':
        // 同步最新数据
        const localTime = inconsistency.localData?.updateTime || 0;
        const cloudTime = inconsistency.cloudData?.updateTime || 0;

        if (localTime > cloudTime) {
          // 本地更新，上传到云端
          await this.syncQueue.addSyncItem({
            type: 'order_update',
            orderId: inconsistency.orderId,
            data: inconsistency.localData,
            priority: 'medium'
          });
          return {
            inconsistencyId: inconsistency.orderId,
            success: true,
            action: 'uploaded_latest_to_cloud'
          };
        } else {
          // 云端更新，下载到本地
          await this.orderService.updateOrder(inconsistency.orderId, inconsistency.cloudData);
          return {
            inconsistencyId: inconsistency.orderId,
            success: true,
            action: 'downloaded_latest_from_cloud'
          };
        }

      default:
        return {
          inconsistencyId: inconsistency.orderId,
          success: false,
          error: '未知的修复动作',
          action: 'unknown'
        };
    }
  }

  // 确定同步方向
  private determineSyncDirection(localOrder: Order, cloudOrder: Order): string {
    // 基于时间戳确定哪个更新
    if (localOrder.updateTime > cloudOrder.updateTime) {
      return 'upload_to_cloud';
    } else if (cloudOrder.updateTime > localOrder.updateTime) {
      return 'download_from_cloud';
    } else {
      // 时间戳相同，基于状态优先级
      const statusPriority = {
        'completed': 10,
        'shipped': 9,
        'printing': 8,
        'processing': 7,
        'pending_process': 6,
        'cancelled': 5,
        'failed': 1
      };

      const localPriority = statusPriority[localOrder.status] || 0;
      const cloudPriority = statusPriority[cloudOrder.status] || 0;

      return localPriority >= cloudPriority ? 'upload_to_cloud' : 'download_from_cloud';
    }
  }

  // 批量同步优化
  async performBatchSync(items: SyncQueueItem[]): Promise<BatchSyncResult> {
    const batchSize = 10; // 每批处理10个项目
    const batches = this.chunkArray(items, batchSize);
    const results: SyncResult[] = [];

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`处理批次 ${i + 1}/${batches.length}，包含 ${batch.length} 个项目`);

      // 并行处理批次内的项目
      const batchPromises = batch.map(item => this.syncWithIntelligentRetry(item));
      const batchResults = await Promise.allSettled(batchPromises);

      // 处理批次结果
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            error: result.reason.message,
            retryCount: 0,
            requiresManualIntervention: true
          });
        }
      });

      // 批次间延迟，避免服务器压力
      if (i < batches.length - 1) {
        await this.sleep(1000);
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      totalItems: items.length,
      successCount,
      failureCount,
      results,
      processingTime: Date.now() - Date.now() // 实际应该记录开始时间
    };
  }

  // 网络状态监控
  private async monitorNetworkAndSync(): Promise<void> {
    this.networkMonitor.on('connected', async () => {
      console.log('网络连接恢复，开始处理待同步队列');
      const pendingItems = await this.syncQueue.getPendingItems();
      if (pendingItems.length > 0) {
        await this.performBatchSync(pendingItems);
      }
    });

    this.networkMonitor.on('disconnected', () => {
      console.log('网络连接断开，暂停同步操作');
      this.syncQueue.pauseProcessing();
    });
  }

  // 工具方法
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 数据类型定义
interface SyncQueueItem {
  id: string;
  type: 'order_upload' | 'order_update' | 'order_download' | 'status_sync';
  orderId: string;
  data: any;
  priority: 'low' | 'medium' | 'high';
  maxRetries: number;
  createTime: number;
  retryCount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'manual_handling';
}

interface RetryStrategy {
  shouldRetry: boolean;
  category: string;
  maxRetries?: number;
  baseDelay?: number;
  requiresReauth?: boolean;
  requiresManualFix?: boolean;
}

interface DataInconsistency {
  type: 'missing_in_cloud' | 'missing_in_local' | 'status_mismatch' | 'timestamp_mismatch';
  orderId: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  localData: any;
  cloudData: any;
  suggestedAction: string;
}

interface ConsistencyReport {
  timestamp: number;
  totalLocalOrders: number;
  totalCloudOrders: number;
  inconsistencyCount: number;
  inconsistencies: DataInconsistency[];
  overallHealth: 'healthy' | 'warning' | 'critical';
}

interface SyncResult {
  success: boolean;
  result?: any;
  error?: string;
  retryCount: number;
  syncTime?: number;
  requiresManualIntervention?: boolean;
}

interface BatchSyncResult {
  totalItems: number;
  successCount: number;
  failureCount: number;
  results: SyncResult[];
  processingTime: number;
}

interface FixResult {
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  fixResults: IndividualFixResult[];
}

interface IndividualFixResult {
  inconsistencyId: string;
  success: boolean;
  error?: string;
  action: string;
}
```

### 📈 优化效果预期
- **同步成功率**: 从75%提升到95%
- **数据一致性**: 不一致问题检测率100%，自动修复率85%
- **网络异常恢复**: 网络恢复后自动同步成功率90%
- **同步性能**: 批量同步效率提升60%

---

## 📥 4. 文件下载系统的容错机制

### 📊 当前问题分析
- **文件完整性验证缺失**: 下载后无法确保文件完整性
- **重试策略简单**: 固定重试次数，不考虑错误类型
- **断点续传不完善**: 大文件下载中断后需要重新开始

### 🎯 优化方案

#### 增强文件下载服务
```typescript
class EnhancedFileDownloadService {
  private downloadQueue: DownloadQueue;
  private integrityVerifier: FileIntegrityVerifier;
  private resumeManager: DownloadResumeManager;

  constructor() {
    this.downloadQueue = new DownloadQueue();
    this.integrityVerifier = new FileIntegrityVerifier();
    this.resumeManager = new DownloadResumeManager();
  }

  // 智能下载主入口
  async downloadWithSmartRetry(
    fileUrl: string,
    options: EnhancedDownloadOptions
  ): Promise<DownloadResult> {
    const downloadId = this.generateDownloadId(fileUrl);
    let retryCount = 0;
    const maxRetries = options.maxRetries || 5;
    let lastError: Error;

    while (retryCount < maxRetries) {
      try {
        // 检查是否可以断点续传
        const resumeInfo = await this.resumeManager.getResumeInfo(downloadId);

        let result: DownloadResult;
        if (resumeInfo && resumeInfo.canResume) {
          console.log(`断点续传下载: ${fileUrl}, 已下载: ${resumeInfo.downloadedBytes} bytes`);
          result = await this.resumeDownload(fileUrl, options, resumeInfo);
        } else {
          console.log(`开始新下载: ${fileUrl}`);
          result = await this.startNewDownload(fileUrl, options);
        }

        // 验证文件完整性
        if (options.verifyIntegrity !== false) {
          const integrityResult = await this.verifyFileIntegrity(
            result.localPath,
            options.expectedChecksum,
            options.expectedSize
          );

          if (!integrityResult.isValid) {
            throw new Error(`文件完整性验证失败: ${integrityResult.error}`);
          }
        }

        // 下载成功，清理断点续传信息
        await this.resumeManager.clearResumeInfo(downloadId);

        return {
          ...result,
          success: true,
          retryCount,
          downloadTime: Date.now() - result.startTime
        };

      } catch (error) {
        lastError = error;
        retryCount++;

        console.warn(`下载失败 (第${retryCount}次): ${error.message}`);

        // 分析错误并决定重试策略
        const retryStrategy = this.analyzeDownloadError(error, retryCount);

        if (!retryStrategy.shouldRetry) {
          console.log(`下载不可重试: ${error.message}`);
          break;
        }

        // 保存断点续传信息
        if (retryStrategy.supportResume) {
          await this.resumeManager.saveResumeInfo(downloadId, {
            fileUrl,
            localPath: options.localPath,
            downloadedBytes: await this.getDownloadedBytes(options.localPath),
            totalBytes: options.expectedSize,
            lastModified: Date.now(),
            canResume: true
          });
        }

        // 计算重试延迟
        const delay = this.calculateRetryDelay(retryStrategy, retryCount);
        console.log(`等待 ${delay}ms 后重试...`);
        await this.sleep(delay);
      }
    }

    // 所有重试都失败
    await this.resumeManager.markAsFailed(downloadId, lastError);

    return {
      success: false,
      error: `下载失败，已重试${retryCount}次: ${lastError.message}`,
      retryCount,
      localPath: options.localPath
    };
  }

  // 文件完整性验证
  async verifyFileIntegrity(
    filePath: string,
    expectedChecksum?: string,
    expectedSize?: number
  ): Promise<IntegrityResult> {
    try {
      // 检查文件是否存在
      if (!await fs.pathExists(filePath)) {
        return {
          isValid: false,
          error: '文件不存在'
        };
      }

      // 检查文件大小
      if (expectedSize) {
        const stats = await fs.stat(filePath);
        if (stats.size !== expectedSize) {
          return {
            isValid: false,
            error: `文件大小不匹配: 期望${expectedSize}, 实际${stats.size}`,
            expectedSize,
            actualSize: stats.size
          };
        }
      }

      // 检查文件校验和
      if (expectedChecksum) {
        const actualChecksum = await this.calculateChecksum(filePath);
        if (actualChecksum !== expectedChecksum) {
          return {
            isValid: false,
            error: '文件校验和不匹配',
            expectedChecksum,
            actualChecksum
          };
        }
      }

      // 检查文件是否损坏（尝试读取文件头）
      const corruptionCheck = await this.checkFileCorruption(filePath);
      if (!corruptionCheck.isValid) {
        return corruptionCheck;
      }

      return {
        isValid: true,
        actualChecksum: expectedChecksum ? await this.calculateChecksum(filePath) : undefined,
        actualSize: (await fs.stat(filePath)).size
      };

    } catch (error) {
      return {
        isValid: false,
        error: `完整性验证失败: ${error.message}`
      };
    }
  }

  // 断点续传下载
  private async resumeDownload(
    fileUrl: string,
    options: EnhancedDownloadOptions,
    resumeInfo: ResumeInfo
  ): Promise<DownloadResult> {
    const startTime = Date.now();

    // 设置Range请求头进行断点续传
    const headers = {
      ...options.headers,
      'Range': `bytes=${resumeInfo.downloadedBytes}-`
    };

    const response = await fetch(fileUrl, {
      method: 'GET',
      headers,
      timeout: options.timeout || 30000
    });

    if (response.status !== 206) { // 206 Partial Content
      throw new Error(`服务器不支持断点续传: ${response.status}`);
    }

    // 以追加模式打开文件
    const writeStream = fs.createWriteStream(resumeInfo.localPath, { flags: 'a' });

    return new Promise((resolve, reject) => {
      let downloadedBytes = resumeInfo.downloadedBytes;
      const totalBytes = resumeInfo.totalBytes || 0;

      response.body.on('data', (chunk) => {
        downloadedBytes += chunk.length;

        // 更新进度
        if (options.onProgress) {
          options.onProgress({
            downloadedBytes,
            totalBytes,
            percentage: totalBytes > 0 ? (downloadedBytes / totalBytes) * 100 : 0,
            speed: this.calculateSpeed(downloadedBytes - resumeInfo.downloadedBytes, Date.now() - startTime)
          });
        }
      });

      response.body.on('end', () => {
        writeStream.end();
        resolve({
          success: true,
          localPath: resumeInfo.localPath,
          fileSize: downloadedBytes,
          startTime,
          isResumed: true
        });
      });

      response.body.on('error', (error) => {
        writeStream.destroy();
        reject(error);
      });

      response.body.pipe(writeStream);
    });
  }

  // 新下载
  private async startNewDownload(
    fileUrl: string,
    options: EnhancedDownloadOptions
  ): Promise<DownloadResult> {
    const startTime = Date.now();

    const response = await fetch(fileUrl, {
      method: 'GET',
      headers: options.headers,
      timeout: options.timeout || 30000
    });

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    const totalBytes = parseInt(response.headers.get('content-length') || '0');
    const writeStream = fs.createWriteStream(options.localPath);

    return new Promise((resolve, reject) => {
      let downloadedBytes = 0;

      response.body.on('data', (chunk) => {
        downloadedBytes += chunk.length;

        // 更新进度
        if (options.onProgress) {
          options.onProgress({
            downloadedBytes,
            totalBytes,
            percentage: totalBytes > 0 ? (downloadedBytes / totalBytes) * 100 : 0,
            speed: this.calculateSpeed(downloadedBytes, Date.now() - startTime)
          });
        }
      });

      response.body.on('end', () => {
        writeStream.end();
        resolve({
          success: true,
          localPath: options.localPath,
          fileSize: downloadedBytes,
          startTime,
          isResumed: false
        });
      });

      response.body.on('error', (error) => {
        writeStream.destroy();
        reject(error);
      });

      response.body.pipe(writeStream);
    });
  }

  // 下载错误分析
  private analyzeDownloadError(error: Error, retryCount: number): DownloadRetryStrategy {
    const errorMessage = error.message.toLowerCase();

    // 网络超时错误 - 可重试，支持断点续传
    if (errorMessage.includes('timeout') || errorMessage.includes('etimedout')) {
      return {
        shouldRetry: true,
        supportResume: true,
        category: 'timeout',
        baseDelay: 2000,
        maxRetries: 5
      };
    }

    // 网络连接错误 - 可重试，支持断点续传
    if (errorMessage.includes('econnreset') ||
        errorMessage.includes('enotfound') ||
        errorMessage.includes('network')) {
      return {
        shouldRetry: true,
        supportResume: true,
        category: 'network',
        baseDelay: 3000,
        maxRetries: 5
      };
    }

    // 服务器错误 - 可重试
    if (errorMessage.includes('500') ||
        errorMessage.includes('502') ||
        errorMessage.includes('503')) {
      return {
        shouldRetry: true,
        supportResume: false,
        category: 'server',
        baseDelay: 5000,
        maxRetries: 3
      };
    }

    // 文件完整性错误 - 可重试，不支持断点续传
    if (errorMessage.includes('完整性') || errorMessage.includes('校验')) {
      return {
        shouldRetry: retryCount < 2,
        supportResume: false,
        category: 'integrity',
        baseDelay: 1000,
        maxRetries: 2
      };
    }

    // 权限错误 - 不可重试
    if (errorMessage.includes('permission') ||
        errorMessage.includes('unauthorized') ||
        errorMessage.includes('403')) {
      return {
        shouldRetry: false,
        supportResume: false,
        category: 'permission',
        baseDelay: 0,
        maxRetries: 0
      };
    }

    // 文件不存在 - 不可重试
    if (errorMessage.includes('404') || errorMessage.includes('not found')) {
      return {
        shouldRetry: false,
        supportResume: false,
        category: 'not_found',
        baseDelay: 0,
        maxRetries: 0
      };
    }

    // 默认策略
    return {
      shouldRetry: retryCount < 3,
      supportResume: true,
      category: 'unknown',
      baseDelay: 2000,
      maxRetries: 3
    };
  }

  // 计算重试延迟
  private calculateRetryDelay(strategy: DownloadRetryStrategy, retryCount: number): number {
    // 指数退避 + 抖动
    const exponentialDelay = strategy.baseDelay * Math.pow(2, retryCount - 1);
    const maxDelay = 30000; // 最大30秒
    const jitter = Math.random() * 1000; // 随机抖动

    return Math.min(exponentialDelay + jitter, maxDelay);
  }

  // 检查文件损坏
  private async checkFileCorruption(filePath: string): Promise<IntegrityResult> {
    try {
      const fileExtension = path.extname(filePath).toLowerCase();

      switch (fileExtension) {
        case '.pdf':
          return await this.checkPDFCorruption(filePath);
        case '.jpg':
        case '.jpeg':
          return await this.checkJPEGCorruption(filePath);
        case '.png':
          return await this.checkPNGCorruption(filePath);
        default:
          // 对于其他文件类型，只检查是否可以读取
          const buffer = await fs.readFile(filePath, { encoding: null });
          return {
            isValid: buffer.length > 0,
            error: buffer.length === 0 ? '文件为空' : undefined
          };
      }
    } catch (error) {
      return {
        isValid: false,
        error: `文件损坏检查失败: ${error.message}`
      };
    }
  }

  // PDF文件损坏检查
  private async checkPDFCorruption(filePath: string): Promise<IntegrityResult> {
    try {
      const buffer = await fs.readFile(filePath, { encoding: null });

      // 检查PDF文件头
      if (!buffer.subarray(0, 4).equals(Buffer.from('%PDF'))) {
        return {
          isValid: false,
          error: 'PDF文件头损坏'
        };
      }

      // 检查PDF文件尾
      const tail = buffer.subarray(-1024).toString();
      if (!tail.includes('%%EOF')) {
        return {
          isValid: false,
          error: 'PDF文件尾损坏'
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: `PDF损坏检查失败: ${error.message}`
      };
    }
  }

  // 计算文件校验和
  private async calculateChecksum(filePath: string, algorithm: string = 'sha256'): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash(algorithm);
      const stream = fs.createReadStream(filePath);

      stream.on('data', (data) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }

  // 计算下载速度
  private calculateSpeed(bytes: number, timeMs: number): number {
    if (timeMs === 0) return 0;
    return (bytes / (timeMs / 1000)); // bytes per second
  }

  // 获取已下载字节数
  private async getDownloadedBytes(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  // 生成下载ID
  private generateDownloadId(fileUrl: string): string {
    return crypto.createHash('md5').update(fileUrl).digest('hex');
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 断点续传管理器
class DownloadResumeManager {
  private resumeInfoPath = './data/download_resume.json';

  async getResumeInfo(downloadId: string): Promise<ResumeInfo | null> {
    try {
      const resumeData = await fs.readJSON(this.resumeInfoPath);
      return resumeData[downloadId] || null;
    } catch (error) {
      return null;
    }
  }

  async saveResumeInfo(downloadId: string, info: ResumeInfo): Promise<void> {
    try {
      let resumeData = {};
      try {
        resumeData = await fs.readJSON(this.resumeInfoPath);
      } catch (error) {
        // 文件不存在，使用空对象
      }

      resumeData[downloadId] = info;
      await fs.writeJSON(this.resumeInfoPath, resumeData, { spaces: 2 });
    } catch (error) {
      console.error('保存断点续传信息失败:', error);
    }
  }

  async clearResumeInfo(downloadId: string): Promise<void> {
    try {
      const resumeData = await fs.readJSON(this.resumeInfoPath);
      delete resumeData[downloadId];
      await fs.writeJSON(this.resumeInfoPath, resumeData, { spaces: 2 });
    } catch (error) {
      // 忽略错误
    }
  }

  async markAsFailed(downloadId: string, error: Error): Promise<void> {
    try {
      const resumeData = await fs.readJSON(this.resumeInfoPath);
      if (resumeData[downloadId]) {
        resumeData[downloadId].canResume = false;
        resumeData[downloadId].failureReason = error.message;
        resumeData[downloadId].failureTime = Date.now();
        await fs.writeJSON(this.resumeInfoPath, resumeData, { spaces: 2 });
      }
    } catch (error) {
      console.error('标记下载失败状态失败:', error);
    }
  }
}

// 数据类型定义
interface EnhancedDownloadOptions {
  localPath: string;
  expectedChecksum?: string;
  expectedSize?: number;
  maxRetries?: number;
  timeout?: number;
  headers?: Record<string, string>;
  verifyIntegrity?: boolean;
  onProgress?: (progress: DownloadProgress) => void;
}

interface DownloadProgress {
  downloadedBytes: number;
  totalBytes: number;
  percentage: number;
  speed: number; // bytes per second
}

interface DownloadResult {
  success: boolean;
  localPath: string;
  fileSize?: number;
  error?: string;
  retryCount?: number;
  downloadTime?: number;
  startTime?: number;
  isResumed?: boolean;
}

interface ResumeInfo {
  fileUrl: string;
  localPath: string;
  downloadedBytes: number;
  totalBytes: number;
  lastModified: number;
  canResume: boolean;
  failureReason?: string;
  failureTime?: number;
}

interface DownloadRetryStrategy {
  shouldRetry: boolean;
  supportResume: boolean;
  category: string;
  baseDelay: number;
  maxRetries: number;
}

interface IntegrityResult {
  isValid: boolean;
  error?: string;
  expectedChecksum?: string;
  actualChecksum?: string;
  expectedSize?: number;
  actualSize?: number;
}
```

### 📈 优化效果预期
- **下载成功率**: 从85%提升到98%
- **大文件下载稳定性**: 断点续传成功率95%
- **文件完整性**: 损坏文件检测率100%，自动修复率80%
- **下载效率**: 重复下载减少70%，总体下载时间减少40%

---

## 🖨️ 5. 打印队列的智能调度优化

### 📊 当前问题分析
- **打印机选择算法简单**: 仅基于基本能力匹配，未考虑负载均衡
- **优先级管理不足**: 缺少动态优先级调整机制
- **资源利用率低**: 打印机负载不均，部分设备闲置

### 🎯 优化方案

#### 智能打印调度器
```typescript
class IntelligentPrintScheduler {
  private printerManager: PrinterManager;
  private loadBalancer: PrinterLoadBalancer;
  private priorityManager: TaskPriorityManager;
  private performanceAnalyzer: PrinterPerformanceAnalyzer;

  constructor() {
    this.printerManager = new PrinterManager();
    this.loadBalancer = new PrinterLoadBalancer();
    this.priorityManager = new TaskPriorityManager();
    this.performanceAnalyzer = new PrinterPerformanceAnalyzer();
  }

  // 智能打印机选择
  async selectOptimalPrinter(order: Order): Promise<PrinterSelection> {
    // 1. 分析订单要求
    const orderRequirements = await this.analyzeOrderRequirements(order);

    // 2. 获取可用打印机
    const availablePrinters = await this.printerManager.getAvailablePrinters();

    // 3. 过滤符合要求的打印机
    const compatiblePrinters = availablePrinters.filter(printer =>
      this.isCompatible(printer, orderRequirements)
    );

    if (compatiblePrinters.length === 0) {
      throw new Error('没有找到符合要求的打印机');
    }

    // 4. 多维度评分
    const scoredPrinters = await Promise.all(
      compatiblePrinters.map(async printer => ({
        printer,
        score: await this.calculatePrinterScore(printer, orderRequirements),
        reasons: await this.generateScoreReasons(printer, orderRequirements)
      }))
    );

    // 5. 选择最佳打印机
    const bestPrinter = scoredPrinters.sort((a, b) => b.score - a.score)[0];

    // 6. 预留打印机资源
    await this.printerManager.reservePrinter(bestPrinter.printer.name, order.orderId);

    return {
      printer: bestPrinter.printer,
      score: bestPrinter.score,
      reasons: bestPrinter.reasons,
      confidence: bestPrinter.score / 100,
      alternatives: scoredPrinters.slice(1, 4).map(sp => sp.printer)
    };
  }

  // 多维度打印机评分
  private async calculatePrinterScore(
    printer: PrinterCapabilities,
    requirements: OrderRequirements
  ): Promise<number> {
    let score = 0;
    const weights = {
      capability: 0.4,    // 能力匹配 40%
      performance: 0.3,   // 性能表现 30%
      load: 0.2,          // 负载情况 20%
      maintenance: 0.1    // 维护状态 10%
    };

    // 1. 能力匹配度评分 (0-40分)
    const capabilityScore = this.calculateCapabilityScore(printer, requirements);
    score += capabilityScore * weights.capability;

    // 2. 性能表现评分 (0-30分)
    const performanceScore = await this.calculatePerformanceScore(printer);
    score += performanceScore * weights.performance;

    // 3. 负载情况评分 (0-20分)
    const loadScore = await this.calculateLoadScore(printer);
    score += loadScore * weights.load;

    // 4. 维护状态评分 (0-10分)
    const maintenanceScore = this.calculateMaintenanceScore(printer);
    score += maintenanceScore * weights.maintenance;

    return Math.round(score);
  }

  // 能力匹配度评分
  private calculateCapabilityScore(
    printer: PrinterCapabilities,
    requirements: OrderRequirements
  ): number {
    let score = 0;

    // 颜色支持匹配 (20分)
    if (printer.capabilities.colorSupport === requirements.colorMode) {
      score += 20;
    } else if (printer.capabilities.colorSupport === 'color' && requirements.colorMode === 'mono') {
      score += 15; // 彩色打印机可以打印黑白，但不是最优
    }

    // 双面打印支持 (10分)
    if (requirements.requiresDuplex) {
      score += printer.capabilities.duplexSupport ? 10 : 0;
    } else {
      score += 10; // 不需要双面打印时给满分
    }

    // 纸张大小支持 (10分)
    if (printer.capabilities.paperSizes.includes(requirements.paperSize)) {
      score += 10;
    }

    // 装订支持 (10分)
    if (requirements.bindingType && requirements.bindingType !== '不装订') {
      score += printer.capabilities.bindingSupport.includes(requirements.bindingType) ? 10 : 0;
    } else {
      score += 10;
    }

    // 分辨率匹配 (10分)
    if (printer.capabilities.maxResolution >= requirements.requiredResolution) {
      score += 10;
    } else {
      score += (printer.capabilities.maxResolution / requirements.requiredResolution) * 10;
    }

    return Math.min(score, 60); // 最高60分
  }

  // 性能表现评分
  private async calculatePerformanceScore(printer: PrinterCapabilities): Promise<number> {
    const stats = await this.performanceAnalyzer.getPrinterStatistics(printer.name);
    let score = 0;

    // 成功率 (15分)
    score += (stats.successRate / 100) * 15;

    // 平均处理时间 (10分)
    const avgTimeScore = Math.max(0, 10 - (stats.averageJobTime / 60)); // 每分钟扣1分
    score += Math.min(avgTimeScore, 10);

    // 可靠性 (5分)
    const uptimeScore = (stats.uptime / (24 * 60 * 60 * 1000)) * 5; // 基于24小时在线时间
    score += Math.min(uptimeScore, 5);

    return Math.min(score, 30);
  }

  // 负载情况评分
  private async calculateLoadScore(printer: PrinterCapabilities): Promise<number> {
    const currentLoad = await this.loadBalancer.getPrinterLoad(printer.name);
    const maxConcurrentJobs = printer.capabilities.maxConcurrentJobs || 3;

    // 负载越低分数越高
    const loadPercentage = currentLoad.currentJobs / maxConcurrentJobs;
    const score = Math.max(0, 20 - (loadPercentage * 20));

    return score;
  }

  // 维护状态评分
  private calculateMaintenanceScore(printer: PrinterCapabilities): number {
    switch (printer.maintenanceStatus) {
      case 'excellent': return 10;
      case 'good': return 8;
      case 'warning': return 5;
      case 'critical': return 2;
      case 'maintenance_required': return 0;
      default: return 6;
    }
  }

  // 动态优先级调整
  async adjustTaskPriority(jobId: string): Promise<void> {
    const job = await this.getJob(jobId);
    const order = await this.orderService.getOrder(job.orderId);

    let newPriority = job.priority;
    const adjustmentReasons: string[] = [];

    // 1. 基于等待时间调整
    const waitingTime = Date.now() - job.createTime;
    const waitingHours = waitingTime / (60 * 60 * 1000);

    if (waitingHours > 4) {
      newPriority += 3;
      adjustmentReasons.push(`等待时间过长(${waitingHours.toFixed(1)}小时)`);
    } else if (waitingHours > 2) {
      newPriority += 2;
      adjustmentReasons.push(`等待时间较长(${waitingHours.toFixed(1)}小时)`);
    } else if (waitingHours > 1) {
      newPriority += 1;
      adjustmentReasons.push(`等待时间超过1小时`);
    }

    // 2. 基于订单特征调整
    if (order.isUrgent) {
      newPriority += 3;
      adjustmentReasons.push('紧急订单');
    }

    if (order.customerLevel === 'VIP') {
      newPriority += 2;
      adjustmentReasons.push('VIP客户');
    }

    if (order.deliveryType === 'express' && order.deliveryTime) {
      const deliveryDeadline = new Date(order.deliveryTime).getTime();
      const timeToDeadline = deliveryDeadline - Date.now();
      const hoursToDeadline = timeToDeadline / (60 * 60 * 1000);

      if (hoursToDeadline < 6) {
        newPriority += 4;
        adjustmentReasons.push(`配送截止时间临近(${hoursToDeadline.toFixed(1)}小时)`);
      } else if (hoursToDeadline < 12) {
        newPriority += 2;
        adjustmentReasons.push(`配送时间紧张(${hoursToDeadline.toFixed(1)}小时)`);
      }
    }

    // 3. 基于文件复杂度调整
    const complexity = await this.analyzeJobComplexity(job);
    if (complexity.isComplex) {
      newPriority += 1;
      adjustmentReasons.push('复杂文档处理');
    }

    // 4. 基于打印机可用性调整
    const availablePrinters = await this.printerManager.getCompatiblePrinters(job);
    if (availablePrinters.length <= 2) {
      newPriority += 2;
      adjustmentReasons.push('可用打印机数量有限');
    }

    // 限制最大优先级
    newPriority = Math.min(newPriority, 10);

    // 更新优先级
    if (newPriority !== job.priority) {
      await this.updateJobPriority(jobId, newPriority, adjustmentReasons.join(', '));
      console.log(`任务 ${jobId} 优先级调整: ${job.priority} -> ${newPriority}, 原因: ${adjustmentReasons.join(', ')}`);
    }
  }

  // 智能负载均衡
  async performLoadBalancing(): Promise<LoadBalancingResult> {
    const allPrinters = await this.printerManager.getAllPrinters();
    const loadInfo = await Promise.all(
      allPrinters.map(async printer => ({
        printer,
        load: await this.loadBalancer.getPrinterLoad(printer.name),
        capacity: printer.capabilities.maxConcurrentJobs || 3
      }))
    );

    // 计算负载分布
    const totalCapacity = loadInfo.reduce((sum, info) => sum + info.capacity, 0);
    const totalLoad = loadInfo.reduce((sum, info) => sum + info.load.currentJobs, 0);
    const averageLoadPercentage = totalLoad / totalCapacity;

    // 识别过载和空闲的打印机
    const overloadedPrinters = loadInfo.filter(info =>
      (info.load.currentJobs / info.capacity) > (averageLoadPercentage + 0.2)
    );

    const underloadedPrinters = loadInfo.filter(info =>
      (info.load.currentJobs / info.capacity) < (averageLoadPercentage - 0.2)
    );

    const rebalancingActions: RebalancingAction[] = [];

    // 执行负载重新分配
    for (const overloaded of overloadedPrinters) {
      const availableUnderloaded = underloadedPrinters.filter(u =>
        u.load.currentJobs < u.capacity
      );

      if (availableUnderloaded.length > 0) {
        // 选择负载最轻的打印机
        const targetPrinter = availableUnderloaded.sort((a, b) =>
          (a.load.currentJobs / a.capacity) - (b.load.currentJobs / b.capacity)
        )[0];

        // 移动一个任务
        const taskToMove = await this.findMovableTask(overloaded.printer.name);
        if (taskToMove && await this.isCompatible(targetPrinter.printer, taskToMove.requirements)) {
          await this.moveTask(taskToMove.id, overloaded.printer.name, targetPrinter.printer.name);

          rebalancingActions.push({
            type: 'task_moved',
            taskId: taskToMove.id,
            fromPrinter: overloaded.printer.name,
            toPrinter: targetPrinter.printer.name,
            reason: '负载均衡'
          });

          // 更新负载信息
          overloaded.load.currentJobs--;
          targetPrinter.load.currentJobs++;
        }
      }
    }

    return {
      totalPrinters: allPrinters.length,
      averageLoadPercentage,
      overloadedCount: overloadedPrinters.length,
      underloadedCount: underloadedPrinters.length,
      rebalancingActions,
      balancingImprovement: this.calculateBalancingImprovement(loadInfo, rebalancingActions)
    };
  }

  // 任务复杂度分析
  private async analyzeJobComplexity(job: PrintJob): Promise<ComplexityAnalysis> {
    const order = await this.orderService.getOrder(job.orderId);
    let complexityScore = 0;
    const factors: string[] = [];

    // 文件数量
    if (order.files.length > 5) {
      complexityScore += 2;
      factors.push(`多文件(${order.files.length}个)`);
    }

    // 总页数
    const totalPages = order.files.reduce((sum, file) => sum + file.pageCount, 0);
    if (totalPages > 100) {
      complexityScore += 3;
      factors.push(`大量页面(${totalPages}页)`);
    } else if (totalPages > 50) {
      complexityScore += 2;
      factors.push(`较多页面(${totalPages}页)`);
    }

    // 装订要求
    if (order.printConfig.binding !== '不装订') {
      complexityScore += 2;
      factors.push(`需要装订(${order.printConfig.binding})`);
    }

    // 颜色模式
    if (order.printConfig.color === '标准彩色') {
      complexityScore += 2;
      factors.push('高质量彩色打印');
    }

    // 双面打印
    if (order.printConfig.side === '双面打印') {
      complexityScore += 1;
      factors.push('双面打印');
    }

    // 特殊纸张
    if (order.printConfig.paperType !== 'A4') {
      complexityScore += 1;
      factors.push(`特殊纸张(${order.printConfig.paperType})`);
    }

    return {
      score: complexityScore,
      isComplex: complexityScore >= 5,
      factors
    };
  }

  // 查找可移动的任务
  private async findMovableTask(printerName: string): Promise<MovableTask | null> {
    const tasks = await this.getPrinterTasks(printerName);

    // 优先移动优先级较低且还未开始的任务
    const movableTasks = tasks.filter(task =>
      task.status === 'queued' && task.priority <= 5
    );

    if (movableTasks.length === 0) {
      return null;
    }

    // 选择优先级最低的任务
    const taskToMove = movableTasks.sort((a, b) => a.priority - b.priority)[0];

    return {
      id: taskToMove.id,
      requirements: await this.analyzeOrderRequirements(
        await this.orderService.getOrder(taskToMove.orderId)
      )
    };
  }

  // 移动任务到其他打印机
  private async moveTask(taskId: string, fromPrinter: string, toPrinter: string): Promise<void> {
    await this.db.run(
      'UPDATE print_jobs SET printer_name = ?, update_time = ? WHERE id = ?',
      [toPrinter, Date.now(), taskId]
    );

    // 记录任务移动日志
    await this.logTaskMovement(taskId, fromPrinter, toPrinter, '负载均衡');
  }

  // 计算负载均衡改善程度
  private calculateBalancingImprovement(
    loadInfo: PrinterLoadInfo[],
    actions: RebalancingAction[]
  ): number {
    if (actions.length === 0) return 0;

    // 计算重新分配前的负载方差
    const beforeVariance = this.calculateLoadVariance(loadInfo);

    // 模拟重新分配后的负载
    const afterLoadInfo = this.simulateLoadAfterRebalancing(loadInfo, actions);
    const afterVariance = this.calculateLoadVariance(afterLoadInfo);

    // 返回改善百分比
    return ((beforeVariance - afterVariance) / beforeVariance) * 100;
  }

  // 计算负载方差
  private calculateLoadVariance(loadInfo: PrinterLoadInfo[]): number {
    const loadPercentages = loadInfo.map(info => info.load.currentJobs / info.capacity);
    const average = loadPercentages.reduce((sum, p) => sum + p, 0) / loadPercentages.length;
    const variance = loadPercentages.reduce((sum, p) => sum + Math.pow(p - average, 2), 0) / loadPercentages.length;
    return variance;
  }
}

// 数据类型定义
interface OrderRequirements {
  colorMode: 'mono' | 'color';
  paperSize: string;
  requiresDuplex: boolean;
  bindingType?: string;
  requiredResolution: number;
  estimatedPages: number;
  urgencyLevel: 'normal' | 'urgent' | 'express';
}

interface PrinterSelection {
  printer: PrinterCapabilities;
  score: number;
  reasons: string[];
  confidence: number;
  alternatives: PrinterCapabilities[];
}

interface ComplexityAnalysis {
  score: number;
  isComplex: boolean;
  factors: string[];
}

interface MovableTask {
  id: string;
  requirements: OrderRequirements;
}

interface LoadBalancingResult {
  totalPrinters: number;
  averageLoadPercentage: number;
  overloadedCount: number;
  underloadedCount: number;
  rebalancingActions: RebalancingAction[];
  balancingImprovement: number;
}

interface RebalancingAction {
  type: 'task_moved' | 'printer_paused' | 'printer_resumed';
  taskId?: string;
  fromPrinter?: string;
  toPrinter?: string;
  reason: string;
}

interface PrinterLoadInfo {
  printer: PrinterCapabilities;
  load: {
    currentJobs: number;
    queueLength: number;
    averageWaitTime: number;
  };
  capacity: number;
}
```

### 📈 优化效果预期
- **打印机利用率**: 从60%提升到85%
- **任务等待时间**: 平均等待时间减少50%
- **负载均衡效果**: 打印机负载差异减少70%
- **打印成功率**: 从92%提升到98%

---

## 📊 6. 系统监控和告警机制

### 📊 当前问题分析
- **被动监控**: 只有出现问题时才发现，缺少主动健康检查
- **告警不及时**: 关键问题无法及时发现和处理
- **缺少自动恢复**: 系统异常时需要人工干预

### 🎯 优化方案

#### 系统健康监控器
```typescript
class SystemHealthMonitor {
  private healthChecks: HealthCheck[] = [];
  private alertManager: AlertManager;
  private recoveryManager: AutoRecoveryManager;
  private metricsCollector: MetricsCollector;

  constructor() {
    this.initializeHealthChecks();
    this.alertManager = new AlertManager();
    this.recoveryManager = new AutoRecoveryManager();
    this.metricsCollector = new MetricsCollector();
  }

  // 初始化健康检查项
  private initializeHealthChecks(): void {
    this.healthChecks = [
      new DatabaseHealthCheck(),
      new GhostscriptHealthCheck(),
      new PrinterHealthCheck(),
      new NetworkHealthCheck(),
      new StorageHealthCheck(),
      new MemoryHealthCheck(),
      new CPUHealthCheck(),
      new ServiceHealthCheck()
    ];
  }

  // 执行全面健康检查
  async performHealthCheck(): Promise<HealthReport> {
    const startTime = Date.now();
    console.log('开始系统健康检查...');

    const checkResults = await Promise.allSettled(
      this.healthChecks.map(async check => {
        const checkStartTime = Date.now();
        try {
          const result = await check.execute();
          return {
            ...result,
            checkName: check.name,
            duration: Date.now() - checkStartTime
          };
        } catch (error) {
          return {
            checkName: check.name,
            status: 'unhealthy' as HealthStatus,
            severity: 'critical' as Severity,
            message: `健康检查执行失败: ${error.message}`,
            duration: Date.now() - checkStartTime,
            error: error.message
          };
        }
      })
    );

    // 处理检查结果
    const results: HealthCheckResult[] = checkResults.map(result => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          checkName: 'unknown',
          status: 'unhealthy',
          severity: 'critical',
          message: `健康检查失败: ${result.reason}`,
          duration: 0,
          error: result.reason.toString()
        };
      }
    });

    // 计算整体健康状态
    const overallHealth = this.calculateOverallHealth(results);

    const report: HealthReport = {
      timestamp: Date.now(),
      overallHealth,
      duration: Date.now() - startTime,
      checks: results,
      summary: this.generateHealthSummary(results),
      recommendations: this.generateRecommendations(results),
      metrics: await this.metricsCollector.collectCurrentMetrics()
    };

    // 记录健康检查结果
    await this.recordHealthCheck(report);

    // 如果系统不健康，触发告警和自动恢复
    if (overallHealth !== 'healthy') {
      await this.handleUnhealthySystem(report);
    }

    console.log(`健康检查完成，耗时: ${report.duration}ms，状态: ${overallHealth}`);
    return report;
  }

  // 计算整体健康状态
  private calculateOverallHealth(results: HealthCheckResult[]): HealthStatus {
    const criticalIssues = results.filter(r => r.severity === 'critical' && r.status === 'unhealthy');
    const warningIssues = results.filter(r => r.severity === 'warning' && r.status === 'unhealthy');

    if (criticalIssues.length > 0) {
      return 'critical';
    } else if (warningIssues.length > 0) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }

  // 处理不健康的系统
  private async handleUnhealthySystem(report: HealthReport): Promise<void> {
    // 1. 发送告警
    await this.alertManager.sendAlert(report);

    // 2. 尝试自动恢复
    const recoveryResults = await this.recoveryManager.attemptAutoRecovery(report);

    // 3. 如果自动恢复成功，发送恢复通知
    if (recoveryResults.some(r => r.success)) {
      await this.alertManager.sendRecoveryNotification(recoveryResults);
    }

    // 4. 记录处理结果
    await this.recordIncidentHandling(report, recoveryResults);
  }

  // 生成健康摘要
  private generateHealthSummary(results: HealthCheckResult[]): HealthSummary {
    const total = results.length;
    const healthy = results.filter(r => r.status === 'healthy').length;
    const warning = results.filter(r => r.status === 'unhealthy' && r.severity === 'warning').length;
    const critical = results.filter(r => r.status === 'unhealthy' && r.severity === 'critical').length;

    return {
      totalChecks: total,
      healthyCount: healthy,
      warningCount: warning,
      criticalCount: critical,
      healthPercentage: (healthy / total) * 100
    };
  }

  // 生成改进建议
  private generateRecommendations(results: HealthCheckResult[]): string[] {
    const recommendations: string[] = [];

    results.forEach(result => {
      if (result.status === 'unhealthy') {
        switch (result.checkName) {
          case 'DatabaseHealthCheck':
            if (result.message.includes('连接')) {
              recommendations.push('检查数据库连接配置和网络连通性');
            } else if (result.message.includes('性能')) {
              recommendations.push('优化数据库查询性能，考虑添加索引');
            }
            break;

          case 'MemoryHealthCheck':
            if (result.severity === 'critical') {
              recommendations.push('立即释放内存或重启相关服务');
            } else {
              recommendations.push('监控内存使用情况，考虑增加内存或优化内存使用');
            }
            break;

          case 'StorageHealthCheck':
            recommendations.push('清理临时文件，扩展存储空间或迁移数据');
            break;

          case 'PrinterHealthCheck':
            recommendations.push('检查打印机连接状态，重启打印服务或联系维护人员');
            break;

          case 'NetworkHealthCheck':
            recommendations.push('检查网络连接，重启网络服务或联系网络管理员');
            break;
        }
      }
    });

    return [...new Set(recommendations)]; // 去重
  }

  // 启动定期监控
  async startPeriodicMonitoring(intervalMinutes: number = 5): Promise<void> {
    console.log(`启动定期健康监控，间隔: ${intervalMinutes}分钟`);

    setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('定期健康检查失败:', error);
        await this.alertManager.sendAlert({
          type: 'monitoring_failure',
          message: `健康监控系统故障: ${error.message}`,
          timestamp: Date.now(),
          severity: 'critical'
        });
      }
    }, intervalMinutes * 60 * 1000);
  }

  // 实时指标监控
  async startRealTimeMetricsMonitoring(): Promise<void> {
    console.log('启动实时指标监控...');

    // 监控关键指标
    const metricsToMonitor = [
      { name: 'cpu_usage', threshold: 80, checkInterval: 30000 },
      { name: 'memory_usage', threshold: 85, checkInterval: 30000 },
      { name: 'disk_usage', threshold: 90, checkInterval: 60000 },
      { name: 'active_print_jobs', threshold: 50, checkInterval: 10000 },
      { name: 'failed_jobs_rate', threshold: 5, checkInterval: 60000 }
    ];

    metricsToMonitor.forEach(metric => {
      setInterval(async () => {
        try {
          const value = await this.metricsCollector.getMetric(metric.name);

          if (value > metric.threshold) {
            await this.alertManager.sendThresholdAlert({
              metricName: metric.name,
              currentValue: value,
              threshold: metric.threshold,
              timestamp: Date.now()
            });
          }
        } catch (error) {
          console.error(`指标监控失败 ${metric.name}:`, error);
        }
      }, metric.checkInterval);
    });
  }
}

// 具体健康检查实现
class DatabaseHealthCheck implements HealthCheck {
  name = 'DatabaseHealthCheck';

  async execute(): Promise<HealthCheckResult> {
    try {
      const startTime = Date.now();

      // 检查数据库连接
      const connectionResult = await this.checkConnection();
      if (!connectionResult.success) {
        return {
          checkName: this.name,
          status: 'unhealthy',
          severity: 'critical',
          message: `数据库连接失败: ${connectionResult.error}`,
          details: connectionResult
        };
      }

      // 检查数据库性能
      const performanceResult = await this.checkPerformance();
      if (!performanceResult.success) {
        return {
          checkName: this.name,
          status: 'unhealthy',
          severity: 'warning',
          message: `数据库性能问题: ${performanceResult.error}`,
          details: performanceResult
        };
      }

      // 检查数据库空间
      const spaceResult = await this.checkDiskSpace();
      if (!spaceResult.success) {
        return {
          checkName: this.name,
          status: 'unhealthy',
          severity: spaceResult.freeSpacePercentage < 5 ? 'critical' : 'warning',
          message: `数据库空间不足: ${spaceResult.error}`,
          details: spaceResult
        };
      }

      return {
        checkName: this.name,
        status: 'healthy',
        severity: 'info',
        message: '数据库运行正常',
        duration: Date.now() - startTime,
        details: {
          connection: connectionResult,
          performance: performanceResult,
          space: spaceResult
        }
      };

    } catch (error) {
      return {
        checkName: this.name,
        status: 'unhealthy',
        severity: 'critical',
        message: `数据库健康检查失败: ${error.message}`,
        error: error.message
      };
    }
  }

  private async checkConnection(): Promise<ConnectionResult> {
    try {
      const startTime = Date.now();
      await this.db.get('SELECT 1');
      const responseTime = Date.now() - startTime;

      return {
        success: true,
        responseTime,
        isHealthy: responseTime < 1000
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        responseTime: -1,
        isHealthy: false
      };
    }
  }

  private async checkPerformance(): Promise<PerformanceResult> {
    try {
      const queries = [
        'SELECT COUNT(*) FROM orders',
        'SELECT COUNT(*) FROM print_jobs WHERE status = "processing"',
        'SELECT COUNT(*) FROM sync_queue WHERE status = "pending"'
      ];

      const results = await Promise.all(
        queries.map(async query => {
          const startTime = Date.now();
          await this.db.get(query);
          return Date.now() - startTime;
        })
      );

      const averageQueryTime = results.reduce((sum, time) => sum + time, 0) / results.length;

      return {
        success: averageQueryTime < 500,
        averageQueryTime,
        slowQueries: results.filter(time => time > 1000).length,
        error: averageQueryTime >= 500 ? `平均查询时间过长: ${averageQueryTime}ms` : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        averageQueryTime: -1,
        slowQueries: 0
      };
    }
  }

  private async checkDiskSpace(): Promise<DiskSpaceResult> {
    try {
      const stats = await fs.statSync('./data');
      // 这里应该实现实际的磁盘空间检查
      // 简化实现
      return {
        success: true,
        totalSpace: 1000000000, // 1GB
        freeSpace: 500000000,   // 500MB
        freeSpacePercentage: 50
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        totalSpace: 0,
        freeSpace: 0,
        freeSpacePercentage: 0
      };
    }
  }
}

// 自动恢复管理器
class AutoRecoveryManager {
  private recoveryStrategies: Map<string, RecoveryStrategy[]> = new Map();

  constructor() {
    this.initializeRecoveryStrategies();
  }

  private initializeRecoveryStrategies(): void {
    this.recoveryStrategies.set('DatabaseHealthCheck', [
      new DatabaseConnectionRecovery(),
      new DatabaseRestartRecovery()
    ]);

    this.recoveryStrategies.set('MemoryHealthCheck', [
      new MemoryCleanupRecovery(),
      new ServiceRestartRecovery()
    ]);

    this.recoveryStrategies.set('PrinterHealthCheck', [
      new PrinterReconnectionRecovery(),
      new PrintServiceRestartRecovery()
    ]);

    this.recoveryStrategies.set('StorageHealthCheck', [
      new TempFileCleanupRecovery(),
      new LogRotationRecovery()
    ]);
  }

  async attemptAutoRecovery(report: HealthReport): Promise<RecoveryResult[]> {
    const recoveryResults: RecoveryResult[] = [];

    const unhealthyChecks = report.checks.filter(check =>
      check.status === 'unhealthy' && check.severity === 'critical'
    );

    for (const check of unhealthyChecks) {
      const strategies = this.recoveryStrategies.get(check.checkName) || [];

      for (const strategy of strategies) {
        try {
          console.log(`尝试自动恢复: ${check.checkName} - ${strategy.name}`);

          const result = await strategy.execute(check);
          recoveryResults.push({
            checkName: check.checkName,
            strategyName: strategy.name,
            success: result.success,
            message: result.message,
            duration: result.duration
          });

          if (result.success) {
            console.log(`自动恢复成功: ${check.checkName} - ${strategy.name}`);
            break; // 恢复成功，跳出策略循环
          }
        } catch (error) {
          recoveryResults.push({
            checkName: check.checkName,
            strategyName: strategy.name,
            success: false,
            message: `恢复策略执行失败: ${error.message}`,
            duration: 0
          });
        }
      }
    }

    return recoveryResults;
  }
}

// 告警管理器
class AlertManager {
  private alertChannels: AlertChannel[] = [];

  constructor() {
    this.initializeAlertChannels();
  }

  private initializeAlertChannels(): void {
    this.alertChannels = [
      new EmailAlertChannel(),
      new LogAlertChannel(),
      new WebhookAlertChannel()
    ];
  }

  async sendAlert(report: HealthReport | any): Promise<void> {
    const alertMessage = this.formatAlertMessage(report);

    const sendPromises = this.alertChannels.map(async channel => {
      try {
        await channel.send(alertMessage);
        console.log(`告警发送成功: ${channel.name}`);
      } catch (error) {
        console.error(`告警发送失败 ${channel.name}:`, error);
      }
    });

    await Promise.allSettled(sendPromises);
  }

  async sendThresholdAlert(alert: ThresholdAlert): Promise<void> {
    const message = {
      type: 'threshold_exceeded',
      title: '指标阈值告警',
      message: `${alert.metricName} 超过阈值: 当前值 ${alert.currentValue}, 阈值 ${alert.threshold}`,
      timestamp: alert.timestamp,
      severity: 'warning'
    };

    await this.sendAlert(message);
  }

  private formatAlertMessage(report: any): AlertMessage {
    if (report.overallHealth) {
      // 健康检查报告
      const criticalIssues = report.checks.filter(c => c.severity === 'critical' && c.status === 'unhealthy');

      return {
        type: 'health_check',
        title: '系统健康告警',
        message: `系统健康状态: ${report.overallHealth}, 发现 ${criticalIssues.length} 个严重问题`,
        details: criticalIssues.map(issue => issue.message).join('; '),
        timestamp: report.timestamp,
        severity: report.overallHealth === 'critical' ? 'critical' : 'warning'
      };
    } else {
      // 其他类型告警
      return {
        type: report.type || 'general',
        title: '系统告警',
        message: report.message,
        timestamp: report.timestamp || Date.now(),
        severity: report.severity || 'warning'
      };
    }
  }
}

// 数据类型定义
interface HealthCheck {
  name: string;
  execute(): Promise<HealthCheckResult>;
}

interface HealthCheckResult {
  checkName: string;
  status: HealthStatus;
  severity: Severity;
  message: string;
  duration?: number;
  details?: any;
  error?: string;
}

interface HealthReport {
  timestamp: number;
  overallHealth: HealthStatus;
  duration: number;
  checks: HealthCheckResult[];
  summary: HealthSummary;
  recommendations: string[];
  metrics: SystemMetrics;
}

interface HealthSummary {
  totalChecks: number;
  healthyCount: number;
  warningCount: number;
  criticalCount: number;
  healthPercentage: number;
}

type HealthStatus = 'healthy' | 'warning' | 'critical';
type Severity = 'info' | 'warning' | 'critical';

interface RecoveryStrategy {
  name: string;
  execute(check: HealthCheckResult): Promise<{ success: boolean; message: string; duration: number }>;
}

interface RecoveryResult {
  checkName: string;
  strategyName: string;
  success: boolean;
  message: string;
  duration: number;
}

interface AlertChannel {
  name: string;
  send(message: AlertMessage): Promise<void>;
}

interface AlertMessage {
  type: string;
  title: string;
  message: string;
  details?: string;
  timestamp: number;
  severity: Severity;
}

interface ThresholdAlert {
  metricName: string;
  currentValue: number;
  threshold: number;
  timestamp: number;
}

interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  activePrintJobs: number;
  failedJobsRate: number;
  networkLatency: number;
}
```

### 📈 优化效果预期
- **问题发现时间**: 从被动发现减少到主动发现，平均发现时间从2小时减少到5分钟
- **系统可用性**: 从95%提升到99.5%
- **自动恢复成功率**: 常见问题自动恢复率达到80%
- **运维效率**: 人工干预需求减少60%

---

## 📋 实施优先级和建议

### 🎯 实施优先级排序

#### **第一阶段 (高优先级) - 核心稳定性**
1. **订单状态流转逻辑优化** ⭐⭐⭐⭐⭐
   - 影响: 业务流程可靠性
   - 实施难度: 中等
   - 预期收益: 高

2. **Ghostscript处理流程的错误恢复** ⭐⭐⭐⭐⭐
   - 影响: 文档处理成功率
   - 实施难度: 高
   - 预期收益: 高

3. **数据同步机制的可靠性优化** ⭐⭐⭐⭐
   - 影响: 数据一致性
   - 实施难度: 中等
   - 预期收益: 中高

#### **第二阶段 (中优先级) - 性能提升**
4. **文件下载系统的容错机制** ⭐⭐⭐
   - 影响: 下载成功率
   - 实施难度: 中等
   - 预期收益: 中等

5. **打印队列的智能调度优化** ⭐⭐⭐
   - 影响: 资源利用率
   - 实施难度: 高
   - 预期收益: 中等

6. **系统监控和告警机制** ⭐⭐⭐⭐
   - 影响: 系统可靠性
   - 实施难度: 中等
   - 预期收益: 中高

#### **第三阶段 (低优先级) - 体验优化**
7. **物流发货流程的自动化优化** ⭐⭐
   - 影响: 操作效率
   - 实施难度: 中等
   - 预期收益: 中等

8. **用户体验优化** ⭐⭐
   - 影响: 用户满意度
   - 实施难度: 低
   - 预期收益: 中等

### 📅 实施时间规划

#### **第一阶段 (4-6周)**
- 周1-2: 订单状态流转逻辑优化
- 周3-4: Ghostscript错误恢复机制
- 周5-6: 数据同步可靠性优化

#### **第二阶段 (4-5周)**
- 周7-8: 文件下载容错机制
- 周9-10: 打印队列智能调度
- 周11: 系统监控和告警

#### **第三阶段 (3-4周)**
- 周12-13: 物流自动化优化
- 周14-15: 用户体验优化

### 🔧 实施建议

#### **技术准备**
1. **代码审查**: 对现有代码进行全面审查，识别潜在问题
2. **测试环境**: 建立完整的测试环境，确保安全实施
3. **备份策略**: 制定完整的数据备份和回滚策略
4. **监控工具**: 部署必要的监控和日志工具

#### **团队协作**
1. **分工明确**: 根据团队成员技能分配任务
2. **定期评审**: 每周进行进度评审和问题讨论
3. **文档更新**: 及时更新技术文档和操作手册
4. **知识分享**: 定期进行技术分享和培训

#### **风险控制**
1. **渐进式部署**: 采用灰度发布，逐步推广新功能
2. **回滚准备**: 每个阶段都准备快速回滚方案
3. **性能监控**: 密切监控系统性能指标
4. **用户反馈**: 收集用户反馈，及时调整优化方案

### 📊 成功指标

#### **技术指标**
- 系统可用性: 99.5%+
- 订单处理成功率: 98%+
- 平均响应时间: <2秒
- 错误恢复成功率: 85%+

#### **业务指标**
- 客户满意度: 95%+
- 订单处理时间: 减少40%
- 人工干预率: 减少60%
- 系统维护成本: 减少30%

---

## 🏗️ 开发规范与架构原则

> **📋 基于模块功能开发规范的架构指导**
> 本章节整合了模块功能开发规范中的核心理念，为7.30重构提供统一的开发标准和架构原则。

### 🎯 核心架构理念

#### **本地优先架构 (Local-First Architecture)**
遵循"**本地是宇宙中心，云端只是消息队列**"的核心理念：

- **唯一权威数据源**: 本地 SQLite 数据库是应用程序状态的**唯一**真相来源
- **单向数据流**: 数据主要从云端单向流入本地，除发货回写外严禁向云端写入中间状态
- **胖客户端，瘦云端**: 复杂业务逻辑、文件处理、状态管理全部在本地完成

### 📐 三大黄金原则

#### **原则一：UI永远不与"云"对话**
```typescript
// ❌ 错误示例 - Vue组件中直接调用云函数
async function handleOrder() {
  const result = await callCloudFunction('updateOrder', data); // 禁止！
}

// ✅ 正确示例 - 通过electronAPI调用本地服务
async function handleOrder() {
  const result = await window.electronAPI.updateOrderStatus(orderId, status);
}
```

**实施要求**：
- Vue 组件中**绝对不能**出现 `callCloudFunction` 或直接网络请求
- 所有数据操作必须通过 `window.electronAPI` 接口
- UI层只负责数据展示和操作触发

#### **原则二：状态变更只发生在本地**
```typescript
// ✅ 正确的状态管理
class OrderStatusManager {
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    // 1. 更新本地数据库
    await this.db.run(
      'UPDATE orders SET status = ?, update_time = ? WHERE id = ?',
      [status, Date.now(), orderId]
    );

    // 2. 触发本地事件
    this.eventEmitter.emit('orderStatusChanged', { orderId, status });

    // 3. 仅在特定状态时同步到云端（如发货完成）
    if (status === OrderStatus.SHIPPED) {
      await this.syncToCloud(orderId, status);
    }
  }
}
```

**实施要求**：
- 订单中间状态只更新本地数据库
- 严禁将处理中状态同步回云端
- 只有最终状态（如发货完成）才同步到云端

#### **原则三：业务逻辑严禁放入UI层**
```typescript
// ❌ 错误示例 - 业务逻辑在Vue组件中
<script setup>
function calculatePrintCost(order) {
  let cost = 0;
  order.files.forEach(file => {
    if (file.colorMode === 'color') {
      cost += file.pageCount * 0.5;
    } else {
      cost += file.pageCount * 0.1;
    }
  });
  return cost; // 业务逻辑不应在UI层！
}
</script>

// ✅ 正确示例 - 业务逻辑在服务层
// services/pricing-service.ts
export class PricingService {
  calculatePrintCost(order: Order): number {
    return order.files.reduce((total, file) => {
      const unitPrice = this.getUnitPrice(file.colorMode, file.paperType);
      return total + (file.pageCount * unitPrice);
    }, 0);
  }
}

// Vue组件中只调用服务
<script setup>
async function getCost(order) {
  return await window.electronAPI.calculatePrintCost(order.id);
}
</script>
```

### 🔄 标准开发流程：四步走

#### **第1步：UI层 (Vue组件) - 发出指令**
```vue
<template>
  <el-button @click="handlePrintOrder(order.id)">开始打印</el-button>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';

async function handlePrintOrder(orderId: string) {
  try {
    const result = await window.electronAPI.startPrintJob(orderId);
    if (result.success) {
      ElMessage.success('打印任务已启动');
      await refreshOrderList(); // 刷新本地数据
    } else {
      ElMessage.error(`打印失败: ${result.error}`);
    }
  } catch (error) {
    console.error('打印操作失败:', error);
    ElMessage.error('操作失败');
  }
}
</script>
```

#### **第2步：API定义层 (Preload & Types) - 声明接口**
```typescript
// preload.ts
contextBridge.exposeInMainWorld('electronAPI', {
  // 打印相关API
  startPrintJob: (orderId: string) => ipcRenderer.invoke('print:start-job', orderId),
  getPrintQueue: () => ipcRenderer.invoke('print:get-queue'),
  cancelPrintJob: (jobId: string) => ipcRenderer.invoke('print:cancel-job', jobId),
});

// electron.d.ts
export interface ElectronAPI {
  startPrintJob: (orderId: string) => Promise<{ success: boolean; jobId?: string; error?: string }>;
  getPrintQueue: () => Promise<PrintJob[]>;
  cancelPrintJob: (jobId: string) => Promise<{ success: boolean; error?: string }>;
}
```

#### **第3步：IPC路由层 (Handlers) - 接收指令**
```typescript
// ipc/print-handlers.ts
export function setupPrintHandlers() {
  ipcMain.handle('print:start-job', async (event, orderId) => {
    try {
      const jobId = await printQueueService.createPrintJob(orderId);
      await printQueueService.startPrinting(jobId);
      return { success: true, jobId };
    } catch (error: any) {
      console.error(`Failed to start print job for order ${orderId}:`, error);
      return { success: false, error: error.message };
    }
  });

  ipcMain.handle('print:get-queue', async () => {
    return await printQueueService.getQueueStatus();
  });

  ipcMain.handle('print:cancel-job', async (event, jobId) => {
    try {
      await printQueueService.cancelJob(jobId);
      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  });
}
```

#### **第4步：服务与数据层 (Service/DB) - 执行指令**
```typescript
// services/print-queue-service.ts
export class PrintQueueService {
  async createPrintJob(orderId: string): Promise<string> {
    const order = await this.databaseService.getOrder(orderId);
    if (!order) {
      throw new Error('订单不存在');
    }

    const jobId = this.generateJobId();
    const job: PrintJob = {
      id: jobId,
      orderId,
      status: 'queued',
      priority: this.calculatePriority(order),
      createTime: Date.now(),
      printerName: await this.selectOptimalPrinter(order)
    };

    // 保存到本地数据库
    await this.databaseService.createPrintJob(job);

    // 更新订单状态
    await this.databaseService.updateOrderStatus(orderId, OrderStatus.PENDING_PRINT);

    return jobId;
  }

  async startPrinting(jobId: string): Promise<void> {
    const job = await this.databaseService.getPrintJob(jobId);
    if (!job) {
      throw new Error('打印任务不存在');
    }

    // 更新任务状态
    await this.databaseService.updatePrintJobStatus(jobId, 'printing');

    // 启动实际打印流程
    await this.ghostscriptService.processPrintJob(job);
  }
}
```

### 📊 数据操作规范

#### **文件操作 - 非破坏性原则**
```typescript
class FileProcessingService {
  async convertToGrayscale(originalFileId: string): Promise<string> {
    const originalFile = await this.databaseService.getFile(originalFileId);

    // 1. 生成新文件路径
    const newFilePath = this.generateProcessedFilePath(originalFile.path, 'grayscale');

    // 2. 执行转换（生成新文件）
    await this.imageProcessor.convertToGrayscale(originalFile.path, newFilePath);

    // 3. 在数据库中创建新记录
    const newFileId = await this.databaseService.createFile({
      path: newFilePath,
      originalName: originalFile.originalName,
      sourceFileId: originalFileId, // 指向原文件
      status: 'processed',
      processType: 'grayscale',
      createTime: Date.now()
    });

    // 4. 更新原文件状态
    await this.databaseService.updateFileStatus(originalFileId, 'source-processed');

    return newFileId;
  }
}
```

**关键要求**：
- ✅ 生成新文件，创建新记录
- ✅ 通过 `source_file_id` 建立关联
- ❌ 绝对禁止覆盖原始文件

#### **配置管理 - 集中化原则**
```typescript
class ConfigService {
  // ✅ 正确的配置获取方式
  async getPrinterSettings(): Promise<PrinterSettings> {
    return await this.get('printer_settings', DEFAULT_PRINTER_SETTINGS);
  }

  async getStoragePath(): Promise<string> {
    return await this.get('storage_path', './data/files');
  }

  // ❌ 禁止硬编码
  // const STORAGE_PATH = 'C:/PrintShop/files'; // 不要这样做！
}

// 使用示例
class FileService {
  async saveFile(file: Buffer, filename: string): Promise<string> {
    // ✅ 从配置获取路径
    const storagePath = await this.configService.getStoragePath();
    const fullPath = path.join(storagePath, filename);

    await fs.writeFile(fullPath, file);
    return fullPath;
  }
}
```

#### **数据库交互 - 服务封装原则**
```typescript
// ✅ 正确示例 - 通过服务层操作数据库
class OrderService {
  async getOrdersWithFiles(status?: OrderStatus): Promise<OrderWithFiles[]> {
    return await this.databaseService.getOrdersWithFiles(status);
  }

  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    await this.databaseService.updateOrderStatus(orderId, status);

    // 触发相关业务逻辑
    await this.handleStatusChange(orderId, status);
  }
}

// ❌ 错误示例 - 直接操作数据库
class SomeService {
  async badExample() {
    const db = new Database('./app.db'); // 不要这样做！
    const result = await db.get('SELECT * FROM orders'); // 避免裸SQL！
  }
}
```

### 🔧 实施指导

#### **代码审查检查点**
1. **UI层检查**：
   - [ ] Vue组件中无云函数调用
   - [ ] 无直接网络请求
   - [ ] 无业务逻辑计算

2. **API层检查**：
   - [ ] 所有API都在preload.ts中声明
   - [ ] 类型定义完整
   - [ ] 错误处理规范

3. **服务层检查**：
   - [ ] 业务逻辑封装在服务中
   - [ ] 数据库操作通过服务层
   - [ ] 配置通过ConfigService获取

4. **数据层检查**：
   - [ ] 文件操作遵循非破坏性原则
   - [ ] 状态变更记录完整
   - [ ] 无硬编码配置

#### **性能优化建议**
```typescript
// 批量操作优化
class BatchOperationService {
  async batchUpdateOrderStatus(orderIds: string[], status: OrderStatus): Promise<void> {
    const db = await this.databaseService.getDb();

    // 使用事务提高性能
    await db.run('BEGIN TRANSACTION');
    try {
      for (const orderId of orderIds) {
        await db.run(
          'UPDATE orders SET status = ?, update_time = ? WHERE id = ?',
          [status, Date.now(), orderId]
        );
      }
      await db.run('COMMIT');
    } catch (error) {
      await db.run('ROLLBACK');
      throw error;
    }
  }
}

// 缓存机制
class CacheService {
  private cache = new Map<string, { data: any; expiry: number }>();

  async getCachedData<T>(key: string, fetcher: () => Promise<T>, ttl: number = 300000): Promise<T> {
    const cached = this.cache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }

    const data = await fetcher();
    this.cache.set(key, { data, expiry: Date.now() + ttl });
    return data;
  }
}
```

---

*本优化方案将根据实施进度和反馈持续更新和完善*