<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云打印终端 - 智能打印管理系统</title>
    
    <!-- Vuetify CSS -->
    <link href="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
    <!-- Material Design Icons -->
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.3.67/css/materialdesignicons.min.css" rel="stylesheet">
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Materio主题色彩 */
            --primary-color: #5d87ff;
            --primary-light: #ecf2ff;
            --primary-dark: #4570ea;
            --secondary-color: #49beff;
            --success-color: #13deb9;
            --warning-color: #ffae1f;
            --danger-color: #fa896b;
            --info-color: #539bff;
            
            /* 文字颜色 */
            --text-primary: #2a3547;
            --text-regular: #5a6a85;
            --text-secondary: #7c8fac;
            --text-placeholder: #adb5bd;
            
            /* 背景颜色 */
            --bg-color: #ffffff;
            --bg-page: #f5f5f9;
            --bg-overlay: rgba(0, 0, 0, 0.8);
            --bg-light: #f9f9fd;
            
            /* 边框颜色 */
            --border-base: #e5eaef;
            --border-light: #f1f5f9;
            --border-lighter: #f8fafc;
            
            /* 阴影 */
            --shadow-base: 0 1px 4px rgba(0, 0, 0, 0.08);
            --shadow-hover: 0 4px 20px rgba(93, 135, 255, 0.15);
            --shadow-card: 0 0 2px rgba(145, 158, 171, 0.2), 0 12px 24px -4px rgba(145, 158, 171, 0.12);
            
            /* 圆角 */
            --border-radius-small: 4px;
            --border-radius-base: 7px;
            --border-radius-large: 12px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-page);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 14px;
        }
        
        /* 自定义Vuetify样式 */
        .v-application {
            font-family: 'Inter', sans-serif !important;
        }
        
        .v-card {
            box-shadow: var(--shadow-card) !important;
            border-radius: var(--border-radius-base) !important;
        }
        
        .v-btn {
            border-radius: var(--border-radius-small) !important;
            text-transform: none !important;
            font-weight: 500 !important;
        }
        
        .v-navigation-drawer {
            border-right: 1px solid var(--border-light) !important;
        }
        
        .v-app-bar {
            border-bottom: 1px solid var(--border-light) !important;
            backdrop-filter: blur(10px) !important;
        }
        
        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-shipped {
            background: #e2e3f1;
            color: #383874;
        }
        
        /* 统计卡片 */
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: var(--border-radius-large) !important;
        }
        
        .stats-card-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #00c9a7 100%);
        }
        
        .stats-card-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #ff9f43 100%);
        }
        
        .stats-card-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #ff6b6b 100%);
        }
        
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in-left {
            animation: slideInLeft 0.3s ease-out;
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        /* 打印机状态指示 */
        .printer-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .printer-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .printer-online {
            background: var(--success-color);
        }
        
        .printer-offline {
            background: var(--text-placeholder);
        }
        
        .printer-busy {
            background: var(--warning-color);
        }
        
        /* 进度条样式 */
        .progress-container {
            background: var(--border-lighter);
            border-radius: 10px;
            overflow: hidden;
            height: 8px;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 10px;
            transition: width 0.3s ease;
        }
    </style>
</head>

<body>
    <div id="app">
        <v-app>
            <!-- 侧边导航栏 -->
            <v-navigation-drawer
                v-model="drawer"
                :rail="rail"
                permanent
                @click="rail = false"
                color="white"
                width="280"
                rail-width="72"
            >
                <!-- Logo区域 -->
                <div class="pa-6 pb-4">
                    <div class="d-flex align-center">
                        <v-avatar size="40" class="mr-3">
                            <v-icon color="white" size="24">mdi-printer-3d</v-icon>
                        </v-avatar>
                        <div v-show="!rail">
                            <div class="text-h6 font-weight-bold text-primary">云打印终端</div>
                            <div class="text-caption text-medium-emphasis">智能打印管理</div>
                        </div>
                    </div>
                </div>

                <v-divider></v-divider>

                <!-- 导航菜单 -->
                <v-list density="compact" nav>
                    <!-- 仪表盘 -->
                    <v-list-item
                        prepend-icon="mdi-view-dashboard"
                        title="仪表盘"
                        value="dashboard"
                        :active="currentPage === 'dashboard'"
                        @click="currentPage = 'dashboard'"
                        class="mb-1"
                    ></v-list-item>

                    <!-- 订单管理 -->
                    <v-list-group value="orders">
                        <template v-slot:activator="{ props }">
                            <v-list-item
                                v-bind="props"
                                prepend-icon="mdi-file-document-multiple"
                                title="订单管理"
                            ></v-list-item>
                        </template>
                        <v-list-item
                            prepend-icon="mdi-clock-outline"
                            title="待处理订单"
                            value="pending-orders"
                            :active="currentPage === 'pending-orders'"
                            @click="currentPage = 'pending-orders'"
                        ></v-list-item>
                        <v-list-item
                            prepend-icon="mdi-printer"
                            title="打印中订单"
                            value="printing-orders"
                            :active="currentPage === 'printing-orders'"
                            @click="currentPage = 'printing-orders'"
                        ></v-list-item>
                        <v-list-item
                            prepend-icon="mdi-check-circle"
                            title="已完成订单"
                            value="completed-orders"
                            :active="currentPage === 'completed-orders'"
                            @click="currentPage = 'completed-orders'"
                        ></v-list-item>
                    </v-list-group>

                    <!-- 打印管理 -->
                    <v-list-group value="printing">
                        <template v-slot:activator="{ props }">
                            <v-list-item
                                v-bind="props"
                                prepend-icon="mdi-printer-settings"
                                title="打印管理"
                            ></v-list-item>
                        </template>
                        <v-list-item
                            prepend-icon="mdi-format-list-bulleted"
                            title="打印队列"
                            value="print-queue"
                            :active="currentPage === 'print-queue'"
                            @click="currentPage = 'print-queue'"
                        ></v-list-item>
                        <v-list-item
                            prepend-icon="mdi-printer-outline"
                            title="打印机管理"
                            value="printers"
                            :active="currentPage === 'printers'"
                            @click="currentPage = 'printers'"
                        ></v-list-item>
                    </v-list-group>

                    <!-- 物流发货 -->
                    <v-list-item
                        prepend-icon="mdi-truck-delivery"
                        title="物流发货"
                        value="logistics"
                        :active="currentPage === 'logistics'"
                        @click="currentPage = 'logistics'"
                        class="mb-1"
                    ></v-list-item>

                    <!-- 文件管理 -->
                    <v-list-item
                        prepend-icon="mdi-folder-multiple"
                        title="文件管理"
                        value="files"
                        :active="currentPage === 'files'"
                        @click="currentPage = 'files'"
                        class="mb-1"
                    ></v-list-item>

                    <v-divider class="my-4"></v-divider>

                    <!-- 系统设置 -->
                    <v-list-item
                        prepend-icon="mdi-cog"
                        title="系统设置"
                        value="settings"
                        :active="currentPage === 'settings'"
                        @click="currentPage = 'settings'"
                        class="mb-1"
                    ></v-list-item>

                    <!-- 同步状态 -->
                    <v-list-item
                        prepend-icon="mdi-sync"
                        title="同步状态"
                        value="sync"
                        :active="currentPage === 'sync'"
                        @click="currentPage = 'sync'"
                        class="mb-1"
                    ></v-list-item>
                </v-list>

                <!-- 底部状态 -->
                <template v-slot:append>
                    <div class="pa-4" v-show="!rail">
                        <v-card class="pa-3" color="primary" variant="tonal">
                            <div class="d-flex align-center">
                                <v-icon class="mr-2" color="primary">mdi-wifi</v-icon>
                                <div>
                                    <div class="text-caption font-weight-medium">网络状态</div>
                                    <div class="text-caption text-success">已连接</div>
                                </div>
                            </div>
                        </v-card>
                    </div>
                </template>
            </v-navigation-drawer>

            <!-- 顶部应用栏 -->
            <v-app-bar
                :order="-1"
                color="white"
                height="80"
                flat
            >
                <v-app-bar-nav-icon
                    variant="text"
                    @click.stop="rail = !rail"
                ></v-app-bar-nav-icon>

                <!-- 面包屑导航 -->
                <div class="ml-4">
                    <v-breadcrumbs
                        :items="breadcrumbs"
                        class="pa-0"
                        density="compact"
                    >
                        <template v-slot:divider>
                            <v-icon icon="mdi-chevron-right"></v-icon>
                        </template>
                    </v-breadcrumbs>
                    <div class="text-h5 font-weight-bold mt-1">{{ pageTitle }}</div>
                </div>

                <v-spacer></v-spacer>

                <!-- 搜索框 -->
                <v-text-field
                    v-model="searchQuery"
                    prepend-inner-icon="mdi-magnify"
                    placeholder="搜索订单、文件..."
                    variant="outlined"
                    density="compact"
                    hide-details
                    style="max-width: 300px;"
                    class="mr-4"
                ></v-text-field>

                <!-- 通知按钮 -->
                <v-btn
                    icon="mdi-bell-outline"
                    variant="text"
                    class="mr-2"
                    @click="showNotifications = !showNotifications"
                >
                    <v-icon>mdi-bell-outline</v-icon>
                    <v-badge
                        v-if="notifications.length > 0"
                        :content="notifications.length"
                        color="error"
                        offset-x="2"
                        offset-y="2"
                    ></v-badge>
                </v-btn>

                <!-- 用户头像 -->
                <v-menu>
                    <template v-slot:activator="{ props }">
                        <v-btn
                            v-bind="props"
                            icon
                            class="mr-2"
                        >
                            <v-avatar size="36" color="primary">
                                <span class="text-white font-weight-medium">管</span>
                            </v-avatar>
                        </v-btn>
                    </template>
                    <v-list>
                        <v-list-item>
                            <v-list-item-title>管理员</v-list-item-title>
                            <v-list-item-subtitle><EMAIL></v-list-item-subtitle>
                        </v-list-item>
                        <v-divider></v-divider>
                        <v-list-item prepend-icon="mdi-account" title="个人资料"></v-list-item>
                        <v-list-item prepend-icon="mdi-cog" title="设置"></v-list-item>
                        <v-list-item prepend-icon="mdi-logout" title="退出登录"></v-list-item>
                    </v-list>
                </v-menu>
            </v-app-bar>

            <!-- 主内容区域 -->
            <v-main>
                <v-container fluid class="pa-6">
                    <!-- 仪表盘页面 -->
                    <div v-if="currentPage === 'dashboard'" class="fade-in">
                        <!-- 统计卡片行 -->
                        <v-row class="mb-6">
                            <v-col cols="12" sm="6" md="3">
                                <v-card class="stats-card pa-4" elevation="0">
                                    <div class="d-flex align-center justify-space-between">
                                        <div>
                                            <div class="text-h4 font-weight-bold mb-1">{{ stats.totalOrders }}</div>
                                            <div class="text-body-2 opacity-90">总订单数</div>
                                        </div>
                                        <v-icon size="40" class="opacity-80">mdi-file-document-multiple</v-icon>
                                    </div>
                                    <div class="mt-3 d-flex align-center">
                                        <v-icon size="16" class="mr-1">mdi-trending-up</v-icon>
                                        <span class="text-caption">+12% 较昨日</span>
                                    </div>
                                </v-card>
                            </v-col>

                            <v-col cols="12" sm="6" md="3">
                                <v-card class="stats-card-success pa-4" elevation="0">
                                    <div class="d-flex align-center justify-space-between">
                                        <div>
                                            <div class="text-h4 font-weight-bold mb-1 text-white">{{ stats.completedOrders }}</div>
                                            <div class="text-body-2 text-white opacity-90">已完成</div>
                                        </div>
                                        <v-icon size="40" class="opacity-80 text-white">mdi-check-circle</v-icon>
                                    </div>
                                    <div class="mt-3 d-flex align-center text-white">
                                        <v-icon size="16" class="mr-1">mdi-trending-up</v-icon>
                                        <span class="text-caption">+8% 较昨日</span>
                                    </div>
                                </v-card>
                            </v-col>

                            <v-col cols="12" sm="6" md="3">
                                <v-card class="stats-card-warning pa-4" elevation="0">
                                    <div class="d-flex align-center justify-space-between">
                                        <div>
                                            <div class="text-h4 font-weight-bold mb-1 text-white">{{ stats.printingOrders }}</div>
                                            <div class="text-body-2 text-white opacity-90">打印中</div>
                                        </div>
                                        <v-icon size="40" class="opacity-80 text-white">mdi-printer</v-icon>
                                    </div>
                                    <div class="mt-3 d-flex align-center text-white">
                                        <v-icon size="16" class="mr-1">mdi-clock-outline</v-icon>
                                        <span class="text-caption">实时更新</span>
                                    </div>
                                </v-card>
                            </v-col>

                            <v-col cols="12" sm="6" md="3">
                                <v-card class="stats-card-danger pa-4" elevation="0">
                                    <div class="d-flex align-center justify-space-between">
                                        <div>
                                            <div class="text-h4 font-weight-bold mb-1 text-white">{{ stats.onlinePrinters }}</div>
                                            <div class="text-body-2 text-white opacity-90">在线打印机</div>
                                        </div>
                                        <v-icon size="40" class="opacity-80 text-white">mdi-printer-outline</v-icon>
                                    </div>
                                    <div class="mt-3 d-flex align-center text-white">
                                        <v-icon size="16" class="mr-1">mdi-wifi</v-icon>
                                        <span class="text-caption">{{ stats.totalPrinters }}台总计</span>
                                    </div>
                                </v-card>
                            </v-col>
                        </v-row>

                        <!-- 主要内容区域 -->
                        <v-row>
                            <!-- 最近订单 -->
                            <v-col cols="12" md="8">
                                <v-card>
                                    <v-card-title class="d-flex align-center justify-space-between">
                                        <span>最近订单</span>
                                        <v-btn
                                            color="primary"
                                            variant="text"
                                            size="small"
                                            @click="currentPage = 'pending-orders'"
                                        >
                                            查看全部
                                        </v-btn>
                                    </v-card-title>
                                    <v-card-text>
                                        <v-data-table
                                            :headers="orderHeaders"
                                            :items="recentOrders"
                                            :items-per-page="5"
                                            hide-default-footer
                                            class="elevation-0"
                                        >
                                            <template v-slot:item.status="{ item }">
                                                <span :class="getStatusClass(item.status)">
                                                    {{ getStatusText(item.status) }}
                                                </span>
                                            </template>
                                            <template v-slot:item.actions="{ item }">
                                                <v-btn
                                                    icon="mdi-eye"
                                                    variant="text"
                                                    size="small"
                                                    @click="viewOrder(item)"
                                                ></v-btn>
                                                <v-btn
                                                    v-if="item.status === 'pending'"
                                                    icon="mdi-printer"
                                                    variant="text"
                                                    size="small"
                                                    color="primary"
                                                    @click="startPrint(item)"
                                                ></v-btn>
                                            </template>
                                        </v-data-table>
                                    </v-card-text>
                                </v-card>
                            </v-col>

                            <!-- 打印机状态 -->
                            <v-col cols="12" md="4">
                                <v-card class="mb-4">
                                    <v-card-title>打印机状态</v-card-title>
                                    <v-card-text>
                                        <div v-for="printer in printers" :key="printer.id" class="mb-3">
                                            <div class="d-flex align-center justify-space-between mb-2">
                                                <div class="d-flex align-center">
                                                    <div class="printer-status-dot mr-2" :class="getPrinterStatusClass(printer.status)"></div>
                                                    <div>
                                                        <div class="font-weight-medium">{{ printer.name }}</div>
                                                        <div class="text-caption text-medium-emphasis">{{ printer.model }}</div>
                                                    </div>
                                                </div>
                                                <v-chip
                                                    :color="getPrinterStatusColor(printer.status)"
                                                    size="small"
                                                    variant="tonal"
                                                >
                                                    {{ getPrinterStatusText(printer.status) }}
                                                </v-chip>
                                            </div>
                                            <div v-if="printer.currentJob" class="ml-6">
                                                <div class="text-caption text-medium-emphasis mb-1">
                                                    正在打印: {{ printer.currentJob.orderNo }}
                                                </div>
                                                <div class="progress-container">
                                                    <div class="progress-bar" :style="{ width: printer.currentJob.progress + '%' }"></div>
                                                </div>
                                                <div class="text-caption text-medium-emphasis mt-1">
                                                    {{ printer.currentJob.progress }}% 完成
                                                </div>
                                            </div>
                                        </div>
                                    </v-card-text>
                                </v-card>

                                <!-- 快速操作 -->
                                <v-card>
                                    <v-card-title>快速操作</v-card-title>
                                    <v-card-text>
                                        <v-btn
                                            block
                                            color="primary"
                                            class="mb-3"
                                            prepend-icon="mdi-sync"
                                            @click="syncOrders"
                                        >
                                            同步订单
                                        </v-btn>
                                        <v-btn
                                            block
                                            color="success"
                                            class="mb-3"
                                            prepend-icon="mdi-truck-delivery"
                                            @click="currentPage = 'logistics'"
                                        >
                                            发货管理
                                        </v-btn>
                                        <v-btn
                                            block
                                            color="info"
                                            prepend-icon="mdi-printer-settings"
                                            @click="currentPage = 'printers'"
                                        >
                                            打印机设置
                                        </v-btn>
                                    </v-card-text>
                                </v-card>
                            </v-col>
                        </v-row>
                    </div>

                    <!-- 待处理订单页面 -->
                    <div v-if="currentPage === 'pending-orders'" class="fade-in">
                        <v-card>
                            <v-card-title class="d-flex align-center justify-space-between">
                                <span>待处理订单</span>
                                <div class="d-flex align-center gap-2">
                                    <v-text-field
                                        v-model="orderSearch"
                                        prepend-inner-icon="mdi-magnify"
                                        placeholder="搜索订单..."
                                        variant="outlined"
                                        density="compact"
                                        hide-details
                                        style="max-width: 250px;"
                                    ></v-text-field>
                                    <v-btn
                                        color="primary"
                                        prepend-icon="mdi-refresh"
                                        @click="refreshOrders"
                                    >
                                        刷新
                                    </v-btn>
                                </div>
                            </v-card-title>
                            <v-card-text>
                                <v-data-table
                                    :headers="detailedOrderHeaders"
                                    :items="pendingOrders"
                                    :search="orderSearch"
                                    :items-per-page="10"
                                    class="elevation-0"
                                >
                                    <template v-slot:item.status="{ item }">
                                        <span :class="getStatusClass(item.status)">
                                            {{ getStatusText(item.status) }}
                                        </span>
                                    </template>
                                    <template v-slot:item.priority="{ item }">
                                        <v-chip
                                            :color="getPriorityColor(item.priority)"
                                            size="small"
                                            variant="tonal"
                                        >
                                            {{ item.priority }}
                                        </v-chip>
                                    </template>
                                    <template v-slot:item.actions="{ item }">
                                        <v-btn
                                            icon="mdi-eye"
                                            variant="text"
                                            size="small"
                                            @click="viewOrder(item)"
                                        ></v-btn>
                                        <v-btn
                                            icon="mdi-printer"
                                            variant="text"
                                            size="small"
                                            color="primary"
                                            @click="startPrint(item)"
                                        ></v-btn>
                                        <v-btn
                                            icon="mdi-dots-vertical"
                                            variant="text"
                                            size="small"
                                        ></v-btn>
                                    </template>
                                </v-data-table>
                            </v-card-text>
                        </v-card>
                    </div>

                    <!-- 打印队列页面 -->
                    <div v-if="currentPage === 'print-queue'" class="fade-in">
                        <v-card>
                            <v-card-title>打印队列管理</v-card-title>
                            <v-card-text>
                                <v-alert
                                    v-if="printQueue.length === 0"
                                    type="info"
                                    variant="tonal"
                                    class="mb-4"
                                >
                                    当前没有打印任务
                                </v-alert>
                                <div v-for="job in printQueue" :key="job.id" class="mb-4">
                                    <v-card variant="outlined">
                                        <v-card-text>
                                            <div class="d-flex align-center justify-space-between">
                                                <div>
                                                    <div class="font-weight-medium">{{ job.orderNo }}</div>
                                                    <div class="text-caption text-medium-emphasis">
                                                        打印机: {{ job.printerName }} | 文件: {{ job.fileName }}
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <v-chip
                                                        :color="getJobStatusColor(job.status)"
                                                        size="small"
                                                        variant="tonal"
                                                    >
                                                        {{ job.status }}
                                                    </v-chip>
                                                    <div class="text-caption text-medium-emphasis mt-1">
                                                        {{ job.createdAt }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="job.status === '打印中'" class="mt-3">
                                                <div class="progress-container">
                                                    <div class="progress-bar" :style="{ width: job.progress + '%' }"></div>
                                                </div>
                                                <div class="text-caption text-medium-emphasis mt-1">
                                                    {{ job.progress }}% 完成 (预计剩余: {{ job.estimatedTime }})
                                                </div>
                                            </div>
                                        </v-card-text>
                                    </v-card>
                                </div>
                            </v-card-text>
                        </v-card>
                    </div>

                    <!-- 物流发货页面 -->
                    <div v-if="currentPage === 'logistics'" class="fade-in">
                        <v-row>
                            <v-col cols="12" md="8">
                                <v-card>
                                    <v-card-title>待发货订单</v-card-title>
                                    <v-card-text>
                                        <v-data-table
                                            :headers="logisticsHeaders"
                                            :items="readyToShipOrders"
                                            :items-per-page="10"
                                            class="elevation-0"
                                        >
                                            <template v-slot:item.status="{ item }">
                                                <span :class="getStatusClass(item.status)">
                                                    {{ getStatusText(item.status) }}
                                                </span>
                                            </template>
                                            <template v-slot:item.actions="{ item }">
                                                <v-btn
                                                    color="primary"
                                                    size="small"
                                                    @click="createShipment(item)"
                                                >
                                                    创建发货
                                                </v-btn>
                                            </template>
                                        </v-data-table>
                                    </v-card-text>
                                </v-card>
                            </v-col>
                            <v-col cols="12" md="4">
                                <v-card>
                                    <v-card-title>快速发货</v-card-title>
                                    <v-card-text>
                                        <v-text-field
                                            v-model="barcodeInput"
                                            label="扫描条码"
                                            prepend-inner-icon="mdi-barcode-scan"
                                            variant="outlined"
                                            @keyup.enter="scanBarcode"
                                        ></v-text-field>
                                        <v-select
                                            v-model="selectedLogistics"
                                            :items="logisticsCompanies"
                                            label="物流公司"
                                            variant="outlined"
                                            class="mt-3"
                                        ></v-select>
                                        <v-btn
                                            block
                                            color="primary"
                                            class="mt-3"
                                            @click="quickShip"
                                        >
                                            快速发货
                                        </v-btn>
                                    </v-card-text>
                                </v-card>
                            </v-col>
                        </v-row>
                    </div>
                </v-container>
            </v-main>

            <!-- 通知抽屉 -->
            <v-navigation-drawer
                v-model="showNotifications"
                location="right"
                temporary
                width="400"
            >
                <v-toolbar color="primary" dark>
                    <v-toolbar-title>通知中心</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon @click="showNotifications = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-list>
                    <v-list-item
                        v-for="notification in notifications"
                        :key="notification.id"
                        class="mb-2"
                    >
                        <template v-slot:prepend>
                            <v-avatar :color="notification.type" size="40">
                                <v-icon :icon="notification.icon" color="white"></v-icon>
                            </v-avatar>
                        </template>
                        <v-list-item-title>{{ notification.title }}</v-list-item-title>
                        <v-list-item-subtitle>{{ notification.message }}</v-list-item-subtitle>
                        <v-list-item-subtitle class="text-caption">{{ notification.time }}</v-list-item-subtitle>
                    </v-list-item>
                </v-list>
            </v-navigation-drawer>
        </v-app>
    </div>

    <!-- Vue 3 和 Vuetify 脚本 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>

    <script>
        const { createApp } = Vue;
        const { createVuetify } = Vuetify;

        // 创建Vuetify实例
        const vuetify = createVuetify({
            theme: {
                defaultTheme: 'light',
                themes: {
                    light: {
                        colors: {
                            primary: '#5d87ff',
                            secondary: '#49beff',
                            success: '#13deb9',
                            warning: '#ffae1f',
                            error: '#fa896b',
                            info: '#539bff',
                        }
                    }
                }
            }
        });

        // Vue应用
        createApp({
            data() {
                return {
                    drawer: true,
                    rail: false,
                    currentPage: 'dashboard',
                    searchQuery: '',
                    orderSearch: '',
                    barcodeInput: '',
                    selectedLogistics: '',
                    showNotifications: false,

                    // 统计数据
                    stats: {
                        totalOrders: 1247,
                        completedOrders: 1089,
                        printingOrders: 23,
                        onlinePrinters: 12,
                        totalPrinters: 15
                    },

                    // 表格头部
                    orderHeaders: [
                        { title: '订单号', key: 'orderNo', sortable: true },
                        { title: '客户', key: 'customer', sortable: true },
                        { title: '文件数', key: 'fileCount', sortable: true },
                        { title: '状态', key: 'status', sortable: true },
                        { title: '创建时间', key: 'createdAt', sortable: true },
                        { title: '操作', key: 'actions', sortable: false }
                    ],

                    detailedOrderHeaders: [
                        { title: '订单号', key: 'orderNo', sortable: true },
                        { title: '客户', key: 'customer', sortable: true },
                        { title: '文件数', key: 'fileCount', sortable: true },
                        { title: '优先级', key: 'priority', sortable: true },
                        { title: '状态', key: 'status', sortable: true },
                        { title: '创建时间', key: 'createdAt', sortable: true },
                        { title: '操作', key: 'actions', sortable: false }
                    ],

                    logisticsHeaders: [
                        { title: '订单号', key: 'orderNo', sortable: true },
                        { title: '客户', key: 'customer', sortable: true },
                        { title: '收货地址', key: 'address', sortable: false },
                        { title: '状态', key: 'status', sortable: true },
                        { title: '完成时间', key: 'completedAt', sortable: true },
                        { title: '操作', key: 'actions', sortable: false }
                    ],

                    // 最近订单数据
                    recentOrders: [
                        {
                            id: 1,
                            orderNo: 'ORD-2024-001',
                            customer: '张三',
                            fileCount: 3,
                            status: 'pending',
                            createdAt: '2024-07-31 09:30'
                        },
                        {
                            id: 2,
                            orderNo: 'ORD-2024-002',
                            customer: '李四',
                            fileCount: 1,
                            status: 'printing',
                            createdAt: '2024-07-31 09:15'
                        },
                        {
                            id: 3,
                            orderNo: 'ORD-2024-003',
                            customer: '王五',
                            fileCount: 2,
                            status: 'completed',
                            createdAt: '2024-07-31 08:45'
                        },
                        {
                            id: 4,
                            orderNo: 'ORD-2024-004',
                            customer: '赵六',
                            fileCount: 5,
                            status: 'pending',
                            createdAt: '2024-07-31 08:30'
                        },
                        {
                            id: 5,
                            orderNo: 'ORD-2024-005',
                            customer: '钱七',
                            fileCount: 1,
                            status: 'shipped',
                            createdAt: '2024-07-30 16:20'
                        }
                    ],

                    // 待处理订单
                    pendingOrders: [
                        {
                            id: 1,
                            orderNo: 'ORD-2024-001',
                            customer: '张三',
                            fileCount: 3,
                            priority: '高',
                            status: 'pending',
                            createdAt: '2024-07-31 09:30'
                        },
                        {
                            id: 4,
                            orderNo: 'ORD-2024-004',
                            customer: '赵六',
                            fileCount: 5,
                            priority: '中',
                            status: 'pending',
                            createdAt: '2024-07-31 08:30'
                        },
                        {
                            id: 6,
                            orderNo: 'ORD-2024-006',
                            customer: '孙八',
                            fileCount: 2,
                            priority: '低',
                            status: 'pending',
                            createdAt: '2024-07-31 07:45'
                        }
                    ],

                    // 待发货订单
                    readyToShipOrders: [
                        {
                            id: 3,
                            orderNo: 'ORD-2024-003',
                            customer: '王五',
                            address: '北京市朝阳区xxx街道xxx号',
                            status: 'completed',
                            completedAt: '2024-07-31 08:45'
                        }
                    ],

                    // 打印机数据
                    printers: [
                        {
                            id: 1,
                            name: '打印机-01',
                            model: 'HP LaserJet Pro',
                            status: 'online',
                            currentJob: {
                                orderNo: 'ORD-2024-002',
                                progress: 75
                            }
                        },
                        {
                            id: 2,
                            name: '打印机-02',
                            model: 'Canon PIXMA',
                            status: 'online',
                            currentJob: null
                        },
                        {
                            id: 3,
                            name: '打印机-03',
                            model: 'Epson EcoTank',
                            status: 'busy',
                            currentJob: {
                                orderNo: 'ORD-2024-007',
                                progress: 45
                            }
                        },
                        {
                            id: 4,
                            name: '打印机-04',
                            model: 'Brother HL-L2350DW',
                            status: 'offline',
                            currentJob: null
                        }
                    ],

                    // 打印队列
                    printQueue: [
                        {
                            id: 1,
                            orderNo: 'ORD-2024-002',
                            printerName: '打印机-01',
                            fileName: 'document1.pdf',
                            status: '打印中',
                            progress: 75,
                            estimatedTime: '2分钟',
                            createdAt: '2024-07-31 09:15'
                        },
                        {
                            id: 2,
                            orderNo: 'ORD-2024-007',
                            printerName: '打印机-03',
                            fileName: 'report.pdf',
                            status: '打印中',
                            progress: 45,
                            estimatedTime: '5分钟',
                            createdAt: '2024-07-31 09:20'
                        },
                        {
                            id: 3,
                            orderNo: 'ORD-2024-001',
                            printerName: '等待分配',
                            fileName: 'invoice.pdf',
                            status: '队列中',
                            progress: 0,
                            estimatedTime: '等待中',
                            createdAt: '2024-07-31 09:30'
                        }
                    ],

                    // 物流公司
                    logisticsCompanies: [
                        '顺丰速运',
                        '圆通速递',
                        '中通快递',
                        '申通快递',
                        '韵达速递',
                        '百世快递'
                    ],

                    // 通知数据
                    notifications: [
                        {
                            id: 1,
                            type: 'success',
                            icon: 'mdi-check-circle',
                            title: '订单完成',
                            message: '订单 ORD-2024-003 已完成打印',
                            time: '5分钟前'
                        },
                        {
                            id: 2,
                            type: 'warning',
                            icon: 'mdi-alert',
                            title: '打印机离线',
                            message: '打印机-04 已离线，请检查连接',
                            time: '10分钟前'
                        },
                        {
                            id: 3,
                            type: 'info',
                            icon: 'mdi-information',
                            title: '新订单',
                            message: '收到新订单 ORD-2024-008',
                            time: '15分钟前'
                        }
                    ]
                };
            },

            computed: {
                pageTitle() {
                    const titles = {
                        'dashboard': '仪表盘',
                        'pending-orders': '待处理订单',
                        'printing-orders': '打印中订单',
                        'completed-orders': '已完成订单',
                        'print-queue': '打印队列',
                        'printers': '打印机管理',
                        'logistics': '物流发货',
                        'files': '文件管理',
                        'settings': '系统设置',
                        'sync': '同步状态'
                    };
                    return titles[this.currentPage] || '云打印终端';
                },

                breadcrumbs() {
                    const crumbs = [
                        { title: '首页', disabled: false, href: '#' }
                    ];

                    if (this.currentPage !== 'dashboard') {
                        crumbs.push({
                            title: this.pageTitle,
                            disabled: true
                        });
                    }

                    return crumbs;
                }
            },

            methods: {
                // 状态相关方法
                getStatusClass(status) {
                    const classes = {
                        'pending': 'status-indicator status-pending',
                        'printing': 'status-indicator status-processing',
                        'completed': 'status-indicator status-completed',
                        'failed': 'status-indicator status-failed',
                        'shipped': 'status-indicator status-shipped'
                    };
                    return classes[status] || 'status-indicator';
                },

                getStatusText(status) {
                    const texts = {
                        'pending': '待处理',
                        'printing': '打印中',
                        'completed': '已完成',
                        'failed': '失败',
                        'shipped': '已发货'
                    };
                    return texts[status] || status;
                },

                getPriorityColor(priority) {
                    const colors = {
                        '高': 'error',
                        '中': 'warning',
                        '低': 'success'
                    };
                    return colors[priority] || 'primary';
                },

                getPrinterStatusClass(status) {
                    const classes = {
                        'online': 'printer-online',
                        'offline': 'printer-offline',
                        'busy': 'printer-busy'
                    };
                    return classes[status] || 'printer-offline';
                },

                getPrinterStatusColor(status) {
                    const colors = {
                        'online': 'success',
                        'offline': 'error',
                        'busy': 'warning'
                    };
                    return colors[status] || 'error';
                },

                getPrinterStatusText(status) {
                    const texts = {
                        'online': '在线',
                        'offline': '离线',
                        'busy': '忙碌'
                    };
                    return texts[status] || status;
                },

                getJobStatusColor(status) {
                    const colors = {
                        '队列中': 'info',
                        '打印中': 'warning',
                        '已完成': 'success',
                        '失败': 'error'
                    };
                    return colors[status] || 'primary';
                },

                // 操作方法
                viewOrder(item) {
                    console.log('查看订单:', item);
                    // 这里可以打开订单详情对话框
                },

                startPrint(item) {
                    console.log('开始打印:', item);
                    // 这里可以启动打印流程
                    this.addNotification('info', '打印任务已添加到队列', `订单 ${item.orderNo} 已添加到打印队列`);
                },

                syncOrders() {
                    console.log('同步订单');
                    this.addNotification('success', '同步完成', '订单数据已同步');
                },

                refreshOrders() {
                    console.log('刷新订单');
                    this.addNotification('info', '刷新完成', '订单列表已刷新');
                },

                createShipment(item) {
                    console.log('创建发货:', item);
                    this.addNotification('success', '发货创建成功', `订单 ${item.orderNo} 发货单已创建`);
                },

                scanBarcode() {
                    if (this.barcodeInput.trim()) {
                        console.log('扫描条码:', this.barcodeInput);
                        this.addNotification('info', '条码扫描', `已扫描条码: ${this.barcodeInput}`);
                        this.barcodeInput = '';
                    }
                },

                quickShip() {
                    if (this.selectedLogistics) {
                        console.log('快速发货:', this.selectedLogistics);
                        this.addNotification('success', '快速发货', `已通过 ${this.selectedLogistics} 发货`);
                    }
                },

                addNotification(type, title, message) {
                    const notification = {
                        id: Date.now(),
                        type: type,
                        icon: type === 'success' ? 'mdi-check-circle' :
                              type === 'warning' ? 'mdi-alert' :
                              type === 'error' ? 'mdi-alert-circle' : 'mdi-information',
                        title: title,
                        message: message,
                        time: '刚刚'
                    };
                    this.notifications.unshift(notification);

                    // 5秒后自动移除通知
                    setTimeout(() => {
                        const index = this.notifications.findIndex(n => n.id === notification.id);
                        if (index > -1) {
                            this.notifications.splice(index, 1);
                        }
                    }, 5000);
                }
            },

            mounted() {
                // 模拟实时数据更新
                setInterval(() => {
                    // 更新打印进度
                    this.printQueue.forEach(job => {
                        if (job.status === '打印中' && job.progress < 100) {
                            job.progress = Math.min(100, job.progress + Math.random() * 5);
                            if (job.progress >= 100) {
                                job.status = '已完成';
                                job.estimatedTime = '已完成';
                            }
                        }
                    });

                    // 更新打印机状态
                    this.printers.forEach(printer => {
                        if (printer.currentJob && printer.currentJob.progress < 100) {
                            printer.currentJob.progress = Math.min(100, printer.currentJob.progress + Math.random() * 3);
                            if (printer.currentJob.progress >= 100) {
                                printer.currentJob = null;
                                printer.status = 'online';
                            }
                        }
                    });
                }, 2000);
            }
        }).use(vuetify).mount('#app');
    </script>
</body>
</html>
