import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export interface SyncStatus {
  isActive: boolean;
  lastSyncTime: Date | null;
  nextSyncTime: Date | null;
  status: 'idle' | 'syncing' | 'error' | 'success';
  message: string;
  progress: number;
}

export interface SyncStatistics {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  lastError: string | null;
  averageSyncTime: number;
}

export const useSyncStore = defineStore('sync', () => {
  // 状态
  const syncStatus = ref<SyncStatus>({
    isActive: false,
    lastSyncTime: null,
    nextSyncTime: null,
    status: 'idle',
    message: '等待同步',
    progress: 0
  });
  
  const syncStatistics = ref<SyncStatistics>({
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    lastError: null,
    averageSyncTime: 0
  });
  
  const isPeriodicSyncEnabled = ref(false);
  const syncInterval = ref(300000); // 5分钟
  const syncTimer = ref<NodeJS.Timeout | null>(null);
  
  // 计算属性
  const isSyncing = computed(() => syncStatus.value.status === 'syncing');
  const hasError = computed(() => syncStatus.value.status === 'error');
  const successRate = computed(() => {
    const total = syncStatistics.value.totalSyncs;
    if (total === 0) return 0;
    return (syncStatistics.value.successfulSyncs / total) * 100;
  });
  
  const statusColor = computed(() => {
    switch (syncStatus.value.status) {
      case 'syncing':
        return 'info';
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'grey';
    }
  });
  
  const statusIcon = computed(() => {
    switch (syncStatus.value.status) {
      case 'syncing':
        return 'mdi-sync';
      case 'success':
        return 'mdi-check-circle';
      case 'error':
        return 'mdi-alert-circle';
      default:
        return 'mdi-sync-off';
    }
  });
  
  // 操作方法
  const initialize = async () => {
    try {
      console.log('初始化同步服务...');
      
      // TODO: 在任务7中实现实际的同步服务初始化
      // 这里先设置一个模拟状态
      syncStatus.value = {
        isActive: true,
        lastSyncTime: new Date(),
        nextSyncTime: new Date(Date.now() + syncInterval.value),
        status: 'success',
        message: '同步完成',
        progress: 100
      };
      
      console.log('同步服务初始化完成');
    } catch (error) {
      console.error('同步服务初始化失败:', error);
      syncStatus.value.status = 'error';
      syncStatus.value.message = '初始化失败';
    }
  };
  
  const startPeriodicSync = async () => {
    try {
      if (syncTimer.value) {
        clearInterval(syncTimer.value);
      }
      
      isPeriodicSyncEnabled.value = true;
      
      // TODO: 在任务7中实现实际的定期同步
      syncTimer.value = setInterval(() => {
        triggerSync();
      }, syncInterval.value);
      
      console.log('定期同步已启动');
    } catch (error) {
      console.error('启动定期同步失败:', error);
    }
  };
  
  const stopPeriodicSync = async () => {
    try {
      if (syncTimer.value) {
        clearInterval(syncTimer.value);
        syncTimer.value = null;
      }
      
      isPeriodicSyncEnabled.value = false;
      
      console.log('定期同步已停止');
    } catch (error) {
      console.error('停止定期同步失败:', error);
    }
  };
  
  const triggerSync = async () => {
    if (isSyncing.value) {
      console.log('同步正在进行中，跳过此次触发');
      return;
    }
    
    try {
      syncStatus.value.status = 'syncing';
      syncStatus.value.message = '正在同步...';
      syncStatus.value.progress = 0;
      
      const startTime = Date.now();
      
      // TODO: 在任务7中实现实际的同步逻辑
      // 这里使用模拟的同步过程
      await simulateSync();
      
      const endTime = Date.now();
      const syncTime = endTime - startTime;
      
      // 更新统计信息
      syncStatistics.value.totalSyncs++;
      syncStatistics.value.successfulSyncs++;
      syncStatistics.value.averageSyncTime = 
        (syncStatistics.value.averageSyncTime * (syncStatistics.value.totalSyncs - 1) + syncTime) / 
        syncStatistics.value.totalSyncs;
      
      // 更新同步状态
      syncStatus.value.status = 'success';
      syncStatus.value.message = '同步完成';
      syncStatus.value.progress = 100;
      syncStatus.value.lastSyncTime = new Date();
      syncStatus.value.nextSyncTime = new Date(Date.now() + syncInterval.value);
      
      console.log('同步完成');
    } catch (error) {
      console.error('同步失败:', error);
      
      syncStatistics.value.totalSyncs++;
      syncStatistics.value.failedSyncs++;
      syncStatistics.value.lastError = error instanceof Error ? error.message : '未知错误';
      
      syncStatus.value.status = 'error';
      syncStatus.value.message = '同步失败';
      syncStatus.value.progress = 0;
    }
  };
  
  const simulateSync = async () => {
    // 模拟同步过程
    const steps = [
      { message: '检查网络连接...', progress: 10 },
      { message: '获取云端数据...', progress: 30 },
      { message: '比较本地数据...', progress: 50 },
      { message: '上传更新...', progress: 70 },
      { message: '下载新数据...', progress: 90 },
      { message: '完成同步...', progress: 100 }
    ];
    
    for (const step of steps) {
      syncStatus.value.message = step.message;
      syncStatus.value.progress = step.progress;
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  };
  
  const forceSync = async () => {
    console.log('强制同步');
    await triggerSync();
  };
  
  const getSyncHistory = async (limit = 10) => {
    // TODO: 在任务7中实现获取同步历史
    return [];
  };
  
  const updateSyncInterval = (interval: number) => {
    syncInterval.value = interval;
    
    if (isPeriodicSyncEnabled.value) {
      stopPeriodicSync();
      startPeriodicSync();
    }
  };
  
  const clearSyncStatistics = () => {
    syncStatistics.value = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastError: null,
      averageSyncTime: 0
    };
  };
  
  return {
    // 状态
    syncStatus,
    syncStatistics,
    isPeriodicSyncEnabled,
    syncInterval,
    
    // 计算属性
    isSyncing,
    hasError,
    successRate,
    statusColor,
    statusIcon,
    
    // 方法
    initialize,
    startPeriodicSync,
    stopPeriodicSync,
    triggerSync,
    forceSync,
    getSyncHistory,
    updateSyncInterval,
    clearSyncStatistics
  };
});
