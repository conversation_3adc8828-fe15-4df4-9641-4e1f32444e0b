// 导航组件样式

.v-navigation-drawer {
  border-right: 1px solid var(--border-light) !important;
  
  .v-list {
    .v-list-item {
      border-radius: var(--border-radius-md) !important;
      margin: var(--spacing-1) var(--spacing-3) !important;
      
      &.v-list-item--active {
        background: rgba(var(--v-theme-primary), 0.1) !important;
        color: var(--primary-500) !important;
      }
      
      &:hover {
        background: rgba(var(--v-theme-primary), 0.05) !important;
      }
    }
  }
}

.v-app-bar {
  border-bottom: 1px solid var(--border-light) !important;
}

.v-breadcrumbs {
  .v-breadcrumbs-item {
    color: var(--text-secondary) !important;
    
    &:last-child {
      color: var(--text-primary) !important;
    }
  }
  
  .v-breadcrumbs-divider {
    color: var(--text-tertiary) !important;
  }
}
