// 表格组件样式

.v-data-table {
  border-radius: var(--border-radius-card) !important;
  border: 1px solid var(--border-light) !important;
  
  .v-data-table__wrapper {
    border-radius: var(--border-radius-card) !important;
  }
  
  .v-data-table-header {
    background: var(--bg-light) !important;
    
    th {
      font-weight: var(--font-weight-semibold) !important;
      color: var(--text-primary) !important;
      border-bottom: 1px solid var(--border-light) !important;
    }
  }
  
  .v-data-table__tr {
    &:hover {
      background: var(--bg-hover) !important;
    }
    
    td {
      border-bottom: 1px solid var(--border-light) !important;
    }
  }
}

.v-table {
  border-radius: var(--border-radius-card) !important;
  
  .v-table__wrapper {
    border-radius: var(--border-radius-card) !important;
  }
}
