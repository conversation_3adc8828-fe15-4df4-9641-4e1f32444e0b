<template>
  <div class="order-detail">
    <div class="page-header">
      <h1 class="page-title">订单详情</h1>
      <p class="page-subtitle">查看订单详细信息</p>
    </div>

    <v-card class="content-card" elevation="0">
      <v-card-title>
        订单详情
      </v-card-title>
      <v-card-text>
        <div class="text-center py-8">
          <v-icon icon="mdi-file-document" size="64" color="grey-lighten-1" />
          <div class="text-h6 mt-4 mb-2">订单详情功能</div>
          <div class="text-body-2 text-grey">将在任务4中实现</div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
// TODO: 在任务4中实现订单详情功能
</script>
