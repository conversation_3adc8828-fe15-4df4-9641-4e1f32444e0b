<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">仪表板</h1>
      <p class="page-subtitle">云打印终端系统概览</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <v-card
        v-for="stat in statistics"
        :key="stat.title"
        class="stats-card"
        :class="`stats-card-${stat.type}`"
        elevation="0"
      >
        <v-card-text class="stats-content">
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="stats-value">{{ stat.value }}</div>
              <div class="stats-label">{{ stat.title }}</div>
              <div class="stats-trend">
                <v-icon :icon="stat.trend.icon" size="small" class="trend-icon" />
                {{ stat.trend.text }}
              </div>
            </div>
            <v-icon :icon="stat.icon" size="48" class="stats-icon" />
          </div>
        </v-card-text>
      </v-card>
    </div>

    <!-- 快速操作 -->
    <v-row class="mb-6">
      <v-col cols="12" md="6">
        <v-card class="content-card" elevation="0">
          <v-card-title class="card-title">
            <v-icon icon="mdi-lightning-bolt" class="mr-2" />
            快速操作
          </v-card-title>
          <v-card-text>
            <div class="quick-actions">
              <v-btn
                v-for="action in quickActions"
                :key="action.title"
                :prepend-icon="action.icon"
                :color="action.color"
                variant="tonal"
                size="large"
                class="action-btn"
                @click="handleQuickAction(action.action)"
              >
                {{ action.title }}
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card class="content-card" elevation="0">
          <v-card-title class="card-title">
            <v-icon icon="mdi-information" class="mr-2" />
            系统状态
          </v-card-title>
          <v-card-text>
            <div class="system-status">
              <div
                v-for="status in systemStatus"
                :key="status.name"
                class="status-item"
              >
                <div class="status-info">
                  <v-icon :icon="status.icon" :color="status.color" class="mr-2" />
                  <span class="status-name">{{ status.name }}</span>
                </div>
                <v-chip
                  :color="status.color"
                  size="small"
                  variant="tonal"
                >
                  {{ status.status }}
                </v-chip>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 最近活动 -->
    <v-card class="content-card" elevation="0">
      <v-card-title class="card-title">
        <v-icon icon="mdi-history" class="mr-2" />
        最近活动
      </v-card-title>
      <v-card-text>
        <div class="recent-activities">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
          >
            <v-avatar :color="activity.color" size="32" class="mr-3">
              <v-icon :icon="activity.icon" color="white" size="16" />
            </v-avatar>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ formatTime(activity.time) }}</div>
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useSyncStore } from '@/stores/sync';

const router = useRouter();
const syncStore = useSyncStore();

// 统计数据
const statistics = ref([
  {
    title: '今日订单',
    value: '128',
    icon: 'mdi-file-document',
    type: 'primary',
    trend: { icon: 'mdi-trending-up', text: '+12%' }
  },
  {
    title: '打印队列',
    value: '23',
    icon: 'mdi-printer',
    type: 'success',
    trend: { icon: 'mdi-trending-down', text: '-5%' }
  },
  {
    title: '已发货',
    value: '95',
    icon: 'mdi-truck-delivery',
    type: 'warning',
    trend: { icon: 'mdi-trending-up', text: '+8%' }
  },
  {
    title: '同步状态',
    value: '正常',
    icon: 'mdi-sync',
    type: 'success',
    trend: { icon: 'mdi-check', text: '已同步' }
  }
]);

// 快速操作
const quickActions = ref([
  {
    title: '扫描发货',
    icon: 'mdi-qrcode-scan',
    color: 'primary',
    action: 'scan'
  },
  {
    title: '手动同步',
    icon: 'mdi-sync',
    color: 'secondary',
    action: 'sync'
  },
  {
    title: '打印机设置',
    icon: 'mdi-printer-settings',
    color: 'success',
    action: 'printers'
  },
  {
    title: '查看统计',
    icon: 'mdi-chart-line',
    color: 'info',
    action: 'statistics'
  }
]);

// 系统状态
const systemStatus = ref([
  {
    name: '网络连接',
    status: '正常',
    icon: 'mdi-wifi',
    color: 'success'
  },
  {
    name: '数据库',
    status: '正常',
    icon: 'mdi-database',
    color: 'success'
  },
  {
    name: '打印服务',
    status: '正常',
    icon: 'mdi-printer',
    color: 'success'
  },
  {
    name: '同步服务',
    status: '正常',
    icon: 'mdi-sync',
    color: 'success'
  }
]);

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '订单 #12345 已打印完成',
    time: new Date(Date.now() - 5 * 60 * 1000),
    icon: 'mdi-printer',
    color: 'success'
  },
  {
    id: 2,
    title: '新订单 #12346 已接收',
    time: new Date(Date.now() - 10 * 60 * 1000),
    icon: 'mdi-file-document-plus',
    color: 'primary'
  },
  {
    id: 3,
    title: '数据同步完成',
    time: new Date(Date.now() - 15 * 60 * 1000),
    icon: 'mdi-sync',
    color: 'info'
  },
  {
    id: 4,
    title: '订单 #12344 已发货',
    time: new Date(Date.now() - 30 * 60 * 1000),
    icon: 'mdi-truck-delivery',
    color: 'warning'
  }
]);

// 方法
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'scan':
      router.push('/logistics/scan');
      break;
    case 'sync':
      syncStore.triggerSync();
      break;
    case 'printers':
      router.push('/printers');
      break;
    case 'statistics':
      router.push('/statistics');
      break;
  }
};

const formatTime = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}小时前`;
  
  const days = Math.floor(hours / 24);
  return `${days}天前`;
};

// 生命周期
onMounted(() => {
  console.log('仪表板页面已加载');
});
</script>

<style lang="scss" scoped>
.dashboard {
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    
    .action-btn {
      height: 56px;
      justify-content: flex-start;
    }
  }
  
  .system-status {
    .status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-3) 0;
      border-bottom: 1px solid var(--border-light);
      
      &:last-child {
        border-bottom: none;
      }
      
      .status-info {
        display: flex;
        align-items: center;
        
        .status-name {
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }
  
  .recent-activities {
    .activity-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-3) 0;
      border-bottom: 1px solid var(--border-light);
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
          margin-bottom: var(--spacing-1);
        }
        
        .activity-time {
          font-size: var(--font-size-caption);
          color: var(--text-secondary);
        }
      }
    }
  }
}
</style>
