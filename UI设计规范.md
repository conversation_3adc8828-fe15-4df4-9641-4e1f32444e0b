# 云打印终端 UI 设计规范
## 基于 Vuetify + Materio 模板

---

## 📋 目录

1. [设计原则](#设计原则)
2. [色彩系统](#色彩系统)
3. [字体规范](#字体规范)
4. [间距系统](#间距系统)
5. [阴影系统](#阴影系统)
6. [圆角规范](#圆角规范)
7. [组件规范](#组件规范)
8. [布局系统](#布局系统)
9. [动画规范](#动画规范)
10. [响应式设计](#响应式设计)
11. [状态系统](#状态系统)
12. [图标规范](#图标规范)

---

## 🎨 设计原则

### Material Design 3.0
- 遵循Google Material Design 3.0设计语言
- 注重用户体验和可访问性
- 保持视觉一致性和功能性

### Vuetify框架优势
- 组件化开发，提高开发效率
- 内置响应式设计
- 丰富的主题定制能力
- 完善的无障碍支持

### Materio模板特色
- 现代化的管理界面设计
- 优雅的配色方案
- 流畅的交互动画
- 专业的数据展示

---

## 🎨 色彩系统

### 主色调 (Primary Colors)
```css
:root {
  /* 主色调 - Materio蓝色系 */
  --primary-50: #f0f4ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #5d87ff;  /* 主色 */
  --primary-600: #4570ea;
  --primary-700: #3b5998;
  --primary-800: #1e3a8a;
  --primary-900: #1e1b4b;

  /* 主色调变体 */
  --primary-light: #ecf2ff;
  --primary-dark: #4570ea;
}
```

### 辅助色调 (Secondary Colors)
```css
:root {
  /* 辅助色 - 青色系 */
  --secondary-50: #ecfeff;
  --secondary-100: #cffafe;
  --secondary-200: #a5f3fc;
  --secondary-300: #67e8f9;
  --secondary-400: #22d3ee;
  --secondary-500: #49beff;  /* 辅助色 */
  --secondary-600: #0891b2;
  --secondary-700: #0e7490;
  --secondary-800: #155e75;
  --secondary-900: #164e63;
}
```

### 功能色调 (Semantic Colors)
```css
:root {
  /* 成功色 - 绿色系 */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-200: #a7f3d0;
  --success-300: #6ee7b7;
  --success-400: #34d399;
  --success-500: #13deb9;  /* 成功色 */
  --success-600: #059669;
  --success-700: #047857;
  --success-800: #065f46;
  --success-900: #064e3b;

  /* 警告色 - 橙色系 */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #ffae1f;  /* 警告色 */
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* 错误色 - 红色系 */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #fa896b;  /* 错误色 */
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* 信息色 - 蓝色系 */
  --info-50: #eff6ff;
  --info-100: #dbeafe;
  --info-200: #bfdbfe;
  --info-300: #93c5fd;
  --info-400: #60a5fa;
  --info-500: #539bff;  /* 信息色 */
  --info-600: #2563eb;
  --info-700: #1d4ed8;
  --info-800: #1e40af;
  --info-900: #1e3a8a;
}
```

### 中性色调 (Neutral Colors)
```css
:root {
  /* 文字颜色 */
  --text-primary: #2a3547;      /* 主要文字 */
  --text-regular: #5a6a85;      /* 常规文字 */
  --text-secondary: #7c8fac;    /* 次要文字 */
  --text-placeholder: #adb5bd;  /* 占位符文字 */
  --text-disabled: #d1d5db;     /* 禁用文字 */

  /* 背景颜色 */
  --bg-color: #ffffff;          /* 主背景 */
  --bg-page: #f5f5f9;          /* 页面背景 */
  --bg-light: #f9f9fd;         /* 浅色背景 */
  --bg-overlay: rgba(0, 0, 0, 0.8); /* 遮罩背景 */

  /* 边框颜色 */
  --border-base: #e5eaef;       /* 基础边框 */
  --border-light: #f1f5f9;      /* 浅色边框 */
  --border-lighter: #f8fafc;    /* 更浅边框 */
  --border-dark: #d1d5db;       /* 深色边框 */
}
```

---

## 📝 字体规范

### 字体族 (Font Family)
```css
:root {
  /* 主字体 */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* 等宽字体 */
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}
```

### 字体大小 (Font Sizes)
```css
:root {
  /* 标题字体 */
  --font-size-h1: 2.5rem;    /* 40px */
  --font-size-h2: 2rem;      /* 32px */
  --font-size-h3: 1.75rem;   /* 28px */
  --font-size-h4: 1.5rem;    /* 24px */
  --font-size-h5: 1.25rem;   /* 20px */
  --font-size-h6: 1.125rem;  /* 18px */

  /* 正文字体 */
  --font-size-body-1: 1rem;      /* 16px */
  --font-size-body-2: 0.875rem;  /* 14px */
  --font-size-caption: 0.75rem;  /* 12px */
  --font-size-overline: 0.625rem; /* 10px */
}
```

### 字体权重 (Font Weights)
```css
:root {
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}
```

### 行高 (Line Heights)
```css
:root {
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}
```

---

## 📏 间距系统

### 基础间距 (Base Spacing)
```css
:root {
  /* 基础间距单位 - 4px */
  --spacing-0: 0;
  --spacing-1: 0.25rem;  /* 4px */
  --spacing-2: 0.5rem;   /* 8px */
  --spacing-3: 0.75rem;  /* 12px */
  --spacing-4: 1rem;     /* 16px */
  --spacing-5: 1.25rem;  /* 20px */
  --spacing-6: 1.5rem;   /* 24px */
  --spacing-8: 2rem;     /* 32px */
  --spacing-10: 2.5rem;  /* 40px */
  --spacing-12: 3rem;    /* 48px */
  --spacing-16: 4rem;    /* 64px */
  --spacing-20: 5rem;    /* 80px */
  --spacing-24: 6rem;    /* 96px */
}
```

### 组件间距 (Component Spacing)
```css
:root {
  /* 内边距 */
  --padding-xs: var(--spacing-2);   /* 8px */
  --padding-sm: var(--spacing-3);   /* 12px */
  --padding-md: var(--spacing-4);   /* 16px */
  --padding-lg: var(--spacing-6);   /* 24px */
  --padding-xl: var(--spacing-8);   /* 32px */

  /* 外边距 */
  --margin-xs: var(--spacing-2);    /* 8px */
  --margin-sm: var(--spacing-3);    /* 12px */
  --margin-md: var(--spacing-4);    /* 16px */
  --margin-lg: var(--spacing-6);    /* 24px */
  --margin-xl: var(--spacing-8);    /* 32px */

  /* 栅格间距 */
  --grid-gutter: var(--spacing-6);  /* 24px */
}
```

---

## 🌟 阴影系统

### 基础阴影 (Base Shadows)
```css
:root {
  /* 无阴影 */
  --shadow-none: none;

  /* 基础阴影 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-base: 0 1px 4px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* 特殊阴影 */
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-outline: 0 0 0 3px rgba(93, 135, 255, 0.1);
}
```

### 组件阴影 (Component Shadows)
```css
:root {
  /* 卡片阴影 */
  --shadow-card: 0 0 2px rgba(145, 158, 171, 0.2), 0 12px 24px -4px rgba(145, 158, 171, 0.12);
  --shadow-card-hover: 0 0 2px rgba(145, 158, 171, 0.2), 0 16px 32px -4px rgba(145, 158, 171, 0.16);

  /* 按钮阴影 */
  --shadow-button: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-button-hover: 0 4px 8px rgba(0, 0, 0, 0.15);

  /* 导航阴影 */
  --shadow-nav: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 模态框阴影 */
  --shadow-modal: 0 20px 40px rgba(0, 0, 0, 0.15);

  /* 下拉菜单阴影 */
  --shadow-dropdown: 0 4px 16px rgba(0, 0, 0, 0.12);
}
```

---

## 🔘 圆角规范

### 基础圆角 (Base Border Radius)
```css
:root {
  /* 基础圆角 */
  --border-radius-none: 0;
  --border-radius-xs: 2px;
  --border-radius-sm: 4px;
  --border-radius-base: 7px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-2xl: 20px;
  --border-radius-3xl: 24px;
  --border-radius-full: 50%;
}
```

### 组件圆角 (Component Border Radius)
```css
:root {
  /* 按钮圆角 */
  --border-radius-button: var(--border-radius-sm);

  /* 输入框圆角 */
  --border-radius-input: var(--border-radius-base);

  /* 卡片圆角 */
  --border-radius-card: var(--border-radius-lg);

  /* 模态框圆角 */
  --border-radius-modal: var(--border-radius-xl);

  /* 头像圆角 */
  --border-radius-avatar: var(--border-radius-full);

  /* 标签圆角 */
  --border-radius-chip: 20px;
}
```

---

## 🧩 组件规范

### 按钮组件 (Button Component)

#### 基础样式
```css
.v-btn {
  border-radius: var(--border-radius-button) !important;
  text-transform: none !important;
  font-weight: var(--font-weight-medium) !important;
  font-family: var(--font-family-primary) !important;
  letter-spacing: 0.02em;
  transition: all 0.2s ease;
}
```

#### 按钮尺寸
```css
/* 小按钮 */
.v-btn--size-small {
  height: 32px !important;
  min-width: 64px !important;
  padding: 0 12px !important;
  font-size: 0.875rem !important;
}

/* 默认按钮 */
.v-btn--size-default {
  height: 40px !important;
  min-width: 80px !important;
  padding: 0 16px !important;
  font-size: 0.875rem !important;
}

/* 大按钮 */
.v-btn--size-large {
  height: 48px !important;
  min-width: 96px !important;
  padding: 0 24px !important;
  font-size: 1rem !important;
}
```

#### 按钮变体
```css
/* 主要按钮 */
.v-btn--variant-elevated {
  box-shadow: var(--shadow-button) !important;
}

.v-btn--variant-elevated:hover {
  box-shadow: var(--shadow-button-hover) !important;
  transform: translateY(-1px);
}

/* 文本按钮 */
.v-btn--variant-text {
  background: transparent !important;
}

.v-btn--variant-text:hover {
  background: rgba(93, 135, 255, 0.08) !important;
}

/* 轮廓按钮 */
.v-btn--variant-outlined {
  border: 1px solid var(--border-base) !important;
  background: transparent !important;
}

.v-btn--variant-outlined:hover {
  background: rgba(93, 135, 255, 0.04) !important;
  border-color: var(--primary-500) !important;
}
```

### 卡片组件 (Card Component)

#### 基础样式
```css
.v-card {
  box-shadow: var(--shadow-card) !important;
  border-radius: var(--border-radius-card) !important;
  border: none !important;
  background: var(--bg-color) !important;
  transition: all 0.3s ease;
}

.v-card:hover {
  box-shadow: var(--shadow-card-hover) !important;
  transform: translateY(-2px);
}
```

#### 卡片变体
```css
/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%) !important;
  color: white !important;
  border-radius: var(--border-radius-xl) !important;
}

.stats-card-success {
  background: linear-gradient(135deg, var(--success-500) 0%, #00c9a7 100%) !important;
}

.stats-card-warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, #ff9f43 100%) !important;
}

.stats-card-danger {
  background: linear-gradient(135deg, var(--error-500) 0%, #ff6b6b 100%) !important;
}

/* 轮廓卡片 */
.v-card--variant-outlined {
  border: 1px solid var(--border-light) !important;
  box-shadow: none !important;
}

/* 平面卡片 */
.v-card--variant-flat {
  box-shadow: none !important;
  background: var(--bg-light) !important;
}
```

### 导航组件 (Navigation Component)

#### 侧边导航
```css
.v-navigation-drawer {
  border-right: 1px solid var(--border-light) !important;
  background: var(--bg-color) !important;
}

.v-navigation-drawer .v-list-item {
  border-radius: var(--border-radius-sm) !important;
  margin: 2px 8px !important;
  transition: all 0.2s ease;
}

.v-navigation-drawer .v-list-item:hover {
  background: var(--border-lighter) !important;
  transform: translateX(2px);
}

.v-navigation-drawer .v-list-item--active {
  background: var(--primary-light) !important;
  color: var(--primary-500) !important;
  font-weight: var(--font-weight-medium) !important;
}

.v-navigation-drawer .v-list-item--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary-500);
  border-radius: 0 2px 2px 0;
}
```

#### 顶部导航
```css
.v-app-bar {
  border-bottom: 1px solid var(--border-light) !important;
  backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.v-breadcrumbs {
  padding: 0 !important;
}

.v-breadcrumbs .v-breadcrumbs-item {
  color: var(--text-secondary) !important;
  font-size: 0.875rem !important;
}

.v-breadcrumbs .v-breadcrumbs-item--disabled {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-medium) !important;
}
```

### 表单组件 (Form Component)

#### 输入框
```css
.v-text-field {
  font-family: var(--font-family-primary) !important;
}

.v-text-field .v-field {
  border-radius: var(--border-radius-input) !important;
  background: var(--bg-color) !important;
}

.v-text-field--variant-outlined .v-field {
  border: 1px solid var(--border-base) !important;
}

.v-text-field--variant-outlined .v-field:hover {
  border-color: var(--primary-300) !important;
}

.v-text-field--variant-outlined .v-field--focused {
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 2px rgba(93, 135, 255, 0.1) !important;
}

.v-text-field--variant-filled .v-field {
  background: var(--bg-light) !important;
}
```

#### 选择器
```css
.v-select .v-field {
  border-radius: var(--border-radius-input) !important;
}

.v-select .v-list {
  border-radius: var(--border-radius-base) !important;
  box-shadow: var(--shadow-dropdown) !important;
  border: 1px solid var(--border-light) !important;
}

.v-select .v-list-item {
  transition: all 0.2s ease;
}

.v-select .v-list-item:hover {
  background: var(--primary-light) !important;
}
```

### 数据表格组件 (Data Table Component)

```css
.v-data-table {
  border-radius: var(--border-radius-base) !important;
  overflow: hidden;
}

.v-data-table .v-data-table__thead {
  background: var(--bg-light) !important;
}

.v-data-table .v-data-table__thead th {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-size: 0.875rem !important;
  border-bottom: 1px solid var(--border-light) !important;
}

.v-data-table .v-data-table__tbody tr {
  transition: all 0.2s ease;
}

.v-data-table .v-data-table__tbody tr:hover {
  background: var(--bg-light) !important;
}

.v-data-table .v-data-table__tbody td {
  border-bottom: 1px solid var(--border-lighter) !important;
  font-size: 0.875rem !important;
}
```

---

## 🎯 状态系统

### 状态指示器 (Status Indicators)

#### 基础状态样式
```css
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
```

#### 订单状态
```css
.status-pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-processing {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.status-completed {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-failed {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-shipped {
  background: #e2e3f1;
  color: #383874;
  border: 1px solid #d1d4e0;
}
```

#### 打印机状态
```css
.printer-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.printer-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.printer-online {
  background: var(--success-500);
  box-shadow: 0 0 0 2px rgba(19, 222, 185, 0.2);
}

.printer-offline {
  background: var(--text-placeholder);
}

.printer-busy {
  background: var(--warning-500);
  box-shadow: 0 0 0 2px rgba(255, 174, 31, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```

#### 优先级标签
```css
.priority-high {
  background: var(--error-50);
  color: var(--error-700);
  border: 1px solid var(--error-200);
}

.priority-medium {
  background: var(--warning-50);
  color: var(--warning-700);
  border: 1px solid var(--warning-200);
}

.priority-low {
  background: var(--success-50);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}
```

### 进度指示器 (Progress Indicators)

```css
.progress-container {
  background: var(--border-lighter);
  border-radius: 10px;
  overflow: hidden;
  height: 8px;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  border-radius: 10px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
```

---

## 🎬 动画规范

### 基础动画 (Base Animations)

#### 缓动函数
```css
:root {
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-sharp: cubic-bezier(0.4, 0, 0.6, 1);
}
```

#### 动画时长
```css
:root {
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
}
```

#### 页面过渡动画
```css
.fade-in {
  animation: fadeIn 0.5s var(--ease-out);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.3s var(--ease-out);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s var(--ease-out);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
```

#### 交互动画
```css
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--duration-fast) var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.click-scale {
  transition: transform var(--duration-fast) var(--ease-sharp);
}

.click-scale:active {
  transform: scale(0.95);
}
```

---

## 📐 布局系统

### 栅格系统 (Grid System)

#### Vuetify栅格配置
```css
:root {
  /* 断点定义 */
  --breakpoint-xs: 0px;
  --breakpoint-sm: 600px;
  --breakpoint-md: 960px;
  --breakpoint-lg: 1280px;
  --breakpoint-xl: 1920px;
  --breakpoint-xxl: 2560px;

  /* 容器最大宽度 */
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
}
```

#### 布局容器
```css
.v-container {
  padding-left: var(--spacing-4) !important;
  padding-right: var(--spacing-4) !important;
}

.v-container--fluid {
  max-width: 100% !important;
}

.v-row {
  margin-left: calc(-1 * var(--grid-gutter) / 2) !important;
  margin-right: calc(-1 * var(--grid-gutter) / 2) !important;
}

.v-col {
  padding-left: calc(var(--grid-gutter) / 2) !important;
  padding-right: calc(var(--grid-gutter) / 2) !important;
}
```

### 应用布局 (Application Layout)

#### 主布局结构
```css
.v-application {
  font-family: var(--font-family-primary) !important;
  background: var(--bg-page) !important;
}

.v-main {
  padding-top: 80px !important; /* 顶部导航高度 */
}

.v-main .v-main__wrap {
  min-height: calc(100vh - 80px) !important;
}
```

#### 侧边栏布局
```css
.v-navigation-drawer {
  width: 280px !important;
  z-index: 1001 !important;
}

.v-navigation-drawer--rail {
  width: 72px !important;
}

.v-navigation-drawer .v-navigation-drawer__content {
  overflow-y: auto !important;
  scrollbar-width: thin;
  scrollbar-color: var(--border-base) transparent;
}

.v-navigation-drawer .v-navigation-drawer__content::-webkit-scrollbar {
  width: 4px;
}

.v-navigation-drawer .v-navigation-drawer__content::-webkit-scrollbar-track {
  background: transparent;
}

.v-navigation-drawer .v-navigation-drawer__content::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: 2px;
}
```

#### 顶部导航布局
```css
.v-app-bar {
  height: 80px !important;
  z-index: 1000 !important;
}

.v-app-bar .v-toolbar__content {
  height: 80px !important;
  padding-left: var(--spacing-8) !important;
  padding-right: var(--spacing-8) !important;
}
```

---

## 📱 响应式设计

### 断点管理 (Breakpoint Management)

#### 移动端优先 (Mobile First)
```css
/* 基础样式 - 移动端 */
.responsive-container {
  padding: var(--spacing-4);
}

/* 平板端 */
@media (min-width: 600px) {
  .responsive-container {
    padding: var(--spacing-6);
  }
}

/* 桌面端 */
@media (min-width: 960px) {
  .responsive-container {
    padding: var(--spacing-8);
  }
}

/* 大屏幕 */
@media (min-width: 1280px) {
  .responsive-container {
    padding: var(--spacing-10);
  }
}
```

#### 组件响应式
```css
/* 统计卡片响应式 */
.stats-card {
  margin-bottom: var(--spacing-4);
}

@media (min-width: 600px) {
  .stats-card {
    margin-bottom: var(--spacing-6);
  }
}

/* 数据表格响应式 */
.v-data-table {
  font-size: 0.75rem;
}

@media (min-width: 960px) {
  .v-data-table {
    font-size: 0.875rem;
  }
}

/* 侧边栏响应式 */
@media (max-width: 959px) {
  .v-navigation-drawer {
    position: fixed !important;
    z-index: 1002 !important;
  }

  .v-main {
    padding-left: 0 !important;
  }
}
```

### 内容适配 (Content Adaptation)

#### 文字响应式
```css
.responsive-title {
  font-size: 1.5rem;
  line-height: 1.3;
}

@media (min-width: 600px) {
  .responsive-title {
    font-size: 2rem;
  }
}

@media (min-width: 960px) {
  .responsive-title {
    font-size: 2.5rem;
  }
}

.responsive-body {
  font-size: 0.875rem;
  line-height: 1.6;
}

@media (min-width: 960px) {
  .responsive-body {
    font-size: 1rem;
  }
}
```

#### 间距响应式
```css
.responsive-spacing {
  margin-bottom: var(--spacing-4);
}

@media (min-width: 600px) {
  .responsive-spacing {
    margin-bottom: var(--spacing-6);
  }
}

@media (min-width: 960px) {
  .responsive-spacing {
    margin-bottom: var(--spacing-8);
  }
}
```

---

## 🎨 图标规范

### 图标库 (Icon Library)

#### Material Design Icons
```html
<!-- CDN引入 -->
<link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.3.67/css/materialdesignicons.min.css" rel="stylesheet">
```

#### 常用图标映射
```javascript
const iconMap = {
  // 导航图标
  dashboard: 'mdi-view-dashboard',
  orders: 'mdi-file-document-multiple',
  printing: 'mdi-printer-settings',
  logistics: 'mdi-truck-delivery',
  files: 'mdi-folder-multiple',
  settings: 'mdi-cog',
  sync: 'mdi-sync',

  // 操作图标
  add: 'mdi-plus',
  edit: 'mdi-pencil',
  delete: 'mdi-delete',
  view: 'mdi-eye',
  download: 'mdi-download',
  upload: 'mdi-upload',
  search: 'mdi-magnify',
  filter: 'mdi-filter',
  refresh: 'mdi-refresh',

  // 状态图标
  success: 'mdi-check-circle',
  warning: 'mdi-alert',
  error: 'mdi-alert-circle',
  info: 'mdi-information',
  pending: 'mdi-clock-outline',

  // 打印相关
  printer: 'mdi-printer',
  printQueue: 'mdi-format-list-bulleted',
  document: 'mdi-file-document',
  barcode: 'mdi-barcode-scan',

  // 系统图标
  user: 'mdi-account',
  notification: 'mdi-bell-outline',
  menu: 'mdi-menu',
  close: 'mdi-close',
  expand: 'mdi-chevron-down',
  collapse: 'mdi-chevron-up'
};
```

#### 图标尺寸规范
```css
.icon-xs { font-size: 12px !important; }
.icon-sm { font-size: 16px !important; }
.icon-md { font-size: 20px !important; }
.icon-lg { font-size: 24px !important; }
.icon-xl { font-size: 32px !important; }
.icon-2xl { font-size: 40px !important; }
```

---

## ⚙️ Vuetify主题配置

### 主题配置 (Theme Configuration)

```javascript
import { createVuetify } from 'vuetify';

const vuetify = createVuetify({
  theme: {
    defaultTheme: 'light',
    themes: {
      light: {
        dark: false,
        colors: {
          // 主色调
          primary: '#5d87ff',
          'primary-darken-1': '#4570ea',
          'primary-lighten-1': '#ecf2ff',

          // 辅助色
          secondary: '#49beff',
          'secondary-darken-1': '#0891b2',
          'secondary-lighten-1': '#cffafe',

          // 功能色
          success: '#13deb9',
          'success-darken-1': '#059669',
          'success-lighten-1': '#d1fae5',

          warning: '#ffae1f',
          'warning-darken-1': '#d97706',
          'warning-lighten-1': '#fef3c7',

          error: '#fa896b',
          'error-darken-1': '#dc2626',
          'error-lighten-1': '#fee2e2',

          info: '#539bff',
          'info-darken-1': '#2563eb',
          'info-lighten-1': '#dbeafe',

          // 表面色
          surface: '#ffffff',
          'surface-variant': '#f5f5f9',
          'surface-bright': '#f9f9fd',

          // 背景色
          background: '#f5f5f9',

          // 文字色
          'on-surface': '#2a3547',
          'on-surface-variant': '#5a6a85',
          'on-background': '#2a3547'
        }
      },
      dark: {
        dark: true,
        colors: {
          // 暗色主题配置
          primary: '#5d87ff',
          secondary: '#49beff',
          success: '#13deb9',
          warning: '#ffae1f',
          error: '#fa896b',
          info: '#539bff',
          surface: '#1e1e1e',
          background: '#121212'
        }
      }
    }
  },
  defaults: {
    VBtn: {
      style: 'text-transform: none; font-weight: 500;',
      rounded: 'sm'
    },
    VCard: {
      rounded: 'lg',
      elevation: 0
    },
    VTextField: {
      variant: 'outlined',
      density: 'comfortable'
    },
    VSelect: {
      variant: 'outlined',
      density: 'comfortable'
    },
    VDataTable: {
      density: 'comfortable'
    }
  }
});
```

---

## 📚 使用指南

### 开发环境配置

#### 1. 安装依赖
```bash
npm install vue@3 vuetify@3
npm install @mdi/font
```

#### 2. 引入样式
```javascript
// main.js
import { createApp } from 'vue';
import { createVuetify } from 'vuetify';
import 'vuetify/styles';
import '@mdi/font/css/materialdesignicons.css';

const vuetify = createVuetify({
  // 配置选项
});

const app = createApp(App);
app.use(vuetify);
app.mount('#app');
```

#### 3. 自定义CSS变量
```css
/* styles/variables.css */
@import url('./design-tokens.css');

/* 应用自定义变量 */
.v-application {
  font-family: var(--font-family-primary) !important;
}
```

### 组件使用示例

#### 统计卡片
```vue
<template>
  <v-card class="stats-card pa-4" elevation="0">
    <div class="d-flex align-center justify-space-between">
      <div>
        <div class="text-h4 font-weight-bold mb-1">{{ value }}</div>
        <div class="text-body-2 opacity-90">{{ title }}</div>
      </div>
      <v-icon size="40" class="opacity-80">{{ icon }}</v-icon>
    </div>
    <div class="mt-3 d-flex align-center">
      <v-icon size="16" class="mr-1">mdi-trending-up</v-icon>
      <span class="text-caption">{{ trend }}</span>
    </div>
  </v-card>
</template>
```

#### 状态指示器
```vue
<template>
  <span :class="getStatusClass(status)">
    {{ getStatusText(status) }}
  </span>
</template>

<script>
export default {
  methods: {
    getStatusClass(status) {
      const classes = {
        'pending': 'status-indicator status-pending',
        'processing': 'status-indicator status-processing',
        'completed': 'status-indicator status-completed',
        'failed': 'status-indicator status-failed'
      };
      return classes[status] || 'status-indicator';
    }
  }
};
</script>
```

### 最佳实践

#### 1. 组件命名
- 使用PascalCase命名组件
- 组件名应该具有描述性
- 避免使用缩写

#### 2. 样式组织
- 使用CSS变量保持一致性
- 避免内联样式
- 使用Vuetify的工具类

#### 3. 响应式设计
- 移动端优先设计
- 使用Vuetify的栅格系统
- 测试不同屏幕尺寸

#### 4. 性能优化
- 按需引入Vuetify组件
- 使用v-show而非v-if进行频繁切换
- 合理使用计算属性

---

## 🔧 维护指南

### 版本更新
- 定期更新Vuetify版本
- 检查设计规范的兼容性
- 测试现有组件的表现

### 扩展规范
- 新增组件需要遵循现有设计语言
- 更新设计令牌时需要全局测试
- 保持文档的及时更新

### 质量保证
- 使用ESLint和Prettier保持代码质量
- 进行跨浏览器测试
- 确保无障碍访问性

---

*本设计规范基于Vuetify 3.x和Material Design 3.0制定，适用于云打印终端项目的UI开发。*