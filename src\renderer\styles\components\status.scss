// 状态组件样式

.v-chip {
  border-radius: var(--border-radius-full) !important;
  font-weight: var(--font-weight-medium) !important;
  
  &.v-chip--size-small {
    font-size: var(--font-size-caption) !important;
  }
  
  &.v-chip--size-default {
    font-size: var(--font-size-body-2) !important;
  }
  
  &.v-chip--size-large {
    font-size: var(--font-size-body-1) !important;
  }
}

.v-alert {
  border-radius: var(--border-radius-card) !important;
  border-left: 4px solid !important;
  
  &.v-alert--variant-tonal {
    &.v-alert--type-success {
      border-left-color: var(--success-500) !important;
    }
    
    &.v-alert--type-warning {
      border-left-color: var(--warning-500) !important;
    }
    
    &.v-alert--type-error {
      border-left-color: var(--error-500) !important;
    }
    
    &.v-alert--type-info {
      border-left-color: var(--info-500) !important;
    }
  }
}

.v-progress-circular {
  &.v-progress-circular--size-small {
    width: 20px !important;
    height: 20px !important;
  }
  
  &.v-progress-circular--size-default {
    width: 32px !important;
    height: 32px !important;
  }
  
  &.v-progress-circular--size-large {
    width: 48px !important;
    height: 48px !important;
  }
}

.v-progress-linear {
  border-radius: var(--border-radius-full) !important;
}
