"use strict";
/**
 * 环境工具函数
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.platform = exports.isTest = exports.isProd = exports.isDev = void 0;
exports.getAppDataPath = getAppDataPath;
exports.getLogPath = getLogPath;
exports.getTempPath = getTempPath;
exports.getDownloadPath = getDownloadPath;
exports.getDocumentsPath = getDocumentsPath;
exports.getAppVersion = getAppVersion;
exports.getElectronVersion = getElectronVersion;
exports.getNodeVersion = getNodeVersion;
exports.getChromeVersion = getChromeVersion;
exports.getEnvVar = getEnvVar;
exports.checkDevelopment = checkDevelopment;
exports.getAppConfig = getAppConfig;
exports.isDev = process.env.NODE_ENV === 'development';
exports.isProd = process.env.NODE_ENV === 'production';
exports.isTest = process.env.NODE_ENV === 'test';
/**
 * 获取应用数据目录
 */
function getAppDataPath() {
    const { app } = require('electron');
    return app.getPath('userData');
}
/**
 * 获取应用日志目录
 */
function getLogPath() {
    const { app } = require('electron');
    return app.getPath('logs');
}
/**
 * 获取应用临时目录
 */
function getTempPath() {
    const { app } = require('electron');
    return app.getPath('temp');
}
/**
 * 获取应用下载目录
 */
function getDownloadPath() {
    const { app } = require('electron');
    return app.getPath('downloads');
}
/**
 * 获取应用文档目录
 */
function getDocumentsPath() {
    const { app } = require('electron');
    return app.getPath('documents');
}
/**
 * 获取平台信息
 */
exports.platform = {
    isWindows: process.platform === 'win32',
    isMac: process.platform === 'darwin',
    isLinux: process.platform === 'linux'
};
/**
 * 获取应用版本
 */
function getAppVersion() {
    const { app } = require('electron');
    return app.getVersion();
}
/**
 * 获取Electron版本
 */
function getElectronVersion() {
    return process.versions.electron;
}
/**
 * 获取Node.js版本
 */
function getNodeVersion() {
    return process.versions.node;
}
/**
 * 获取Chrome版本
 */
function getChromeVersion() {
    return process.versions.chrome;
}
/**
 * 环境变量获取器
 */
function getEnvVar(key, defaultValue) {
    return process.env[key] || defaultValue;
}
/**
 * 检查是否为开发环境
 */
function checkDevelopment() {
    return exports.isDev || getEnvVar('ELECTRON_IS_DEV') === '1';
}
function getAppConfig() {
    return {
        isDev: exports.isDev,
        isProd: exports.isProd,
        platform: exports.platform,
        version: getAppVersion(),
        electronVersion: getElectronVersion(),
        nodeVersion: getNodeVersion(),
        chromeVersion: getChromeVersion(),
        appDataPath: getAppDataPath(),
        logPath: getLogPath(),
        tempPath: getTempPath()
    };
}
//# sourceMappingURL=env.js.map