<template>
  <div class="order-list">
    <div class="page-header">
      <h1 class="page-title">订单管理</h1>
      <p class="page-subtitle">管理和处理所有订单</p>
    </div>

    <v-card class="content-card" elevation="0">
      <v-card-title>
        订单列表
      </v-card-title>
      <v-card-text>
        <div class="text-center py-8">
          <v-icon icon="mdi-file-document-multiple" size="64" color="grey-lighten-1" />
          <div class="text-h6 mt-4 mb-2">订单管理功能</div>
          <div class="text-body-2 text-grey">将在任务4中实现</div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
// TODO: 在任务4中实现订单管理功能
</script>
