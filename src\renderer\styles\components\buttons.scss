// 按钮组件样式

.v-btn {
  text-transform: none !important;
  font-weight: var(--font-weight-medium) !important;
  border-radius: var(--border-radius-button) !important;
  
  // 主要按钮
  &.v-btn--variant-elevated {
    box-shadow: var(--shadow-button) !important;
    
    &:hover {
      box-shadow: var(--shadow-button-hover) !important;
    }
  }
  
  // 文本按钮
  &.v-btn--variant-text {
    &:hover {
      background-color: rgba(var(--v-theme-primary), 0.08) !important;
    }
  }
  
  // 轮廓按钮
  &.v-btn--variant-outlined {
    border-width: 1px !important;
    
    &:hover {
      background-color: rgba(var(--v-theme-primary), 0.08) !important;
    }
  }
  
  // 色调按钮
  &.v-btn--variant-tonal {
    &:hover {
      opacity: 0.8;
    }
  }
  
  // 扁平按钮
  &.v-btn--variant-flat {
    &:hover {
      opacity: 0.9;
    }
  }
  
  // 尺寸变体
  &.v-btn--size-x-small {
    font-size: var(--font-size-caption) !important;
    padding: 0 8px !important;
    min-height: 24px !important;
  }
  
  &.v-btn--size-small {
    font-size: var(--font-size-body-2) !important;
    padding: 0 12px !important;
    min-height: 32px !important;
  }
  
  &.v-btn--size-default {
    font-size: var(--font-size-body-1) !important;
    padding: 0 16px !important;
    min-height: 40px !important;
  }
  
  &.v-btn--size-large {
    font-size: var(--font-size-body-1) !important;
    padding: 0 24px !important;
    min-height: 48px !important;
  }
  
  &.v-btn--size-x-large {
    font-size: var(--font-size-h6) !important;
    padding: 0 32px !important;
    min-height: 56px !important;
  }
  
  // 禁用状态
  &.v-btn--disabled {
    opacity: 0.4 !important;
    pointer-events: none !important;
  }
  
  // 加载状态
  &.v-btn--loading {
    pointer-events: none !important;
    
    .v-btn__content {
      opacity: 0;
    }
  }
}

// 按钮组
.v-btn-group {
  border-radius: var(--border-radius-button) !important;
  overflow: hidden;
  
  .v-btn {
    border-radius: 0 !important;
    
    &:first-child {
      border-top-left-radius: var(--border-radius-button) !important;
      border-bottom-left-radius: var(--border-radius-button) !important;
    }
    
    &:last-child {
      border-top-right-radius: var(--border-radius-button) !important;
      border-bottom-right-radius: var(--border-radius-button) !important;
    }
  }
}

// 浮动操作按钮
.v-fab {
  box-shadow: var(--shadow-lg) !important;
  
  &:hover {
    box-shadow: var(--shadow-xl) !important;
  }
}

// 图标按钮
.v-btn--icon {
  border-radius: var(--border-radius-full) !important;
  
  &.v-btn--size-small {
    width: 32px !important;
    height: 32px !important;
  }
  
  &.v-btn--size-default {
    width: 40px !important;
    height: 40px !important;
  }
  
  &.v-btn--size-large {
    width: 48px !important;
    height: 48px !important;
  }
}

// 切换按钮
.v-btn-toggle {
  border-radius: var(--border-radius-button) !important;
  
  .v-btn {
    border-radius: 0 !important;
    
    &:first-child {
      border-top-left-radius: var(--border-radius-button) !important;
      border-bottom-left-radius: var(--border-radius-button) !important;
    }
    
    &:last-child {
      border-top-right-radius: var(--border-radius-button) !important;
      border-bottom-right-radius: var(--border-radius-button) !important;
    }
    
    &.v-btn--active {
      background-color: var(--primary-500) !important;
      color: white !important;
    }
  }
}
