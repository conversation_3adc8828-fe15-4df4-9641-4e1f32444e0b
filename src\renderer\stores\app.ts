import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export interface BreadcrumbItem {
  title: string;
  to: string;
  disabled: boolean;
}

export interface CurrentRoute {
  name: string;
  path: string;
  title: string;
  icon?: string;
  parent?: string;
}

export interface UserConfig {
  theme: 'light' | 'dark';
  language: string;
  autoSync: boolean;
  syncInterval: number;
  printerSettings: Record<string, any>;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
  };
}

export interface WindowSize {
  width: number;
  height: number;
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const isAppReady = ref(false);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const isOnline = ref(navigator.onLine);
  
  // 路由相关
  const currentRoute = ref<CurrentRoute | null>(null);
  const breadcrumbs = ref<BreadcrumbItem[]>([]);
  
  // 用户配置
  const userConfig = ref<UserConfig>({
    theme: 'light',
    language: 'zh-CN',
    autoSync: true,
    syncInterval: 300000, // 5分钟
    printerSettings: {},
    notifications: {
      enabled: true,
      sound: true,
      desktop: true
    }
  });
  
  // 窗口状态
  const windowSize = ref<WindowSize>({
    width: window.innerWidth,
    height: window.innerHeight
  });
  
  // 打印机状态
  const printerStatuses = ref<Record<string, any>>({});
  
  // 更新可用状态
  const updateAvailable = ref<any>(null);
  
  // 计算属性
  const isDarkMode = computed(() => userConfig.value.theme === 'dark');
  
  // 操作方法
  const initialize = async () => {
    try {
      isLoading.value = true;
      
      // 初始化应用状态
      console.log('初始化应用状态...');
      
      // 检查网络状态
      updateNetworkStatus();
      
      // 加载用户配置
      await loadUserConfig();
      
      console.log('应用状态初始化完成');
    } catch (err) {
      console.error('应用初始化失败:', err);
      error.value = err instanceof Error ? err.message : '未知错误';
    } finally {
      isLoading.value = false;
    }
  };
  
  const loadUserConfig = async () => {
    try {
      if (window.electronAPI?.config) {
        const config = await window.electronAPI.config.getAll();
        if (config) {
          userConfig.value = { ...userConfig.value, ...config };
        }
      }
    } catch (err) {
      console.warn('加载用户配置失败:', err);
    }
  };
  
  const saveUserConfig = async () => {
    try {
      if (window.electronAPI?.config) {
        await window.electronAPI.config.set('userConfig', userConfig.value);
      }
    } catch (err) {
      console.error('保存用户配置失败:', err);
    }
  };
  
  const updateNetworkStatus = () => {
    isOnline.value = navigator.onLine;
  };
  
  const setAppReady = (ready: boolean) => {
    isAppReady.value = ready;
  };
  
  const setLoading = (loading: boolean) => {
    isLoading.value = loading;
  };
  
  const setError = (errorMessage: string | null) => {
    error.value = errorMessage;
  };
  
  const clearError = () => {
    error.value = null;
  };
  
  const setNetworkStatus = (online: boolean) => {
    isOnline.value = online;
  };
  
  const setCurrentRoute = (route: CurrentRoute) => {
    currentRoute.value = route;
  };
  
  const setBreadcrumbs = (items: BreadcrumbItem[]) => {
    breadcrumbs.value = items;
  };
  
  const updateUserConfig = (config: Partial<UserConfig>) => {
    userConfig.value = { ...userConfig.value, ...config };
    saveUserConfig();
  };
  
  const updateWindowSize = (size: WindowSize) => {
    windowSize.value = size;
  };
  
  const updatePrinterStatus = (printerInfo: any) => {
    printerStatuses.value[printerInfo.id] = printerInfo;
  };
  
  const setUpdateAvailable = (updateInfo: any) => {
    updateAvailable.value = updateInfo;
  };
  
  const closeAllDialogs = () => {
    // TODO: 实现关闭所有对话框的逻辑
    console.log('关闭所有对话框');
  };
  
  // 主题切换
  const toggleTheme = () => {
    const newTheme = userConfig.value.theme === 'light' ? 'dark' : 'light';
    updateUserConfig({ theme: newTheme });
  };
  
  // 语言切换
  const setLanguage = (language: string) => {
    updateUserConfig({ language });
  };
  
  // 通知设置
  const updateNotificationSettings = (settings: Partial<UserConfig['notifications']>) => {
    updateUserConfig({
      notifications: { ...userConfig.value.notifications, ...settings }
    });
  };
  
  // 打印机设置
  const updatePrinterSettings = (printerId: string, settings: any) => {
    const newPrinterSettings = { ...userConfig.value.printerSettings };
    newPrinterSettings[printerId] = settings;
    updateUserConfig({ printerSettings: newPrinterSettings });
  };
  
  // 同步设置
  const updateSyncSettings = (settings: { autoSync?: boolean; syncInterval?: number }) => {
    updateUserConfig(settings);
  };
  
  return {
    // 状态
    isAppReady,
    isLoading,
    error,
    isOnline,
    currentRoute,
    breadcrumbs,
    userConfig,
    windowSize,
    printerStatuses,
    updateAvailable,
    
    // 计算属性
    isDarkMode,
    
    // 方法
    initialize,
    loadUserConfig,
    saveUserConfig,
    updateNetworkStatus,
    setAppReady,
    setLoading,
    setError,
    clearError,
    setNetworkStatus,
    setCurrentRoute,
    setBreadcrumbs,
    updateUserConfig,
    updateWindowSize,
    updatePrinterStatus,
    setUpdateAvailable,
    closeAllDialogs,
    toggleTheme,
    setLanguage,
    updateNotificationSettings,
    updatePrinterSettings,
    updateSyncSettings
  };
});
