import { app, BrowserWindow, ipc<PERSON>ain, Menu, shell } from 'electron';
import { join } from 'path';
import { isDev } from './utils/env';

class PrintTerminalApp {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.initializeServices();
    this.setupAppEvents();
    this.setupIpcHandlers();
  }

  private async initializeServices(): Promise<void> {
    try {
      console.log('服务初始化开始...');
      // TODO: 在任务3中实现数据库和服务初始化
      console.log('服务初始化完成');
    } catch (error) {
      console.error('服务初始化失败:', error);
      app.quit();
    }
  }

  private setupAppEvents(): void {
    // 当所有窗口关闭时退出应用 (macOS除外)
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // 当应用激活时创建窗口 (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow();
      }
    });

    // 应用准备就绪时创建窗口
    app.whenReady().then(async () => {
      await this.createMainWindow();
      this.setupMenu();
    });

    // 应用退出前清理
    app.on('before-quit', async () => {
      await this.cleanup();
    });
  }

  private async createMainWindow(): Promise<void> {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 800,
      show: false,
      icon: join(__dirname, '../../resources/icon.png'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, 'preload.js'),
        webSecurity: !isDev
      }
    });

    // 加载应用
    if (isDev) {
      // 在开发模式下，等待Vite服务器启动
      await this.loadDevURL();
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
    }

    // 窗口准备显示时显示
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    // 处理外部链接
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // 窗口关闭时清理引用
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private async loadDevURL(): Promise<void> {
    const url = 'http://localhost:5173';
    const maxRetries = 10;
    const retryDelay = 1000;

    for (let i = 0; i < maxRetries; i++) {
      try {
        await this.mainWindow!.loadURL(url);
        console.log('成功连接到Vite开发服务器');
        return;
      } catch (error) {
        console.log(`尝试连接Vite服务器 (${i + 1}/${maxRetries})...`);
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    throw new Error('无法连接到Vite开发服务器');
  }

  private setupMenu(): void {
    if (process.platform === 'darwin') {
      // macOS菜单
      const template = [
        {
          label: 'Print Terminal',
          submenu: [
            { role: 'about' },
            { type: 'separator' },
            { role: 'services' },
            { type: 'separator' },
            { role: 'hide' },
            { role: 'hideothers' },
            { role: 'unhide' },
            { type: 'separator' },
            { role: 'quit' }
          ]
        },
        {
          label: '编辑',
          submenu: [
            { role: 'undo' },
            { role: 'redo' },
            { type: 'separator' },
            { role: 'cut' },
            { role: 'copy' },
            { role: 'paste' },
            { role: 'selectall' }
          ]
        },
        {
          label: '视图',
          submenu: [
            { role: 'reload' },
            { role: 'forceReload' },
            { role: 'toggleDevTools' },
            { type: 'separator' },
            { role: 'resetZoom' },
            { role: 'zoomIn' },
            { role: 'zoomOut' },
            { type: 'separator' },
            { role: 'togglefullscreen' }
          ]
        },
        {
          label: '窗口',
          submenu: [
            { role: 'minimize' },
            { role: 'close' }
          ]
        }
      ];

      const menu = Menu.buildFromTemplate(template as any);
      Menu.setApplicationMenu(menu);
    } else {
      // Windows/Linux - 隐藏菜单栏
      Menu.setApplicationMenu(null);
    }
  }

  private setupIpcHandlers(): void {
    // TODO: 在任务3和任务4中实现具体的IPC处理器

    // 基础IPC处理器

    // 应用控制相关IPC
    ipcMain.handle('app:getVersion', () => {
      return app.getVersion();
    });

    ipcMain.handle('app:quit', () => {
      app.quit();
    });

    ipcMain.handle('app:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('app:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });
  }

  private async cleanup(): Promise<void> {
    try {
      // TODO: 在任务3中实现数据库和服务清理
      console.log('应用清理完成');
    } catch (error) {
      console.error('应用清理失败:', error);
    }
  }
}

// 创建应用实例
new PrintTerminalApp();
