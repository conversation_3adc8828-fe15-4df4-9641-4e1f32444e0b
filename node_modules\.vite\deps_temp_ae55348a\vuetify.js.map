{"version": 3, "sources": ["../../vuetify/src/composables/toggleScope.ts", "../../vuetify/src/util/globals.ts", "../../vuetify/src/util/helpers.ts", "../../vuetify/src/util/color/APCA.ts", "../../vuetify/src/util/console.ts", "../../vuetify/src/util/color/transformCIELAB.ts", "../../vuetify/src/util/color/transformSRGB.ts", "../../vuetify/src/util/colorUtils.ts", "../../vuetify/src/util/propsFactory.ts", "../../vuetify/src/composables/component.ts", "../../vuetify/src/util/getCurrentInstance.ts", "../../vuetify/src/util/injectSelf.ts", "../../vuetify/src/composables/defaults.ts", "../../vuetify/src/util/defineComponent.tsx", "../../vuetify/src/util/easing.ts", "../../vuetify/src/composables/proxiedModel.ts", "../../vuetify/src/locale/en.ts", "../../vuetify/src/locale/adapters/vuetify.ts", "../../vuetify/src/composables/locale.ts", "../../vuetify/src/composables/date/adapters/vuetify.ts", "../../vuetify/src/composables/date/date.ts", "../../vuetify/src/composables/display.ts", "../../vuetify/src/composables/goto.ts", "../../vuetify/src/iconsets/mdi.ts", "../../vuetify/src/composables/icons.tsx", "../../vuetify/src/composables/theme.ts", "../../vuetify/src/composables/layout.ts", "../../vuetify/src/composables/hotkey/key-aliases.ts", "../../vuetify/src/composables/hotkey/hotkey-parsing.ts", "../../vuetify/src/composables/hotkey/hotkey.ts", "../../vuetify/src/framework.ts"], "sourcesContent": ["// Utilities\nimport { effectScope, onScopeDispose, watch } from 'vue'\n\n// Types\nimport type { EffectScope, WatchSource } from 'vue'\n\nexport function useToggleScope (source: WatchSource<boolean>, fn: (reset: () => void) => void) {\n  let scope: EffectScope | undefined\n  function start () {\n    scope = effectScope()\n    scope.run(() => fn.length\n      ? fn(() => { scope?.stop(); start() })\n      : (fn as any)()\n    )\n  }\n\n  watch(source, active => {\n    if (active && !scope) {\n      start()\n    } else if (!active) {\n      scope?.stop()\n      scope = undefined\n    }\n  }, { immediate: true })\n\n  onScopeDispose(() => {\n    scope?.stop()\n  })\n}\n", "export const IN_BROWSER = typeof window !== 'undefined'\nexport const SUPPORTS_INTERSECTION = IN_BROWSER && 'IntersectionObserver' in window\nexport const SUPPORTS_TOUCH = IN_BROWSER && ('ontouchstart' in window || window.navigator.maxTouchPoints > 0)\nexport const SUPPORTS_EYE_DROPPER = IN_BROWSER && 'EyeDropper' in window\nexport const SUPPORTS_MATCH_MEDIA = IN_BROWSER && 'matchMedia' in window && typeof window.matchMedia === 'function'\n", "// Utilities\nimport {\n  camelize,\n  capitalize,\n  Comment,\n  Fragment,\n  isVNode,\n  reactive,\n  shallowRef,\n  toRef,\n  unref,\n  watchEffect,\n} from 'vue'\nimport { IN_BROWSER } from '@/util/globals'\n\n// Types\nimport type {\n  ComponentInternalInstance,\n  ComponentPublicInstance,\n  ComputedGetter,\n  InjectionKey,\n  PropType,\n  Ref,\n  ToRef,\n  VNode,\n  VNodeArrayChildren,\n  VNodeChild,\n} from 'vue'\n\nexport function getNestedValue (obj: any, path: (string | number)[], fallback?: any): any {\n  const last = path.length - 1\n\n  if (last < 0) return obj === undefined ? fallback : obj\n\n  for (let i = 0; i < last; i++) {\n    if (obj == null) {\n      return fallback\n    }\n    obj = obj[path[i]]\n  }\n\n  if (obj == null) return fallback\n\n  return obj[path[last]] === undefined ? fallback : obj[path[last]]\n}\n\nexport function deepEqual (a: any, b: any): boolean {\n  if (a === b) return true\n\n  if (\n    a instanceof Date &&\n    b instanceof Date &&\n    a.getTime() !== b.getTime()\n  ) {\n    // If the values are Date, compare them as timestamps\n    return false\n  }\n\n  if (a !== Object(a) || b !== Object(b)) {\n    // If the values aren't objects, they were already checked for equality\n    return false\n  }\n\n  const props = Object.keys(a)\n\n  if (props.length !== Object.keys(b).length) {\n    // Different number of props, don't bother to check\n    return false\n  }\n\n  return props.every(p => deepEqual(a[p], b[p]))\n}\n\nexport function getObjectValueByPath (obj: any, path?: string | null, fallback?: any): any {\n  // credit: http://stackoverflow.com/questions/6491463/accessing-nested-javascript-objects-with-string-key#comment55278413_6491621\n  if (obj == null || !path || typeof path !== 'string') return fallback\n  if (obj[path] !== undefined) return obj[path]\n  path = path.replace(/\\[(\\w+)\\]/g, '.$1') // convert indexes to properties\n  path = path.replace(/^\\./, '') // strip a leading dot\n  return getNestedValue(obj, path.split('.'), fallback)\n}\n\nexport type SelectItemKey<T = Record<string, any>> =\n  | boolean | null | undefined // Ignored\n  | string // Lookup by key, can use dot notation for nested objects\n  | readonly (string | number)[] // Nested lookup by key, each array item is a key in the next level\n  | ((item: T, fallback?: any) => any)\n\nexport function getPropertyFromItem (\n  item: any,\n  property: SelectItemKey,\n  fallback?: any\n): any {\n  if (property === true) return item === undefined ? fallback : item\n\n  if (property == null || typeof property === 'boolean') return fallback\n\n  if (item !== Object(item)) {\n    if (typeof property !== 'function') return fallback\n\n    const value = property(item, fallback)\n\n    return typeof value === 'undefined' ? fallback : value\n  }\n\n  if (typeof property === 'string') return getObjectValueByPath(item, property, fallback)\n\n  if (Array.isArray(property)) return getNestedValue(item, property, fallback)\n\n  if (typeof property !== 'function') return fallback\n\n  const value = property(item, fallback)\n\n  return typeof value === 'undefined' ? fallback : value\n}\n\nexport function createRange (length: number, start = 0): number[] {\n  return Array.from({ length }, (v, k) => start + k)\n}\n\nexport function getZIndex (el?: Element | null): number {\n  if (!el || el.nodeType !== Node.ELEMENT_NODE) return 0\n\n  const index = Number(window.getComputedStyle(el).getPropertyValue('z-index'))\n\n  if (!index) return getZIndex(el.parentNode as Element)\n  return index\n}\n\nexport function convertToUnit (str: number, unit?: string): string\nexport function convertToUnit (str: string | number | null | undefined, unit?: string): string | undefined\nexport function convertToUnit (str: string | number | null | undefined, unit = 'px'): string | undefined {\n  if (str == null || str === '') {\n    return undefined\n  }\n  const num = Number(str)\n  if (isNaN(num)) {\n    return String(str)\n  } else if (!isFinite(num)) {\n    return undefined\n  } else {\n    return `${num}${unit}`\n  }\n}\n\nexport function isObject (obj: any): obj is Record<string, any> {\n  return obj !== null && typeof obj === 'object' && !Array.isArray(obj)\n}\n\nexport function isPlainObject (obj: any): obj is Record<string, any> {\n  let proto\n  return obj !== null && typeof obj === 'object' && (\n    (proto = Object.getPrototypeOf(obj)) === Object.prototype ||\n    proto === null\n  )\n}\n\nexport function refElement (obj?: ComponentPublicInstance<any> | HTMLElement): HTMLElement | undefined {\n  if (obj && '$el' in obj) {\n    const el = obj.$el as HTMLElement\n    if (el?.nodeType === Node.TEXT_NODE) {\n      // Multi-root component, use the first element\n      return el.nextElementSibling as HTMLElement\n    }\n    return el\n  }\n  return obj as HTMLElement\n}\n\n// KeyboardEvent.keyCode aliases\nexport const keyCodes = Object.freeze({\n  enter: 13,\n  tab: 9,\n  delete: 46,\n  esc: 27,\n  space: 32,\n  up: 38,\n  down: 40,\n  left: 37,\n  right: 39,\n  end: 35,\n  home: 36,\n  del: 46,\n  backspace: 8,\n  insert: 45,\n  pageup: 33,\n  pagedown: 34,\n  shift: 16,\n})\n\nexport const keyValues: Record<string, string> = Object.freeze({\n  enter: 'Enter',\n  tab: 'Tab',\n  delete: 'Delete',\n  esc: 'Escape',\n  space: 'Space',\n  up: 'ArrowUp',\n  down: 'ArrowDown',\n  left: 'ArrowLeft',\n  right: 'ArrowRight',\n  end: 'End',\n  home: 'Home',\n  del: 'Delete',\n  backspace: 'Backspace',\n  insert: 'Insert',\n  pageup: 'PageUp',\n  pagedown: 'PageDown',\n  shift: 'Shift',\n})\n\nexport function keys<O extends {}> (o: O) {\n  return Object.keys(o) as (keyof O)[]\n}\n\nexport function has<T extends string> (obj: object, key: T[]): obj is Record<T, unknown> {\n  return key.every(k => obj.hasOwnProperty(k))\n}\n\ntype MaybePick<\n  T extends object,\n  U extends Extract<keyof T, string>\n> = Record<string, unknown> extends T ? Partial<Pick<T, U>> : Pick<T, U>\n\n// Array of keys\nexport function pick<\n  T extends object,\n  U extends Extract<keyof T, string>\n> (obj: T, paths: readonly U[]): MaybePick<T, U> {\n  const found: any = {}\n\n  for (const key of paths) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      found[key] = obj[key]\n    }\n  }\n\n  return found\n}\n\n// Array of keys\nexport function pickWithRest<\n  T extends object,\n  U extends Extract<keyof T, string>,\n  E extends Extract<keyof T, string>\n> (obj: T, paths: U[], exclude?: E[]): [yes: MaybePick<T, Exclude<U, E>>, no: Omit<T, Exclude<U, E>>]\n// Array of keys or RegExp to test keys against\nexport function pickWithRest<\n  T extends object,\n  U extends Extract<keyof T, string>,\n  E extends Extract<keyof T, string>\n> (obj: T, paths: (U | RegExp)[], exclude?: E[]): [yes: Partial<T>, no: Partial<T>]\nexport function pickWithRest<\n  T extends object,\n  U extends Extract<keyof T, string>,\n  E extends Extract<keyof T, string>\n> (obj: T, paths: (U | RegExp)[], exclude?: E[]): [yes: Partial<T>, no: Partial<T>] {\n  const found = Object.create(null)\n  const rest = Object.create(null)\n\n  for (const key in obj) {\n    if (\n      paths.some(path => path instanceof RegExp\n        ? path.test(key)\n        : path === key\n      ) && !exclude?.some(path => path === key)\n    ) {\n      found[key] = obj[key]\n    } else {\n      rest[key] = obj[key]\n    }\n  }\n\n  return [found, rest]\n}\n\nexport function omit<\n  T extends object,\n  U extends Extract<keyof T, string>\n> (obj: T, exclude: U[]): Omit<T, U> {\n  const clone = { ...obj }\n\n  exclude.forEach(prop => delete clone[prop])\n\n  return clone\n}\n\nconst onRE = /^on[^a-z]/\nexport const isOn = (key: string) => onRE.test(key)\n\nconst bubblingEvents = [\n  'onAfterscriptexecute',\n  'onAnimationcancel',\n  'onAnimationend',\n  'onAnimationiteration',\n  'onAnimationstart',\n  'onAuxclick',\n  'onBeforeinput',\n  'onBeforescriptexecute',\n  'onChange',\n  'onClick',\n  'onCompositionend',\n  'onCompositionstart',\n  'onCompositionupdate',\n  'onContextmenu',\n  'onCopy',\n  'onCut',\n  'onDblclick',\n  'onFocusin',\n  'onFocusout',\n  'onFullscreenchange',\n  'onFullscreenerror',\n  'onGesturechange',\n  'onGestureend',\n  'onGesturestart',\n  'onGotpointercapture',\n  'onInput',\n  'onKeydown',\n  'onKeypress',\n  'onKeyup',\n  'onLostpointercapture',\n  'onMousedown',\n  'onMousemove',\n  'onMouseout',\n  'onMouseover',\n  'onMouseup',\n  'onMousewheel',\n  'onPaste',\n  'onPointercancel',\n  'onPointerdown',\n  'onPointerenter',\n  'onPointerleave',\n  'onPointermove',\n  'onPointerout',\n  'onPointerover',\n  'onPointerup',\n  'onReset',\n  'onSelect',\n  'onSubmit',\n  'onTouchcancel',\n  'onTouchend',\n  'onTouchmove',\n  'onTouchstart',\n  'onTransitioncancel',\n  'onTransitionend',\n  'onTransitionrun',\n  'onTransitionstart',\n  'onWheel',\n]\n\nconst compositionIgnoreKeys = [\n  'ArrowUp',\n  'ArrowDown',\n  'ArrowRight',\n  'ArrowLeft',\n  'Enter',\n  'Escape',\n  'Tab',\n  ' ',\n]\n\nexport function isComposingIgnoreKey (e: KeyboardEvent): boolean {\n  return e.isComposing && compositionIgnoreKeys.includes(e.key)\n}\n\n/**\n * Filter attributes that should be applied to\n * the root element of an input component. Remaining\n * attributes should be passed to the <input> element inside.\n */\nexport function filterInputAttrs (attrs: Record<string, unknown>) {\n  const [events, props] = pickWithRest(attrs, [onRE])\n  const inputEvents = omit(events, bubblingEvents)\n  const [rootAttrs, inputAttrs] = pickWithRest(props, ['class', 'style', 'id', /^data-/])\n  Object.assign(rootAttrs, events)\n  Object.assign(inputAttrs, inputEvents)\n  return [rootAttrs, inputAttrs]\n}\n\n/**\n * Returns the set difference of B and A, i.e. the set of elements in B but not in A\n */\nexport function arrayDiff (a: any[], b: any[]): any[] {\n  const diff: any[] = []\n  for (let i = 0; i < b.length; i++) {\n    if (!a.includes(b[i])) diff.push(b[i])\n  }\n  return diff\n}\n\ntype IfAny<T, Y, N> = 0 extends (1 & T) ? Y : N;\nexport function wrapInArray<T> (\n  v: T | null | undefined\n): T extends readonly any[]\n    ? IfAny<T, T[], T>\n    : NonNullable<T>[] {\n  return v == null\n    ? [] as any\n    : Array.isArray(v)\n      ? v as any : [v] as any\n}\n\nexport function defaultFilter (value: any, search: string | null, item: any) {\n  return value != null &&\n    search != null &&\n    typeof value !== 'boolean' &&\n    value.toString().toLocaleLowerCase().indexOf(search.toLocaleLowerCase()) !== -1\n}\n\nexport function debounce (fn: Function, delay: MaybeRef<number>) {\n  let timeoutId = 0 as any\n  const wrap = (...args: any[]) => {\n    clearTimeout(timeoutId)\n    timeoutId = setTimeout(() => fn(...args), unref(delay))\n  }\n  wrap.clear = () => {\n    clearTimeout(timeoutId)\n  }\n  wrap.immediate = fn\n  return wrap\n}\n\nexport function throttle<T extends (...args: any[]) => any> (fn: T, limit: number) {\n  let throttling = false\n  return (...args: Parameters<T>): void | ReturnType<T> => {\n    if (!throttling) {\n      throttling = true\n      setTimeout(() => throttling = false, limit)\n      return fn(...args)\n    }\n  }\n}\n\nexport function clamp (value: number, min = 0, max = 1) {\n  return Math.max(min, Math.min(max, value))\n}\n\nexport function getDecimals (value: number) {\n  const trimmedStr = value.toString().trim()\n  return trimmedStr.includes('.')\n    ? (trimmedStr.length - trimmedStr.indexOf('.') - 1)\n    : 0\n}\n\nexport function padEnd (str: string, length: number, char = '0') {\n  return str + char.repeat(Math.max(0, length - str.length))\n}\n\nexport function padStart (str: string, length: number, char = '0') {\n  return char.repeat(Math.max(0, length - str.length)) + str\n}\n\nexport function chunk (str: string, size = 1) {\n  const chunked: string[] = []\n  let index = 0\n  while (index < str.length) {\n    chunked.push(str.substr(index, size))\n    index += size\n  }\n  return chunked\n}\n\nexport function chunkArray (array: any[], size = 1) {\n  return Array.from({ length: Math.ceil(array.length / size) }, (v, i) =>\n    array.slice(i * size, i * size + size)\n  )\n}\n\nexport function humanReadableFileSize (bytes: number, base: 1000 | 1024 = 1000): string {\n  if (bytes < base) {\n    return `${bytes} B`\n  }\n\n  const prefix = base === 1024 ? ['Ki', 'Mi', 'Gi'] : ['k', 'M', 'G']\n  let unit = -1\n  while (Math.abs(bytes) >= base && unit < prefix.length - 1) {\n    bytes /= base\n    ++unit\n  }\n  return `${bytes.toFixed(1)} ${prefix[unit]}B`\n}\n\nexport function mergeDeep (\n  source: Record<string, any> = {},\n  target: Record<string, any> = {},\n  arrayFn?: (a: unknown[], b: unknown[]) => unknown[],\n) {\n  const out: Record<string, any> = {}\n\n  for (const key in source) {\n    out[key] = source[key]\n  }\n\n  for (const key in target) {\n    const sourceProperty = source[key]\n    const targetProperty = target[key]\n\n    // Only continue deep merging if\n    // both properties are plain objects\n    if (isPlainObject(sourceProperty) && isPlainObject(targetProperty)) {\n      out[key] = mergeDeep(sourceProperty, targetProperty, arrayFn)\n\n      continue\n    }\n\n    if (arrayFn && Array.isArray(sourceProperty) && Array.isArray(targetProperty)) {\n      out[key] = arrayFn(sourceProperty, targetProperty)\n\n      continue\n    }\n\n    out[key] = targetProperty\n  }\n\n  return out\n}\n\nexport function flattenFragments (nodes: VNode[]): VNode[] {\n  return nodes.map(node => {\n    if (node.type === Fragment) {\n      return flattenFragments(node.children as VNode[])\n    } else {\n      return node\n    }\n  }).flat()\n}\n\nexport function toKebabCase (str = '') {\n  if (toKebabCase.cache.has(str)) return toKebabCase.cache.get(str)!\n  const kebab = str\n    .replace(/[^a-z]/gi, '-')\n    .replace(/\\B([A-Z])/g, '-$1')\n    .toLowerCase()\n  toKebabCase.cache.set(str, kebab)\n  return kebab\n}\ntoKebabCase.cache = new Map<string, string>()\n\nexport type MaybeRef<T> = T | Ref<T>\n\nexport function findChildrenWithProvide (\n  key: InjectionKey<any> | symbol,\n  vnode?: VNodeChild,\n): ComponentInternalInstance[] {\n  if (!vnode || typeof vnode !== 'object') return []\n\n  if (Array.isArray(vnode)) {\n    return vnode.map(child => findChildrenWithProvide(key, child)).flat(1)\n  } else if (vnode.suspense) {\n    return findChildrenWithProvide(key, vnode.ssContent!)\n  } else if (Array.isArray(vnode.children)) {\n    return vnode.children.map(child => findChildrenWithProvide(key, child)).flat(1)\n  } else if (vnode.component) {\n    if (Object.getOwnPropertySymbols(vnode.component.provides).includes(key as symbol)) {\n      return [vnode.component]\n    } else if (vnode.component.subTree) {\n      return findChildrenWithProvide(key, vnode.component.subTree).flat(1)\n    }\n  }\n\n  return []\n}\n\nexport class CircularBuffer<T = never> {\n  readonly #arr: Array<T> = []\n  #pointer = 0\n\n  constructor (public readonly size: number) {}\n\n  get isFull () {\n    return this.#arr.length === this.size\n  }\n\n  push (val: T) {\n    this.#arr[this.#pointer] = val\n    this.#pointer = (this.#pointer + 1) % this.size\n  }\n\n  values (): T[] {\n    return this.#arr.slice(this.#pointer).concat(this.#arr.slice(0, this.#pointer))\n  }\n\n  clear () {\n    this.#arr.length = 0\n    this.#pointer = 0\n  }\n}\n\nexport type UnionToIntersection<U> =\n  (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never\n\nexport function getEventCoordinates (e: MouseEvent | TouchEvent) {\n  if ('touches' in e) {\n    return { clientX: e.touches[0].clientX, clientY: e.touches[0].clientY }\n  }\n\n  return { clientX: e.clientX, clientY: e.clientY }\n}\n\n// Only allow a single return type\ntype NotAUnion<T> = [T] extends [infer U] ? _NotAUnion<U, U> : never\ntype _NotAUnion<T, U> = U extends any ? [T] extends [U] ? unknown : never : never\n\ntype ToReadonlyRefs<T> = { [K in keyof T]: Readonly<ToRef<T[K]>> }\n\n/**\n * Convert a computed ref to a record of refs.\n * The getter function must always return an object with the same keys.\n */\nexport function destructComputed<T extends object> (getter: ComputedGetter<T & NotAUnion<T>>): ToReadonlyRefs<T>\nexport function destructComputed<T extends object> (getter: ComputedGetter<T>) {\n  const refs = reactive({}) as T\n  watchEffect(() => {\n    const base = getter()\n    for (const key in base) {\n      refs[key] = base[key]\n    }\n  }, { flush: 'sync' })\n  const obj = {} as ToReadonlyRefs<T>\n  for (const key in refs) {\n    obj[key] = toRef(() => refs[key]) as any\n  }\n  return obj\n}\n\n/** Array.includes but value can be any type */\nexport function includes (arr: readonly any[], val: any) {\n  return arr.includes(val)\n}\n\nexport function eventName (propName: string) {\n  return propName[2].toLowerCase() + propName.slice(3)\n}\n\n// TODO: this should be an array but vue's types don't accept arrays: vuejs/core#8025\nexport type EventProp<T extends any[] = any[], F = (...args: T) => void> = F\nexport const EventProp = <T extends any[] = any[]>() => [Function, Array] as PropType<EventProp<T>>\n\nexport function hasEvent (props: Record<string, any>, name: string) {\n  name = 'on' + capitalize(name)\n  return !!(props[name] || props[`${name}Once`] || props[`${name}Capture`] || props[`${name}OnceCapture`] || props[`${name}CaptureOnce`])\n}\n\nexport function callEvent<T extends any[]> (handler: EventProp<T> | EventProp<T>[] | undefined, ...args: T) {\n  if (Array.isArray(handler)) {\n    for (const h of handler) {\n      h(...args)\n    }\n  } else if (typeof handler === 'function') {\n    handler(...args)\n  }\n}\n\nexport function focusableChildren (el: Element, filterByTabIndex = true) {\n  const targets = ['button', '[href]', 'input:not([type=\"hidden\"])', 'select', 'textarea', '[tabindex]']\n    .map(s => `${s}${filterByTabIndex ? ':not([tabindex=\"-1\"])' : ''}:not([disabled])`)\n    .join(', ')\n  return [...el.querySelectorAll(targets)] as HTMLElement[]\n}\n\nexport function getNextElement (elements: HTMLElement[], location?: 'next' | 'prev', condition?: (el: HTMLElement) => boolean) {\n  let _el\n  let idx = elements.indexOf(document.activeElement as HTMLElement)\n  const inc = location === 'next' ? 1 : -1\n  do {\n    idx += inc\n    _el = elements[idx]\n  } while ((!_el || _el.offsetParent == null || !(condition?.(_el) ?? true)) && idx < elements.length && idx >= 0)\n  return _el\n}\n\nexport function focusChild (el: Element, location?: 'next' | 'prev' | 'first' | 'last' | number) {\n  const focusable = focusableChildren(el)\n\n  if (location == null) {\n    if (el === document.activeElement || !el.contains(document.activeElement)) {\n      focusable[0]?.focus()\n    }\n  } else if (location === 'first') {\n    focusable[0]?.focus()\n  } else if (location === 'last') {\n    focusable.at(-1)?.focus()\n  } else if (typeof location === 'number') {\n    focusable[location]?.focus()\n  } else {\n    const _el = getNextElement(focusable, location)\n    if (_el) _el.focus()\n    else focusChild(el, location === 'next' ? 'first' : 'last')\n  }\n}\n\nexport function isEmpty (val: any): boolean {\n  return val === null || val === undefined || (typeof val === 'string' && val.trim() === '')\n}\n\nexport function noop () {}\n\n/** Returns null if the selector is not supported or we can't check */\nexport function matchesSelector (el: Element | undefined, selector: string): boolean | null {\n  const supportsSelector = IN_BROWSER &&\n    typeof CSS !== 'undefined' &&\n    typeof CSS.supports !== 'undefined' &&\n    CSS.supports(`selector(${selector})`)\n\n  if (!supportsSelector) return null\n\n  try {\n    return !!el && el.matches(selector)\n  } catch (err) {\n    return null\n  }\n}\n\nexport function ensureValidVNode (vnodes: VNodeArrayChildren): VNodeArrayChildren | null {\n  return vnodes.some(child => {\n    if (!isVNode(child)) return true\n    if (child.type === Comment) return false\n    return child.type !== Fragment ||\n      ensureValidVNode(child.children as VNodeArrayChildren)\n  })\n    ? vnodes\n    : null\n}\n\ntype Slot<T> = [T] extends [never] ? () => VNodeChild : (arg: T) => VNodeChild\n\nexport function renderSlot <T> (slot: Slot<never> | undefined, fallback?: Slot<never> | undefined): VNodeChild\nexport function renderSlot <T> (slot: Slot<T> | undefined, props: T, fallback?: Slot<T> | undefined): VNodeChild\nexport function renderSlot (slot?: Slot<unknown>, props?: unknown, fallback?: Slot<unknown>) {\n  // TODO: check if slot returns elements: #18308\n  return slot?.(props) ?? fallback?.(props)\n}\n\nexport function defer (timeout: number, cb: () => void) {\n  if (!IN_BROWSER || timeout === 0) {\n    cb()\n\n    return () => {}\n  }\n\n  const timeoutId = window.setTimeout(cb, timeout)\n\n  return () => window.clearTimeout(timeoutId)\n}\n\nexport function isClickInsideElement (event: MouseEvent, targetDiv: HTMLElement) {\n  const mouseX = event.clientX\n  const mouseY = event.clientY\n\n  const divRect = targetDiv.getBoundingClientRect()\n  const divLeft = divRect.left\n  const divTop = divRect.top\n  const divRight = divRect.right\n  const divBottom = divRect.bottom\n\n  return mouseX >= divLeft && mouseX <= divRight && mouseY >= divTop && mouseY <= divBottom\n}\n\nexport type TemplateRef = {\n  (target: Element | ComponentPublicInstance | null): void\n  value: HTMLElement | ComponentPublicInstance | null | undefined\n  readonly el: HTMLElement | undefined\n}\nexport function templateRef () {\n  const el = shallowRef<HTMLElement | ComponentPublicInstance | null>()\n  const fn = (target: HTMLElement | ComponentPublicInstance | null) => {\n    el.value = target\n  }\n  Object.defineProperty(fn, 'value', {\n    enumerable: true,\n    get: () => el.value,\n    set: val => el.value = val,\n  })\n  Object.defineProperty(fn, 'el', {\n    enumerable: true,\n    get: () => refElement(el.value),\n  })\n\n  return fn as TemplateRef\n}\n\nexport function checkPrintable (e: KeyboardEvent) {\n  const isPrintableChar = e.key.length === 1\n  const noModifier = !e.ctrlKey && !e.metaKey && !e.altKey\n  return isPrintableChar && noModifier\n}\n\nexport type Primitive = string | number | boolean | symbol | bigint\nexport function isPrimitive (value: unknown): value is Primitive {\n  return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' || typeof value === 'bigint'\n}\n\nexport function escapeForRegex (sign: string) {\n  return '\\\\^$*+?.()|{}[]'.includes(sign)\n    ? `\\\\${sign}`\n    : sign\n}\n\nexport function extractNumber (text: string, decimalDigitsLimit: number | null, decimalSeparator: string) {\n  const onlyValidCharacters = new RegExp(`[\\\\d\\\\-${escapeForRegex(decimalSeparator)}]`)\n  const cleanText = text.split('')\n    .filter(x => onlyValidCharacters.test(x))\n    .filter((x, i, all) => (i === 0 && /[-]/.test(x)) || // sign allowed at the start\n        (x === decimalSeparator && i === all.indexOf(x)) || // decimal separator allowed only once\n        /\\d/.test(x))\n    .join('')\n\n  if (decimalDigitsLimit === 0) {\n    return cleanText.split(decimalSeparator)[0]\n  }\n\n  const decimalPart = new RegExp(`${escapeForRegex(decimalSeparator)}\\\\d`)\n  if (decimalDigitsLimit !== null && decimalPart.test(cleanText)) {\n    const parts = cleanText.split(decimalSeparator)\n    return [\n      parts[0],\n      parts[1].substring(0, decimalDigitsLimit),\n    ].join(decimalSeparator)\n  }\n\n  return cleanText\n}\n\nexport function camelizeProps<T extends Record<string, unknown>> (props: T | null): T {\n  const out = {} as T\n  for (const prop in props) {\n    out[camelize(prop) as keyof T] = props[prop]\n  }\n  return out\n}\n\nexport function onlyDefinedProps (props: Record<string, any>) {\n  const booleanAttributes = ['checked', 'disabled']\n  return Object.fromEntries(Object.entries(props)\n    .filter(([key, v]) => booleanAttributes.includes(key) ? !!v : v !== undefined))\n}\n\nexport type NonEmptyArray<T> = [T, ...T[]]\n", "/**\n * WCAG 3.0 APCA perceptual contrast algorithm from https://github.com/Myndex/SAPC-APCA\n * @licence https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n * @see https://www.w3.org/WAI/GL/task-forces/silver/wiki/Visual_Contrast_of_Text_Subgroup\n */\n// Types\nimport type { RGB } from '@/util'\n\n// MAGICAL NUMBERS\n\n// sRGB Conversion to Relative Luminance (Y)\n\n// Transfer Curve (aka \"Gamma\") for sRGB linearization\n// Simple power curve vs piecewise described in docs\n// Essentially, 2.4 best models actual display\n// characteristics in combination with the total method\nconst mainTRC = 2.4\n\nconst Rco = 0.2126729 // sRGB Red Coefficient (from matrix)\nconst Gco = 0.7151522 // sRGB Green Coefficient (from matrix)\nconst Bco = 0.0721750 // sRGB Blue Coefficient (from matrix)\n\n// For Finding Raw SAPC Contrast from Relative Luminance (Y)\n\n// Constants for SAPC Power Curve Exponents\n// One pair for normal text, and one for reverse\n// These are the \"beating heart\" of SAPC\nconst normBG = 0.55\nconst normTXT = 0.58\nconst revTXT = 0.57\nconst revBG = 0.62\n\n// For Clamping and Scaling Values\n\nconst blkThrs = 0.03 // Level that triggers the soft black clamp\nconst blkClmp = 1.45 // Exponent for the soft black clamp curve\nconst deltaYmin = 0.0005 // Lint trap\nconst scaleBoW = 1.25 // Scaling for dark text on light\nconst scaleWoB = 1.25 // Scaling for light text on dark\nconst loConThresh = 0.078 // Threshold for new simple offset scale\nconst loConFactor = 12.82051282051282 // = 1/0.078,\nconst loConOffset = 0.06 // The simple offset\nconst loClip = 0.001 // Output clip (lint trap #2)\n\nexport function APCAcontrast (text: RGB, background: RGB) {\n  // Linearize sRGB\n  const Rtxt = (text.r / 255) ** mainTRC\n  const Gtxt = (text.g / 255) ** mainTRC\n  const Btxt = (text.b / 255) ** mainTRC\n\n  const Rbg = (background.r / 255) ** mainTRC\n  const Gbg = (background.g / 255) ** mainTRC\n  const Bbg = (background.b / 255) ** mainTRC\n\n  // Apply the standard coefficients and sum to Y\n  let Ytxt = (Rtxt * Rco) + (Gtxt * Gco) + (Btxt * Bco)\n  let Ybg = (Rbg * Rco) + (Gbg * Gco) + (Bbg * Bco)\n\n  // Soft clamp Y when near black.\n  // Now clamping all colors to prevent crossover errors\n  if (Ytxt <= blkThrs) Ytxt += (blkThrs - Ytxt) ** blkClmp\n  if (Ybg <= blkThrs) Ybg += (blkThrs - Ybg) ** blkClmp\n\n  // Return 0 Early for extremely low ∆Y (lint trap #1)\n  if (Math.abs(Ybg - Ytxt) < deltaYmin) return 0.0\n\n  // SAPC CONTRAST\n\n  let outputContrast: number // For weighted final values\n  if (Ybg > Ytxt) {\n    // For normal polarity, black text on white\n    // Calculate the SAPC contrast value and scale\n\n    const SAPC = ((Ybg ** normBG) - (Ytxt ** normTXT)) * scaleBoW\n\n    // NEW! SAPC SmoothScale™\n    // Low Contrast Smooth Scale Rollout to prevent polarity reversal\n    // and also a low clip for very low contrasts (lint trap #2)\n    // much of this is for very low contrasts, less than 10\n    // therefore for most reversing needs, only loConOffset is important\n    outputContrast =\n      (SAPC < loClip) ? 0.0\n      : (SAPC < loConThresh) ? SAPC - SAPC * loConFactor * loConOffset\n      : SAPC - loConOffset\n  } else {\n    // For reverse polarity, light text on dark\n    // WoB should always return negative value.\n\n    const SAPC = ((Ybg ** revBG) - (Ytxt ** revTXT)) * scaleWoB\n\n    outputContrast =\n      (SAPC > -loClip) ? 0.0\n      : (SAPC > -loConThresh) ? SAPC - SAPC * loConFactor * loConOffset\n      : SAPC + loConOffset\n  }\n\n  return outputContrast * 100\n}\n", "/* eslint-disable no-console */\n\n// Utilities\nimport { warn } from 'vue'\n\nexport function consoleWarn (message: string): void {\n  warn(`Vuetify: ${message}`)\n}\n\nexport function consoleError (message: string): void {\n  warn(`Vuetify error: ${message}`)\n}\n\nexport function deprecate (original: string, replacement: string | string[]) {\n  replacement = Array.isArray(replacement)\n    ? replacement.slice(0, -1).map(s => `'${s}'`).join(', ') + ` or '${replacement.at(-1)}'`\n    : `'${replacement}'`\n  warn(`[Vuetify UPGRADE] '${original}' is deprecated, use ${replacement} instead.`)\n}\nexport function breaking (original: string, replacement: string) {\n  // warn(`[Vuetify BREAKING] '${original}' has been removed, use '${replacement}' instead. For more information, see the upgrade guide https://github.com/vuetifyjs/vuetify/releases/tag/v2.0.0#user-content-upgrade-guide`)\n}\nexport function removed (original: string) {\n  // warn(`[Vuetify REMOVED] '${original}' has been removed. You can safely omit it.`)\n}\n", "// Types\nimport type { LAB, XYZ } from '../colorUtils'\n\nconst delta = 0.20689655172413793 // 6÷29\n\nconst cielabForwardTransform = (t: number): number => (\n  t > delta ** 3\n    ? Math.cbrt(t)\n    : (t / (3 * delta ** 2)) + 4 / 29\n)\n\nconst cielabReverseTransform = (t: number): number => (\n  t > delta\n    ? t ** 3\n    : (3 * delta ** 2) * (t - 4 / 29)\n)\n\nexport function fromXYZ (xyz: XYZ): LAB {\n  const transform = cielabForwardTransform\n  const transformedY = transform(xyz[1])\n\n  return [\n    116 * transformedY - 16,\n    500 * (transform(xyz[0] / 0.95047) - transformedY),\n    200 * (transformedY - transform(xyz[2] / 1.08883)),\n  ]\n}\n\nexport function toXYZ (lab: LAB): XYZ {\n  const transform = cielabReverseTransform\n  const Ln = (lab[0] + 16) / 116\n  return [\n    transform(Ln + lab[1] / 500) * 0.95047,\n    transform(Ln),\n    transform(Ln - lab[2] / 200) * 1.08883,\n  ]\n}\n", "// Utilities\nimport { clamp } from '@/util/helpers'\n\n// Types\nimport type { RGB, XYZ } from '../colorUtils'\n\n// For converting XYZ to sRGB\nconst srgbForwardMatrix = [\n  [3.2406, -1.5372, -0.4986],\n  [-0.9689, 1.8758, 0.0415],\n  [0.0557, -0.2040, 1.0570],\n]\n\n// Forward gamma adjust\nconst srgbForwardTransform = (C: number): number => (\n  C <= 0.0031308\n    ? C * 12.92\n    : 1.055 * C ** (1 / 2.4) - 0.055\n)\n\n// For converting sRGB to XYZ\nconst srgbReverseMatrix = [\n  [0.4124, 0.3576, 0.1805],\n  [0.2126, 0.7152, 0.0722],\n  [0.0193, 0.1192, 0.9505],\n]\n\n// Reverse gamma adjust\nconst srgbReverseTransform = (C: number): number => (\n  C <= 0.04045\n    ? C / 12.92\n    : ((C + 0.055) / 1.055) ** 2.4\n)\n\nexport function fromXYZ (xyz: XYZ): RGB {\n  const rgb = Array(3)\n  const transform = srgbForwardTransform\n  const matrix = srgbForwardMatrix\n\n  // Matrix transform, then gamma adjustment\n  for (let i = 0; i < 3; ++i) {\n    // Rescale back to [0, 255]\n    rgb[i] = Math.round(clamp(transform(\n      matrix[i][0] * xyz[0] +\n      matrix[i][1] * xyz[1] +\n      matrix[i][2] * xyz[2]\n    )) * 255)\n  }\n\n  return {\n    r: rgb[0],\n    g: rgb[1],\n    b: rgb[2],\n  }\n}\n\nexport function toXYZ ({ r, g, b }: RGB): XYZ {\n  const xyz: XYZ = [0, 0, 0]\n  const transform = srgbReverseTransform\n  const matrix = srgbReverseMatrix\n\n  // Rescale from [0, 255] to [0, 1] then adjust sRGB gamma to linear RGB\n  r = transform(r / 255)\n  g = transform(g / 255)\n  b = transform(b / 255)\n\n  // Matrix color space transform\n  for (let i = 0; i < 3; ++i) {\n    xyz[i] = matrix[i][0] * r + matrix[i][1] * g + matrix[i][2] * b\n  }\n\n  return xyz\n}\n", "// Utilities\nimport { APCAcontrast } from './color/APCA'\nimport { consoleWarn } from './console'\nimport { chunk, has, padEnd } from './helpers'\nimport * as CIELAB from '@/util/color/transformCIELAB'\nimport * as sRGB from '@/util/color/transformSRGB'\n\n// Types\nimport type { Colors } from '@/composables/theme'\n\nexport type XYZ = [number, number, number]\nexport type LAB = [number, number, number]\nexport type HSV = { h: number, s: number, v: number, a?: number }\nexport type RGB = { r: number, g: number, b: number, a?: number }\nexport type HSL = { h: number, s: number, l: number, a?: number }\nexport type Hex = string & { __hexBrand: never }\nexport type Color = string | number | HSV | RGB | HSL\n\nexport function isCssColor (color?: string | null | false): boolean {\n  return !!color && /^(#|var\\(--|(rgb|hsl)a?\\()/.test(color)\n}\n\nexport function isParsableColor (color: string): boolean {\n  return isCssColor(color) && !/^((rgb|hsl)a?\\()?var\\(--/.test(color)\n}\n\nconst cssColorRe = /^(?<fn>(?:rgb|hsl)a?)\\((?<values>.+)\\)/\nconst mappers = {\n  rgb: (r: number, g: number, b: number, a?: number) => ({ r, g, b, a }),\n  rgba: (r: number, g: number, b: number, a?: number) => ({ r, g, b, a }),\n  hsl: (h: number, s: number, l: number, a?: number) => HSLtoRGB({ h, s, l, a }),\n  hsla: (h: number, s: number, l: number, a?: number) => HSLtoRGB({ h, s, l, a }),\n  hsv: (h: number, s: number, v: number, a?: number) => HSVtoRGB({ h, s, v, a }),\n  hsva: (h: number, s: number, v: number, a?: number) => HSVtoRGB({ h, s, v, a }),\n}\n\nexport function parseColor (color: Color): RGB {\n  if (typeof color === 'number') {\n    if (isNaN(color) || color < 0 || color > 0xFFFFFF) { // int can't have opacity\n      consoleWarn(`'${color}' is not a valid hex color`)\n    }\n\n    return {\n      r: (color & 0xFF0000) >> 16,\n      g: (color & 0xFF00) >> 8,\n      b: (color & 0xFF),\n    }\n  } else if (typeof color === 'string' && cssColorRe.test(color)) {\n    const { groups } = color.match(cssColorRe)!\n    const { fn, values } = groups as { fn: keyof typeof mappers, values: string }\n    const realValues = values.split(/,\\s*|\\s*\\/\\s*|\\s+/)\n      .map((v, i) => {\n        if (\n          v.endsWith('%') ||\n          // unitless slv are %\n          (i > 0 && i < 3 && ['hsl', 'hsla', 'hsv', 'hsva'].includes(fn))\n        ) {\n          return parseFloat(v) / 100\n        } else {\n          return parseFloat(v)\n        }\n      }) as [number, number, number, number?]\n\n    return mappers[fn](...realValues)\n  } else if (typeof color === 'string') {\n    let hex = color.startsWith('#') ? color.slice(1) : color\n\n    if ([3, 4].includes(hex.length)) {\n      hex = hex.split('').map(char => char + char).join('')\n    } else if (![6, 8].includes(hex.length)) {\n      consoleWarn(`'${color}' is not a valid hex(a) color`)\n    }\n\n    const int = parseInt(hex, 16)\n    if (isNaN(int) || int < 0 || int > 0xFFFFFFFF) {\n      consoleWarn(`'${color}' is not a valid hex(a) color`)\n    }\n\n    return HexToRGB(hex as Hex)\n  } else if (typeof color === 'object') {\n    if (has(color, ['r', 'g', 'b'])) {\n      return color\n    } else if (has(color, ['h', 's', 'l'])) {\n      return HSVtoRGB(HSLtoHSV(color))\n    } else if (has(color, ['h', 's', 'v'])) {\n      return HSVtoRGB(color)\n    }\n  }\n\n  throw new TypeError(`Invalid color: ${color == null ? color : (String(color) || (color as any).constructor.name)}\\nExpected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)\n}\n\nexport function RGBToInt (color: RGB) {\n  return (color.r << 16) + (color.g << 8) + color.b\n}\n\nexport function classToHex (\n  color: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<Colors>,\n): string {\n  const [colorName, colorModifier] = color\n    .toString().trim().replace('-', '').split(' ', 2) as (string | undefined)[]\n\n  let hexColor = ''\n  if (colorName && colorName in colors) {\n    if (colorModifier && colorModifier in colors[colorName]) {\n      hexColor = colors[colorName][colorModifier]\n    } else if ('base' in colors[colorName]) {\n      hexColor = colors[colorName].base\n    }\n  } else if (colorName && colorName in currentTheme) {\n    hexColor = currentTheme[colorName] as string\n  }\n\n  return hexColor\n}\n\n/** Converts HSVA to RGBA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV */\nexport function HSVtoRGB (hsva: HSV): RGB {\n  const { h, s, v, a } = hsva\n  const f = (n: number) => {\n    const k = (n + (h / 60)) % 6\n    return v - v * s * Math.max(Math.min(k, 4 - k, 1), 0)\n  }\n\n  const rgb = [f(5), f(3), f(1)].map(v => Math.round(v * 255))\n\n  return { r: rgb[0], g: rgb[1], b: rgb[2], a }\n}\n\nexport function HSLtoRGB (hsla: HSL): RGB {\n  return HSVtoRGB(HSLtoHSV(hsla))\n}\n\n/** Converts RGBA to HSVA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV */\nexport function RGBtoHSV (rgba: RGB): HSV {\n  if (!rgba) return { h: 0, s: 1, v: 1, a: 1 }\n\n  const r = rgba.r / 255\n  const g = rgba.g / 255\n  const b = rgba.b / 255\n  const max = Math.max(r, g, b)\n  const min = Math.min(r, g, b)\n\n  let h = 0\n\n  if (max !== min) {\n    if (max === r) {\n      h = 60 * (0 + ((g - b) / (max - min)))\n    } else if (max === g) {\n      h = 60 * (2 + ((b - r) / (max - min)))\n    } else if (max === b) {\n      h = 60 * (4 + ((r - g) / (max - min)))\n    }\n  }\n\n  if (h < 0) h = h + 360\n\n  const s = max === 0 ? 0 : (max - min) / max\n  const hsv = [h, s, max]\n\n  return { h: hsv[0], s: hsv[1], v: hsv[2], a: rgba.a }\n}\n\nexport function HSVtoHSL (hsva: HSV): HSL {\n  const { h, s, v, a } = hsva\n\n  const l = v - (v * s / 2)\n\n  const sprime = l === 1 || l === 0 ? 0 : (v - l) / Math.min(l, 1 - l)\n\n  return { h, s: sprime, l, a }\n}\n\nexport function HSLtoHSV (hsl: HSL): HSV {\n  const { h, s, l, a } = hsl\n\n  const v = l + s * Math.min(l, 1 - l)\n\n  const sprime = v === 0 ? 0 : 2 - (2 * l / v)\n\n  return { h, s: sprime, v, a }\n}\n\nexport function RGBtoCSS ({ r, g, b, a }: RGB): string {\n  return a === undefined ? `rgb(${r}, ${g}, ${b})` : `rgba(${r}, ${g}, ${b}, ${a})`\n}\n\nexport function HSVtoCSS (hsva: HSV): string {\n  return RGBtoCSS(HSVtoRGB(hsva))\n}\n\nfunction toHex (v: number) {\n  const h = Math.round(v).toString(16)\n  return ('00'.substr(0, 2 - h.length) + h).toUpperCase()\n}\n\nexport function RGBtoHex ({ r, g, b, a }: RGB): Hex {\n  return `#${[\n    toHex(r),\n    toHex(g),\n    toHex(b),\n    a !== undefined ? toHex(Math.round(a * 255)) : '',\n  ].join('')}` as Hex\n}\n\nexport function HexToRGB (hex: Hex): RGB {\n  hex = parseHex(hex)\n  let [r, g, b, a] = chunk(hex, 2).map((c: string) => parseInt(c, 16))\n  a = a === undefined ? a : (a / 255)\n\n  return { r, g, b, a }\n}\n\nexport function HexToHSV (hex: Hex): HSV {\n  const rgb = HexToRGB(hex)\n  return RGBtoHSV(rgb)\n}\n\nexport function HSVtoHex (hsva: HSV): Hex {\n  return RGBtoHex(HSVtoRGB(hsva))\n}\n\nexport function parseHex (hex: string): Hex {\n  if (hex.startsWith('#')) {\n    hex = hex.slice(1)\n  }\n\n  hex = hex.replace(/([^0-9a-f])/gi, 'F')\n\n  if (hex.length === 3 || hex.length === 4) {\n    hex = hex.split('').map(x => x + x).join('')\n  }\n\n  if (hex.length !== 6) {\n    hex = padEnd(padEnd(hex, 6), 8, 'F')\n  }\n\n  return hex as Hex\n}\n\nexport function parseGradient (\n  gradient: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<Colors>,\n) {\n  return gradient.replace(/([a-z]+(\\s[a-z]+-[1-5])?)(?=$|,)/gi, x => {\n    return classToHex(x, colors, currentTheme) || x\n  }).replace(/(rgba\\()#[0-9a-f]+(?=,)/gi, x => {\n    return 'rgba(' + Object.values(HexToRGB(parseHex(x.replace(/rgba\\(/, '')))).slice(0, 3).join(',')\n  })\n}\n\nexport function lighten (value: RGB, amount: number): RGB {\n  const lab = CIELAB.fromXYZ(sRGB.toXYZ(value))\n  lab[0] = lab[0] + amount * 10\n\n  return sRGB.fromXYZ(CIELAB.toXYZ(lab))\n}\n\nexport function darken (value: RGB, amount: number): RGB {\n  const lab = CIELAB.fromXYZ(sRGB.toXYZ(value))\n  lab[0] = lab[0] - amount * 10\n\n  return sRGB.fromXYZ(CIELAB.toXYZ(lab))\n}\n\n/**\n * Calculate the relative luminance of a given color\n * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n */\nexport function getLuma (color: Color) {\n  const rgb = parseColor(color)\n\n  return sRGB.toXYZ(rgb)[1]\n}\n\n/**\n * Returns the contrast ratio (1-21) between two colors.\n * @see https://www.w3.org/TR/WCAG20/#contrast-ratiodef\n */\nexport function getContrast (first: Color, second: Color) {\n  const l1 = getLuma(first)\n  const l2 = getLuma(second)\n\n  const light = Math.max(l1, l2)\n  const dark = Math.min(l1, l2)\n\n  return (light + 0.05) / (dark + 0.05)\n}\n\nexport function getForeground (color: Color) {\n  const blackContrast = Math.abs(APCAcontrast(parseColor(0), parseColor(color)))\n  const whiteContrast = Math.abs(APCAcontrast(parseColor(0xffffff), parseColor(color)))\n\n  // TODO: warn about poor color selections\n  // const contrastAsText = Math.abs(APCAcontrast(colorVal, colorToInt(theme.colors.background)))\n  // const minContrast = Math.max(blackContrast, whiteContrast)\n  // if (minContrast < 60) {\n  //   consoleInfo(`${key} theme color ${color} has poor contrast (${minContrast.toFixed()}%)`)\n  // } else if (contrastAsText < 60 && !['background', 'surface'].includes(color)) {\n  //   consoleInfo(`${key} theme color ${color} has poor contrast as text (${contrastAsText.toFixed()}%)`)\n  // }\n\n  // Prefer white text if both have an acceptable contrast ratio\n  return whiteContrast > Math.min(blackContrast, 50) ? '#fff' : '#000'\n}\n", "// Types\nimport type { IfAny } from '@vue/shared' // eslint-disable-line vue/prefer-import-from-vue\nimport type { ComponentObjectPropsOptions, Prop, PropType } from 'vue'\n\n/**\n * Creates a factory function for props definitions.\n * This is used to define props in a composable then override\n * default values in an implementing component.\n *\n * @example Simplified signature\n * (props: Props) => (defaults?: Record<keyof props, any>) => Props\n *\n * @example Usage\n * const makeProps = propsFactory({\n *   foo: String,\n * })\n *\n * defineComponent({\n *   props: {\n *     ...makeProps({\n *       foo: 'a',\n *     }),\n *   },\n *   setup (props) {\n *     // would be \"string | undefined\", now \"string\" because a default has been provided\n *     props.foo\n *   },\n * }\n */\n\nexport function propsFactory<\n  PropsOptions extends ComponentObjectPropsOptions\n> (props: PropsOptions, source: string) {\n  return <Defaults extends PartialKeys<PropsOptions> = {}>(\n    defaults?: Defaults\n  ): AppendDefault<PropsOptions, Defaults> => {\n    return Object.keys(props).reduce<any>((obj, prop) => {\n      const isObjectDefinition = typeof props[prop] === 'object' && props[prop] != null && !Array.isArray(props[prop])\n      const definition = isObjectDefinition ? props[prop] : { type: props[prop] }\n\n      if (defaults && prop in defaults) {\n        obj[prop] = {\n          ...definition,\n          default: defaults[prop],\n        }\n      } else {\n        obj[prop] = definition\n      }\n\n      if (source && !obj[prop].source) {\n        obj[prop].source = source\n      }\n\n      return obj\n    }, {})\n  }\n}\n\ntype AppendDefault<T extends ComponentObjectPropsOptions, D extends PartialKeys<T>> = {\n  [P in keyof T]-?: unknown extends D[P]\n    ? T[P]\n    : T[P] extends Record<string, unknown>\n      ? Omit<T[P], 'type' | 'default'> & {\n        type: PropType<MergeTypeDefault<T[P], D[P]>>\n        default: MergeDefault<T[P], D[P]>\n      }\n      : {\n        type: PropType<MergeTypeDefault<T[P], D[P]>>\n        default: MergeDefault<T[P], D[P]>\n      }\n}\n\ntype MergeTypeDefault<T, D, P = InferPropType<T>> = unknown extends D\n  ? P\n  : (P | D)\ntype MergeDefault<T, D, P = InferPropType<T>> = unknown extends D\n  ? P\n  : (NonNullable<P> | D)\n\n/**\n * Like `Partial<T>` but doesn't care what the value is\n */\ntype PartialKeys<T> = { [P in keyof T]?: unknown }\n\n// Copied from Vue\ntype InferPropType<T> = [T] extends [null]\n  ? any // null & true would fail to infer\n  : [T] extends [{ type: null | true }]\n    // As TS issue https://github.com/Microsoft/TypeScript/issues/14829\n    // somehow `ObjectConstructor` when inferred from { (): T } becomes `any`\n    // `BooleanConstructor` when inferred from PropConstructor(with PropMethod) becomes `Boolean`\n    ? any\n    : [T] extends [ObjectConstructor | { type: ObjectConstructor }]\n      ? Record<string, any>\n      : [T] extends [BooleanConstructor | { type: BooleanConstructor }]\n        ? boolean\n        : [T] extends [DateConstructor | { type: DateConstructor }]\n          ? Date\n          : [T] extends [(infer U)[] | { type: (infer U)[] }]\n            ? U extends DateConstructor\n              ? Date | InferPropType<U>\n              : InferPropType<U>\n            : [T] extends [Prop<infer V, infer D>]\n              ? unknown extends V\n                ? IfAny<V, V, D>\n                : V\n              : T\n", "// Utilities\nimport { propsFactory } from '@/util/propsFactory'\n\n// Types\nimport type { PropType, StyleValue } from 'vue'\n\nexport type ClassValue = any\n\nexport interface ComponentProps {\n  class: ClassValue\n  style: StyleValue | undefined\n}\n\n// Composables\nexport const makeComponentProps = propsFactory({\n  class: [String, Array, Object] as PropType<ClassValue>,\n  style: {\n    type: [String, Array, Object] as PropType<StyleValue>,\n    default: null,\n  },\n}, 'component')\n", "// Utilities\nimport { getCurrentInstance as _getCurrentInstance } from 'vue'\nimport { toKebabCase } from '@/util/helpers'\n\nexport function getCurrentInstance (name: string, message?: string) {\n  const vm = _getCurrentInstance()\n\n  if (!vm) {\n    throw new Error(`[Vuetify] ${name} ${message || 'must be called from inside a setup function'}`)\n  }\n\n  return vm\n}\n\nexport function getCurrentInstanceName (name = 'composables') {\n  const vm = getCurrentInstance(name).type\n\n  return toKebabCase(vm?.aliasName || vm?.name)\n}\n", "// Utilities\nimport { getCurrentInstance } from '@/util/getCurrentInstance'\n\n// Types\nimport type { ComponentInternalInstance, InjectionKey } from 'vue'\n\nexport function injectSelf<T>(key: InjectionKey<T> | string, vm?: ComponentInternalInstance): T | undefined\nexport function injectSelf (key: InjectionKey<any> | string, vm = getCurrentInstance('injectSelf')) {\n  const { provides } = vm\n\n  if (provides && (key as string | symbol) in provides) {\n    // TS doesn't allow symbol as index type\n    return provides[key as string]\n  }\n  return undefined\n}\n", "// Utilities\nimport { computed, inject, provide, ref, shallowRef, unref, watchEffect } from 'vue'\nimport { getCurrentInstance } from '@/util/getCurrentInstance'\nimport { mergeDeep, toKebabCase } from '@/util/helpers'\nimport { injectSelf } from '@/util/injectSelf'\n\n// Types\nimport type { ComputedRef, InjectionKey, Ref, VNode } from 'vue'\nimport type { MaybeRef } from '@/util'\n\nexport type DefaultsInstance = undefined | {\n  [key: string]: undefined | Record<string, unknown>\n  global?: Record<string, unknown>\n}\n\nexport type DefaultsOptions = Partial<DefaultsInstance>\n\nexport const DefaultsSymbol: InjectionKey<Ref<DefaultsInstance>> = Symbol.for('vuetify:defaults')\n\nexport function createDefaults (options?: DefaultsInstance): Ref<DefaultsInstance> {\n  return ref(options)\n}\n\nexport function injectDefaults () {\n  const defaults = inject(DefaultsSymbol)\n\n  if (!defaults) throw new Error('[Vuetify] Could not find defaults instance')\n\n  return defaults\n}\n\nexport function provideDefaults (\n  defaults?: MaybeRef<DefaultsInstance | undefined>,\n  options?: {\n    disabled?: MaybeRef<boolean | undefined>\n    reset?: MaybeRef<number | string | undefined>\n    root?: MaybeRef<boolean | string | undefined>\n    scoped?: MaybeRef<boolean | undefined>\n  }\n) {\n  const injectedDefaults = injectDefaults()\n  const providedDefaults = ref(defaults)\n\n  const newDefaults = computed(() => {\n    const disabled = unref(options?.disabled)\n\n    if (disabled) return injectedDefaults.value\n\n    const scoped = unref(options?.scoped)\n    const reset = unref(options?.reset)\n    const root = unref(options?.root)\n\n    if (providedDefaults.value == null && !(scoped || reset || root)) return injectedDefaults.value\n\n    let properties = mergeDeep(providedDefaults.value, { prev: injectedDefaults.value })\n\n    if (scoped) return properties\n\n    if (reset || root) {\n      const len = Number(reset || Infinity)\n\n      for (let i = 0; i <= len; i++) {\n        if (!properties || !('prev' in properties)) {\n          break\n        }\n\n        properties = properties.prev\n      }\n\n      if (properties && typeof root === 'string' && root in properties) {\n        properties = mergeDeep(mergeDeep(properties, { prev: properties }), properties[root])\n      }\n\n      return properties\n    }\n\n    return properties.prev\n      ? mergeDeep(properties.prev, properties)\n      : properties\n  }) as ComputedRef<DefaultsInstance>\n\n  provide(DefaultsSymbol, newDefaults)\n\n  return newDefaults\n}\n\nfunction propIsDefined (vnode: VNode, prop: string) {\n  return vnode.props && (typeof vnode.props[prop] !== 'undefined' ||\n    typeof vnode.props[toKebabCase(prop)] !== 'undefined')\n}\n\nexport function internalUseDefaults (\n  props: Record<string, any> = {},\n  name?: string,\n  defaults = injectDefaults()\n) {\n  const vm = getCurrentInstance('useDefaults')\n\n  name = name ?? vm.type.name ?? vm.type.__name\n  if (!name) {\n    throw new Error('[Vuetify] Could not determine component name')\n  }\n\n  const componentDefaults = computed(() => defaults.value?.[props._as ?? name])\n  const _props = new Proxy(props, {\n    get (target, prop: string) {\n      const propValue = Reflect.get(target, prop)\n      if (prop === 'class' || prop === 'style') {\n        return [componentDefaults.value?.[prop], propValue].filter(v => v != null)\n      }\n      if (propIsDefined(vm.vnode, prop)) return propValue\n      const _componentDefault = componentDefaults.value?.[prop]\n      if (_componentDefault !== undefined) return _componentDefault\n      const _globalDefault = defaults.value?.global?.[prop]\n      if (_globalDefault !== undefined) return _globalDefault\n      return propValue\n    },\n  })\n\n  const _subcomponentDefaults = shallowRef()\n  watchEffect(() => {\n    if (componentDefaults.value) {\n      const subComponents = Object.entries(componentDefaults.value)\n        .filter(([key]) => key.startsWith(key[0].toUpperCase()))\n      _subcomponentDefaults.value = subComponents.length ? Object.fromEntries(subComponents) : undefined\n    } else {\n      _subcomponentDefaults.value = undefined\n    }\n  })\n\n  function provideSubDefaults () {\n    const injected = injectSelf(DefaultsSymbol, vm)\n    provide(DefaultsSymbol, computed(() => {\n      return _subcomponentDefaults.value ? mergeDeep(\n        injected?.value ?? {},\n        _subcomponentDefaults.value\n      ) : injected?.value\n    }))\n  }\n\n  return { props: _props, provideSubDefaults }\n}\n\nexport function useDefaults<T extends Record<string, any>> (props: T, name?: string): T\nexport function useDefaults (props?: undefined, name?: string): Record<string, any>\nexport function useDefaults (\n  props: Record<string, any> = {},\n  name?: string,\n) {\n  const { props: _props, provideSubDefaults } = internalUseDefaults(props, name)\n  provideSubDefaults()\n  return _props\n}\n", "// Composables\nimport { injectDefaults, internalUseDefaults } from '@/composables/defaults'\n\n// Utilities\nimport {\n  defineComponent as _defineComponent, // eslint-disable-line no-restricted-imports\n} from 'vue'\nimport { consoleWarn } from '@/util/console'\nimport { pick } from '@/util/helpers'\nimport { propsFactory } from '@/util/propsFactory'\n\n// Types\nimport type {\n  AllowedComponentProps,\n  Component,\n  ComponentCustomProps,\n  ComponentInjectOptions,\n  ComponentObjectPropsOptions,\n  ComponentOptions,\n  ComponentOptionsMixin,\n  ComponentOptionsWithObjectProps,\n  ComponentOptionsWithoutProps,\n  ComponentPropsOptions,\n  ComponentPublicInstance,\n  ComputedOptions,\n  DefineComponent,\n  EmitsOptions,\n  ExtractDefaultPropTypes,\n  ExtractPropTypes,\n  FunctionalComponent,\n  MethodOptions,\n  ObjectEmitsOptions,\n  SlotsType,\n  VNode,\n  VNodeChild,\n  VNodeProps,\n} from 'vue'\n\n// No props\nexport function defineComponent<\n  Props = {},\n  RawBindings = {},\n  D = {},\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = {},\n  EE extends string = string,\n  I extends {} = {},\n  II extends string = string,\n  S extends SlotsType = {},\n>(\n  options: ComponentOptionsWithoutProps<\n    Props,\n    RawBindings,\n    D,\n    C,\n    M,\n    Mixin,\n    Extends,\n    E,\n    EE,\n    I,\n    II,\n    S\n  >\n): DefineComponent<Props, RawBindings, D, C, M, Mixin, Extends, E, EE>\n\n// Object Props\nexport function defineComponent<\n  PropsOptions extends Readonly<ComponentPropsOptions>,\n  RawBindings,\n  D,\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = {},\n  EE extends string = string,\n  I extends {} = {},\n  II extends string = string,\n  S extends SlotsType = {},\n>(\n  options: ComponentOptionsWithObjectProps<\n    PropsOptions,\n    RawBindings,\n    D,\n    C,\n    M,\n    Mixin,\n    Extends,\n    E,\n    EE,\n    I,\n    II,\n    S\n  >\n): DefineComponent<PropsOptions, RawBindings, D, C, M, Mixin, Extends, E, EE> & FilterPropsOptions<PropsOptions>\n\n// Implementation\nexport function defineComponent (options: ComponentOptions) {\n  options._setup = options._setup ?? options.setup\n\n  if (!options.name) {\n    consoleWarn('The component is missing an explicit name, unable to generate default prop value')\n\n    return options\n  }\n\n  if (options._setup) {\n    options.props = propsFactory(options.props ?? {}, options.name)()\n    const propKeys = Object.keys(options.props).filter(key => key !== 'class' && key !== 'style')\n    options.filterProps = function filterProps (props: Record<string, any>) {\n      return pick(props, propKeys)\n    }\n\n    options.props._as = String\n    options.setup = function setup (props: Record<string, any>, ctx) {\n      const defaults = injectDefaults()\n\n      // Skip props proxy if defaults are not provided\n      if (!defaults.value) return options._setup(props, ctx)\n\n      const { props: _props, provideSubDefaults } = internalUseDefaults(props, props._as ?? options.name, defaults)\n\n      const setupBindings = options._setup(_props, ctx)\n\n      provideSubDefaults()\n\n      return setupBindings\n    }\n  }\n\n  return options\n}\n\ntype ToListeners<T extends string | number | symbol> = { [K in T]: K extends `on${infer U}` ? Uncapitalize<U> : K }[T]\n\nexport type SlotsToProps<\n  U extends RawSlots,\n  T = MakeInternalSlots<U>\n> = {\n  $children?: (\n    | VNodeChild\n    | (T extends { default: infer V } ? V : {})\n    | { [K in keyof T]?: T[K] }\n    | { $stable?: boolean }\n  )\n  'v-slots'?: { [K in keyof T]?: T[K] | false }\n} & {\n  [K in keyof T as `v-slot:${K & string}`]?: T[K] | false\n}\n\ntype RawSlots = Record<string, unknown>\ntype Slot<T> = [T] extends [never] ? () => VNodeChild : (arg: T) => VNodeChild\ntype VueSlot<T> = [T] extends [never] ? () => VNode[] : (arg: T) => VNode[]\ntype MakeInternalSlots<T extends RawSlots> = {\n  [K in keyof T]: Slot<T[K]>\n}\ntype MakeSlots<T extends RawSlots> = {\n  [K in keyof T]: VueSlot<T[K]>\n}\n\nexport type GenericProps<Props, Slots extends Record<string, unknown>> = {\n  $props: Props & SlotsToProps<Slots>\n  $slots: MakeSlots<Slots>\n}\n\ntype DefineComponentWithGenericProps<T extends (new (props: Record<string, any>, slots: RawSlots) => {\n  $props?: Record<string, any>\n})> = <\n  PropsOptions extends Readonly<ComponentObjectPropsOptions>,\n  RawBindings,\n  D,\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = Record<string, any>,\n  EE extends string = string,\n  I extends ComponentInjectOptions = {},\n  II extends string = string,\n  // Slots extends RawSlots = ConstructorParameters<T> extends [any, infer SS extends RawSlots | undefined] ? Exclude<SS, undefined> : {},\n  Slots extends RawSlots = ConstructorParameters<T>[1],\n  S extends SlotsType = SlotsType<Partial<MakeSlots<Slots>>>,\n  III = InstanceType<T>,\n  P = III extends Record<'$props', any>\n    ? Omit<PropsOptions, keyof III['$props']>\n    : PropsOptions,\n  EEE extends EmitsOptions = E extends any[]\n    ? E\n    : III extends Record<'$props', any>\n      ? Omit<E, ToListeners<keyof III['$props']>>\n      : E,\n  Base = DefineComponent<\n    P,\n    RawBindings,\n    D,\n    C,\n    M,\n    Mixin,\n    Extends,\n    EEE,\n    EE,\n    PublicProps,\n    ExtractPropTypes<P> & ({} extends E ? {} : EmitsToProps<EEE>),\n    ExtractDefaultPropTypes<P>,\n    S\n  >\n>(\n  options: ComponentOptionsWithObjectProps<PropsOptions, RawBindings, D, C, M, Mixin, Extends, E, EE, I, II, S>\n) => Base & T & FilterPropsOptions<PropsOptions>\n\ntype DefineComponentWithSlots<Slots extends RawSlots> = <\n  PropsOptions extends Readonly<ComponentPropsOptions>,\n  RawBindings,\n  D,\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = Record<string, any>,\n  EE extends string = string,\n  I extends ComponentInjectOptions = {},\n  II extends string = string,\n  S extends SlotsType = SlotsType<Partial<MakeSlots<Slots>>>,\n>(\n  options: ComponentOptionsWithObjectProps<PropsOptions, RawBindings, D, C, M, Mixin, Extends, E, EE, I, II, S>\n) => DefineComponent<\n  ExtractPropTypes<PropsOptions> & SlotsToProps<Slots>,\n  RawBindings,\n  D,\n  C,\n  M,\n  Mixin,\n  Extends,\n  E,\n  EE,\n  PublicProps,\n  ExtractPropTypes<PropsOptions> & SlotsToProps<Slots> & ({} extends E ? {} : EmitsToProps<E>),\n  ExtractDefaultPropTypes<PropsOptions>,\n  S\n> & FilterPropsOptions<PropsOptions>\n\n// No argument - simple default slot\nexport function genericComponent (exposeDefaults?: boolean): DefineComponentWithSlots<{ default: never }>\n\n// Generic constructor argument - generic props and slots\nexport function genericComponent<T extends (new (props: Record<string, any>, slots: any) => {\n  $props?: Record<string, any>\n})> (exposeDefaults?: boolean): DefineComponentWithGenericProps<T>\n\n// Slots argument - simple slots\nexport function genericComponent<\n  Slots extends RawSlots\n> (exposeDefaults?: boolean): DefineComponentWithSlots<Slots>\n\n// Implementation\nexport function genericComponent (exposeDefaults = true) {\n  return (options: any) => ((exposeDefaults ? defineComponent : _defineComponent) as any)(options)\n}\n\nexport function defineFunctionalComponent<\n  T extends FunctionalComponent<Props>,\n  PropsOptions = ComponentObjectPropsOptions,\n  Defaults = ExtractDefaultPropTypes<PropsOptions>,\n  Props = Readonly<ExtractPropTypes<PropsOptions>>,\n> (props: PropsOptions, render: T): FunctionalComponent<Partial<Defaults> & Omit<Props, keyof Defaults>> {\n  render.props = props as any\n  return render as any\n}\n\ntype EmitsToProps<T extends EmitsOptions> = T extends string[]\n  ? {\n    [K in string & `on${Capitalize<T[number]>}`]?: (...args: any[]) => any\n  }\n  : T extends ObjectEmitsOptions\n    ? {\n      [K in string &\n        `on${Capitalize<string & keyof T>}`]?: K extends `on${infer C}`\n        ? T[Uncapitalize<C>] extends null\n          ? (...args: any[]) => any\n          : (\n            ...args: T[Uncapitalize<C>] extends (...args: infer P) => any\n              ? P\n              : never\n          ) => any\n        : never\n    }\n    : {}\n\ntype PublicProps =\n  & VNodeProps\n  & AllowedComponentProps\n  & ComponentCustomProps\n\n// Adds a filterProps method to the component options\nexport interface FilterPropsOptions<PropsOptions extends Readonly<ComponentPropsOptions>, Props = ExtractPropTypes<PropsOptions>> {\n  filterProps<\n    T extends Partial<Props>,\n    U extends Exclude<keyof Props, Exclude<keyof Props, keyof T>>\n  > (props: T): Partial<Pick<T, U>>\n}\n\n// https://github.com/vuejs/core/pull/10557\nexport type ComponentInstance<T> = T extends { new (): ComponentPublicInstance<any, any, any> }\n  ? InstanceType<T>\n  : T extends FunctionalComponent<infer Props, infer Emits>\n    ? ComponentPublicInstance<Props, {}, {}, {}, {}, ShortEmitsToObject<Emits>>\n    : T extends Component<\n          infer Props,\n          infer RawBindings,\n          infer D,\n          infer C,\n          infer M\n        >\n      ? // NOTE we override Props/RawBindings/D to make sure is not `unknown`\n      ComponentPublicInstance<\n          unknown extends Props ? {} : Props,\n          unknown extends RawBindings ? {} : RawBindings,\n          unknown extends D ? {} : D,\n          C,\n          M\n        >\n      : never // not a vue Component\n\ntype ShortEmitsToObject<E> = E extends Record<string, any[]> ? {\n  [K in keyof E]: (...args: E[K]) => any;\n} : E;\n\nexport type JSXComponent<Props = any> =\n  | { new (): ComponentPublicInstance<Props> }\n  | FunctionalComponent<Props>\n", "// Utilities\nimport { computed, shallowRef, toValue, watch } from 'vue'\nimport { clamp } from './helpers'\n\n// Types\nimport type { MaybeRefOrGetter, Ref } from 'vue'\n\nexport const standardEasing = 'cubic-bezier(0.4, 0, 0.2, 1)'\nexport const deceleratedEasing = 'cubic-bezier(0.0, 0, 0.2, 1)' // Entering\nexport const acceleratedEasing = 'cubic-bezier(0.4, 0, 1, 1)' // Leaving\n\nexport type EasingFunction = (n: number) => number\n\nexport const easingPatterns = {\n  linear: (t: number) => t,\n  easeInQuad: (t: number) => t ** 2,\n  easeOutQuad: (t: number) => t * (2 - t),\n  easeInOutQuad: (t: number) => (t < 0.5 ? 2 * t ** 2 : -1 + (4 - 2 * t) * t),\n  easeInCubic: (t: number) => t ** 3,\n  easeOutCubic: (t: number) => --t ** 3 + 1,\n  easeInOutCubic: (t: number) => t < 0.5 ? 4 * t ** 3 : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,\n  easeInQuart: (t: number) => t ** 4,\n  easeOutQuart: (t: number) => 1 - --t ** 4,\n  easeInOutQuart: (t: number) => (t < 0.5 ? 8 * t ** 4 : 1 - 8 * --t ** 4),\n  easeInQuint: (t: number) => t ** 5,\n  easeOutQuint: (t: number) => 1 + --t ** 5,\n  easeInOutQuint: (t: number) => t < 0.5 ? 16 * t ** 5 : 1 + 16 * --t ** 5,\n} as const\n\nexport type EasingOptions = {\n  duration?: number\n  transition?: EasingFunction\n}\n\ntype InternalEasingOptions = {\n  duration: number\n  transition: EasingFunction\n}\n\nexport function useTransition (source: MaybeRefOrGetter<number>, options: MaybeRefOrGetter<EasingOptions>) {\n  const defaultTransition: InternalEasingOptions = {\n    duration: 300,\n    transition: easingPatterns.easeInOutCubic,\n  }\n\n  let raf = -1\n  const outputRef = shallowRef(toValue(source))\n\n  watch(() => toValue(source), async to => {\n    cancelAnimationFrame(raf)\n    const easing = { ...defaultTransition, ...toValue(options) }\n    await executeTransition(outputRef, outputRef.value, to, easing)\n  })\n\n  function executeTransition (out: Ref<number>, from: number, to: number, options: InternalEasingOptions) {\n    const startTime = performance.now()\n    const ease = options.transition ?? easingPatterns.easeInOutCubic\n\n    return new Promise<void>(resolve => {\n      raf = requestAnimationFrame(function step (currentTime: number) {\n        const timeElapsed = currentTime - startTime\n        const progress = timeElapsed / options.duration\n        out.value = from + (to - from) * ease(clamp(progress, 0, 1))\n\n        if (progress < 1) {\n          raf = requestAnimationFrame(step)\n        } else {\n          out.value = to\n          resolve()\n        }\n      })\n    })\n  }\n\n  return computed(() => outputRef.value)\n}\n", "// Composables\nimport { useToggleScope } from '@/composables/toggleScope'\n\n// Utilities\nimport { computed, ref, toRaw, watch } from 'vue'\nimport { getCurrentInstance, toKebabCase } from '@/util'\n\n// Types\nimport type { Ref } from 'vue'\nimport type { EventProp } from '@/util'\n\ntype InnerVal<T> = T extends any[] ? Readonly<T> : T\n\n// Composables\nexport function useProxiedModel<\n  Props extends object & { [key in Prop as `onUpdate:${Prop}`]: EventProp | undefined },\n  Prop extends Extract<keyof Props, string>,\n  Inner = Props[Prop],\n> (\n  props: Props,\n  prop: Prop,\n  defaultValue?: Props[Prop],\n  transformIn: (value?: Props[Prop]) => Inner = (v: any) => v,\n  transformOut: (value: Inner) => Props[Prop] = (v: any) => v,\n) {\n  const vm = getCurrentInstance('useProxiedModel')\n  const internal = ref(props[prop] !== undefined ? props[prop] : defaultValue) as Ref<Props[Prop]>\n  const kebabProp = toKebabCase(prop)\n  const checkKebab = kebabProp !== prop\n\n  const isControlled = checkKebab\n    ? computed(() => {\n      void props[prop]\n      return !!(\n        (vm.vnode.props?.hasOwnProperty(prop) || vm.vnode.props?.hasOwnProperty(kebabProp)) &&\n        (vm.vnode.props?.hasOwnProperty(`onUpdate:${prop}`) || vm.vnode.props?.hasOwnProperty(`onUpdate:${kebabProp}`))\n      )\n    })\n    : computed(() => {\n      void props[prop]\n      return !!(vm.vnode.props?.hasOwnProperty(prop) && vm.vnode.props?.hasOwnProperty(`onUpdate:${prop}`))\n    })\n\n  useToggleScope(() => !isControlled.value, () => {\n    watch(() => props[prop], val => {\n      internal.value = val\n    })\n  })\n\n  const model = computed({\n    get (): any {\n      const externalValue = props[prop]\n      return transformIn(isControlled.value ? externalValue : internal.value)\n    },\n    set (internalValue) {\n      const newValue = transformOut(internalValue)\n      const value = toRaw(isControlled.value ? props[prop] : internal.value)\n      if (value === newValue || transformIn(value) === internalValue) {\n        return\n      }\n      internal.value = newValue\n      vm?.emit(`update:${prop}`, newValue)\n    },\n  }) as any as Ref<InnerVal<Inner>> & { readonly externalValue: Props[Prop] }\n\n  Object.defineProperty(model, 'externalValue', {\n    get: () => isControlled.value ? props[prop] : internal.value,\n  })\n\n  return model\n}\n", "export default {\n  badge: 'Badge',\n  open: 'Open',\n  close: 'Close',\n  dismiss: 'Dismiss',\n  confirmEdit: {\n    ok: 'OK',\n    cancel: 'Cancel',\n  },\n  dataIterator: {\n    noResultsText: 'No matching records found',\n    loadingText: 'Loading items...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Rows per page:',\n    ariaLabel: {\n      sortDescending: 'Sorted descending.',\n      sortAscending: 'Sorted ascending.',\n      sortNone: 'Not sorted.',\n      activateNone: 'Activate to remove sorting.',\n      activateDescending: 'Activate to sort descending.',\n      activateAscending: 'Activate to sort ascending.',\n    },\n    sortBy: 'Sort by',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Items per page:',\n    itemsPerPageAll: 'All',\n    nextPage: 'Next page',\n    prevPage: 'Previous page',\n    firstPage: 'First page',\n    lastPage: 'Last page',\n    pageText: '{0}-{1} of {2}',\n  },\n  dateRangeInput: {\n    divider: 'to',\n  },\n  datePicker: {\n    itemsSelected: '{0} selected',\n    range: {\n      title: 'Select dates',\n      header: 'Enter dates',\n    },\n    title: 'Select date',\n    header: 'Enter date',\n    input: {\n      placeholder: 'Enter date',\n    },\n    ariaLabel: {\n      previousMonth: 'Previous month',\n      nextMonth: 'Next month',\n      selectYear: 'Select year',\n      selectDate: '{0}', // Full date format\n      currentDate: 'Today, {0}',\n    },\n  },\n  noDataText: 'No data available',\n  carousel: {\n    prev: 'Previous visual',\n    next: 'Next visual',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} more',\n    today: 'Today',\n  },\n  input: {\n    clear: 'Clear {0}',\n    prependAction: '{0} prepended action',\n    appendAction: '{0} appended action',\n    otp: 'Please enter OTP character {0}',\n  },\n  fileInput: {\n    counter: '{0} files',\n    counterSize: '{0} files ({1} in total)',\n  },\n  fileUpload: {\n    title: 'Drag and drop files here',\n    divider: 'or',\n    browse: 'Browse Files',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n    title: 'Select Time',\n  },\n  pagination: {\n    ariaLabel: {\n      root: 'Pagination Navigation',\n      next: 'Next page',\n      previous: 'Previous page',\n      page: 'Go to page {0}',\n      currentPage: 'Page {0}, Current page',\n      first: 'First page',\n      last: 'Last page',\n    },\n  },\n  stepper: {\n    next: 'Next',\n    prev: 'Previous',\n  },\n  rating: {\n    ariaLabel: {\n      item: 'Rating {0} of {1}',\n    },\n  },\n  loading: 'Loading...',\n  infiniteScroll: {\n    loadMore: 'Load more',\n    empty: 'No more',\n  },\n  rules: {\n    required: 'This field is required',\n    email: 'Please enter a valid email',\n    number: 'This field can only contain numbers',\n    integer: 'This field can only contain integer values',\n    capital: 'This field can only contain uppercase letters',\n    maxLength: 'You must enter a maximum of {0} characters',\n    minLength: 'You must enter a minimum of {0} characters',\n    strictLength: 'The length of the entered field is invalid',\n    exclude: 'The {0} character is not allowed',\n    notEmpty: 'Please choose at least one value',\n    pattern: 'Invalid format',\n  },\n  hotkey: {\n    then: 'then',\n    ctrl: 'Ctrl',\n    command: 'Command',\n    space: 'Space',\n    shift: 'Shift',\n    alt: 'Alt',\n    enter: 'Enter',\n    escape: 'Escape',\n    upArrow: 'Up Arrow',\n    downArrow: 'Down Arrow',\n    leftArrow: 'Left Arrow',\n    rightArrow: 'Right Arrow',\n    backspace: 'Backspace',\n    option: 'Option',\n    plus: 'plus',\n    shortcut: 'Keyboard shortcut: {0}',\n  },\n  video: {\n    play: 'Play',\n    pause: 'Pause',\n    seek: 'Seek',\n    volume: 'Volume',\n    showVolume: 'Show volume control',\n    mute: 'Mute',\n    unmute: 'Unmute',\n    enterFullscreen: 'Full screen',\n    exitFullscreen: 'Exit full screen',\n  },\n}\n", "// Composables\nimport { useProxiedModel } from '@/composables/proxiedModel'\n\n// Utilities\nimport { ref, shallowRef, toRef, watch } from 'vue'\nimport { consoleError, consoleWarn, getObjectValueByPath } from '@/util'\n\n// Locales\nimport en from '@/locale/en'\n\n// Types\nimport type { Ref } from 'vue'\nimport type { LocaleInstance, LocaleMessages, LocaleOptions } from '@/composables/locale'\n\nconst LANG_PREFIX = '$vuetify.'\n\nconst replace = (str: string, params: unknown[]) => {\n  return str.replace(/\\{(\\d+)\\}/g, (match: string, index: string) => {\n    return String(params[Number(index)])\n  })\n}\n\nconst createTranslateFunction = (\n  current: Ref<string>,\n  fallback: Ref<string>,\n  messages: Ref<LocaleMessages>,\n) => {\n  return (key: string, ...params: unknown[]) => {\n    if (!key.startsWith(LANG_PREFIX)) {\n      return replace(key, params)\n    }\n\n    const shortKey = key.replace(LANG_PREFIX, '')\n    const currentLocale = current.value && messages.value[current.value]\n    const fallbackLocale = fallback.value && messages.value[fallback.value]\n\n    let str: string = getObjectValueByPath(currentLocale, shortKey, null)\n\n    if (!str) {\n      consoleWarn(`Translation key \"${key}\" not found in \"${current.value}\", trying fallback locale`)\n      str = getObjectValueByPath(fallbackLocale, shortKey, null)\n    }\n\n    if (!str) {\n      consoleError(`Translation key \"${key}\" not found in fallback`)\n      str = key\n    }\n\n    if (typeof str !== 'string') {\n      consoleError(`Translation key \"${key}\" has a non-string value`)\n      str = key\n    }\n\n    return replace(str, params)\n  }\n}\n\nfunction createNumberFunction (current: Ref<string>, fallback: Ref<string>) {\n  return (value: number, options?: Intl.NumberFormatOptions) => {\n    const numberFormat = new Intl.NumberFormat([current.value, fallback.value], options)\n\n    return numberFormat.format(value)\n  }\n}\n\nfunction inferDecimalSeparator (current: Ref<string>, fallback: Ref<string>) {\n  const format = createNumberFunction(current, fallback)\n  return format(0.1).includes(',') ? ',' : '.'\n}\n\nfunction useProvided <T> (props: any, prop: string, provided: Ref<T>) {\n  const internal = useProxiedModel(props, prop, props[prop] ?? provided.value)\n\n  // TODO: Remove when defaultValue works\n  internal.value = props[prop] ?? provided.value\n\n  watch(provided, v => {\n    if (props[prop] == null) {\n      internal.value = provided.value\n    }\n  })\n\n  return internal as Ref<T>\n}\n\nfunction createProvideFunction (state: { current: Ref<string>, fallback: Ref<string>, messages: Ref<LocaleMessages> }) {\n  return (props: LocaleOptions): LocaleInstance => {\n    const current = useProvided(props, 'locale', state.current)\n    const fallback = useProvided(props, 'fallback', state.fallback)\n    const messages = useProvided(props, 'messages', state.messages)\n\n    return {\n      name: 'vuetify',\n      current,\n      fallback,\n      messages,\n      decimalSeparator: toRef(() => inferDecimalSeparator(current, fallback)),\n      t: createTranslateFunction(current, fallback, messages),\n      n: createNumberFunction(current, fallback),\n      provide: createProvideFunction({ current, fallback, messages }),\n    }\n  }\n}\n\nexport function createVuetifyAdapter (options?: LocaleOptions): LocaleInstance {\n  const current = shallowRef(options?.locale ?? 'en')\n  const fallback = shallowRef(options?.fallback ?? 'en')\n  const messages = ref({ en, ...options?.messages })\n\n  return {\n    name: 'vuetify',\n    current,\n    fallback,\n    messages,\n    decimalSeparator: toRef(() => options?.decimalSeparator ?? inferDecimalSeparator(current, fallback)),\n    t: createTranslateFunction(current, fallback, messages),\n    n: createNumberFunction(current, fallback),\n    provide: createProvideFunction({ current, fallback, messages }),\n  }\n}\n", "// Utilities\nimport { computed, inject, provide, ref, toRef } from 'vue'\nimport { createVuetifyAdapter } from '@/locale/adapters/vuetify'\n\n// Types\nimport type { InjectionKey, Ref, ShallowRef } from 'vue'\n\nexport interface LocaleMessages {\n  [key: string]: LocaleMessages | string\n}\n\nexport interface LocaleOptions {\n  decimalSeparator?: string\n  messages?: LocaleMessages\n  locale?: string\n  fallback?: string\n  adapter?: LocaleInstance\n}\n\nexport interface LocaleInstance {\n  name: string\n  decimalSeparator: ShallowRef<string>\n  messages: Ref<LocaleMessages>\n  current: Ref<string>\n  fallback: Ref<string>\n  t: (key: string, ...params: unknown[]) => string\n  n: (value: number) => string\n  provide: (props: LocaleOptions) => LocaleInstance\n}\n\nexport const LocaleSymbol: InjectionKey<LocaleInstance & RtlInstance> = Symbol.for('vuetify:locale')\n\nfunction isLocaleInstance (obj: any): obj is LocaleInstance {\n  return obj.name != null\n}\n\nexport function createLocale (options?: LocaleOptions & RtlOptions) {\n  const i18n = options?.adapter && isLocaleInstance(options?.adapter) ? options?.adapter : createVuetifyAdapter(options)\n  const rtl = createRtl(i18n, options)\n\n  return { ...i18n, ...rtl }\n}\n\nexport function useLocale () {\n  const locale = inject(LocaleSymbol)\n\n  if (!locale) throw new Error('[Vuetify] Could not find injected locale instance')\n\n  return locale\n}\n\nexport function provideLocale (props: LocaleOptions & RtlProps) {\n  const locale = inject(LocaleSymbol)\n\n  if (!locale) throw new Error('[Vuetify] Could not find injected locale instance')\n\n  const i18n = locale.provide(props)\n  const rtl = provideRtl(i18n, locale.rtl, props)\n\n  const data = { ...i18n, ...rtl }\n\n  provide(LocaleSymbol, data)\n\n  return data\n}\n\n// RTL\n\nexport interface RtlOptions {\n  rtl?: Record<string, boolean>\n}\n\nexport interface RtlProps {\n  rtl?: boolean\n}\n\nexport interface RtlInstance {\n  isRtl: Ref<boolean>\n  rtl: Ref<Record<string, boolean>>\n  rtlClasses: Ref<string>\n}\n\nexport const RtlSymbol: InjectionKey<RtlInstance> = Symbol.for('vuetify:rtl')\n\nfunction genDefaults () {\n  return {\n    af: false,\n    ar: true,\n    bg: false,\n    ca: false,\n    ckb: false,\n    cs: false,\n    de: false,\n    el: false,\n    en: false,\n    es: false,\n    et: false,\n    fa: true,\n    fi: false,\n    fr: false,\n    hr: false,\n    hu: false,\n    he: true,\n    id: false,\n    it: false,\n    ja: false,\n    km: false,\n    ko: false,\n    lv: false,\n    lt: false,\n    nl: false,\n    no: false,\n    pl: false,\n    pt: false,\n    ro: false,\n    ru: false,\n    sk: false,\n    sl: false,\n    srCyrl: false,\n    srLatn: false,\n    sv: false,\n    th: false,\n    tr: false,\n    az: false,\n    uk: false,\n    vi: false,\n    zhHans: false,\n    zhHant: false,\n  }\n}\n\nexport function createRtl (i18n: LocaleInstance, options?: RtlOptions): RtlInstance {\n  const rtl = ref<Record<string, boolean>>(options?.rtl ?? genDefaults())\n  const isRtl = computed(() => rtl.value[i18n.current.value] ?? false)\n\n  return {\n    isRtl,\n    rtl,\n    rtlClasses: toRef(() => `v-locale--is-${isRtl.value ? 'rtl' : 'ltr'}`),\n  }\n}\n\nexport function provideRtl (locale: LocaleInstance, rtl: RtlInstance['rtl'], props: RtlProps): RtlInstance {\n  const isRtl = computed(() => props.rtl ?? rtl.value[locale.current.value] ?? false)\n\n  return {\n    isRtl,\n    rtl,\n    rtlClasses: toRef(() => `v-locale--is-${isRtl.value ? 'rtl' : 'ltr'}`),\n  }\n}\n\nexport function useRtl () {\n  const locale = inject(LocaleSymbol)\n\n  if (!locale) throw new Error('[Vuetify] Could not find injected rtl instance')\n\n  return { isRtl: locale.isRtl, rtlClasses: locale.rtlClasses }\n}\n", "// Utilities\nimport { consoleWarn, createRange, padStart } from '@/util'\n\n// Types\nimport type { DateAdapter } from '../DateAdapter'\n\ntype CustomDateFormat = Intl.DateTimeFormatOptions | ((date: Date, formatString: string, locale: string) => string)\n\nfunction weekInfo (locale: string): { firstDay: number, firstWeekSize: number } | null {\n  // https://simplelocalize.io/data/locales/\n  // then `new Intl.Locale(...).getWeekInfo()`\n  const code = locale.slice(-2).toUpperCase()\n  switch (true) {\n    case locale === 'GB-alt-variant': {\n      return { firstDay: 0, firstWeekSize: 4 }\n    }\n    case locale === '001': {\n      return { firstDay: 1, firstWeekSize: 1 }\n    }\n    case `AG AS BD BR BS BT BW BZ CA CO DM DO ET GT GU HK HN ID IL IN JM JP KE\n    KH KR LA MH MM MO MT MX MZ NI NP PA PE PH PK PR PY SA SG SV TH TT TW UM US\n    VE VI WS YE ZA ZW`.includes(code): {\n      return { firstDay: 0, firstWeekSize: 1 }\n    }\n    case `AI AL AM AR AU AZ BA BM BN BY CL CM CN CR CY EC GE HR KG KZ LB LK LV\n    MD ME MK MN MY NZ RO RS SI TJ TM TR UA UY UZ VN XK`.includes(code): {\n      return { firstDay: 1, firstWeekSize: 1 }\n    }\n    case `AD AN AT AX BE BG CH CZ DE DK EE ES FI FJ FO FR GB GF GP GR HU IE IS\n    IT LI LT LU MC MQ NL NO PL RE RU SE SK SM VA`.includes(code): {\n      return { firstDay: 1, firstWeekSize: 4 }\n    }\n    case `AE AF BH DJ DZ EG IQ IR JO KW LY OM QA SD SY`.includes(code): {\n      return { firstDay: 6, firstWeekSize: 1 }\n    }\n    case code === 'MV': {\n      return { firstDay: 5, firstWeekSize: 1 }\n    }\n    case code === 'PT': {\n      return { firstDay: 0, firstWeekSize: 4 }\n    }\n    default: return null\n  }\n}\n\nfunction getWeekArray (date: Date, locale: string, firstDayOfWeek?: number) {\n  const weeks = []\n  let currentWeek = []\n  const firstDayOfMonth = startOfMonth(date)\n  const lastDayOfMonth = endOfMonth(date)\n  const first = firstDayOfWeek ?? weekInfo(locale)?.firstDay ?? 0\n  const firstDayWeekIndex = (firstDayOfMonth.getDay() - first + 7) % 7\n  const lastDayWeekIndex = (lastDayOfMonth.getDay() - first + 7) % 7\n\n  for (let i = 0; i < firstDayWeekIndex; i++) {\n    const adjacentDay = new Date(firstDayOfMonth)\n    adjacentDay.setDate(adjacentDay.getDate() - (firstDayWeekIndex - i))\n    currentWeek.push(adjacentDay)\n  }\n\n  for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {\n    const day = new Date(date.getFullYear(), date.getMonth(), i)\n\n    // Add the day to the current week\n    currentWeek.push(day)\n\n    // If the current week has 7 days, add it to the weeks array and start a new week\n    if (currentWeek.length === 7) {\n      weeks.push(currentWeek)\n      currentWeek = []\n    }\n  }\n\n  for (let i = 1; i < 7 - lastDayWeekIndex; i++) {\n    const adjacentDay = new Date(lastDayOfMonth)\n    adjacentDay.setDate(adjacentDay.getDate() + i)\n    currentWeek.push(adjacentDay)\n  }\n\n  if (currentWeek.length > 0) {\n    weeks.push(currentWeek)\n  }\n\n  return weeks\n}\n\nfunction startOfWeek (date: Date, locale: string, firstDayOfWeek?: number) {\n  let day = (firstDayOfWeek ?? weekInfo(locale)?.firstDay ?? 0) % 7\n\n  // prevent infinite loop\n  if (![0, 1, 2, 3, 4, 5, 6].includes(day)) {\n    consoleWarn('Invalid firstDayOfWeek, expected discrete number in range [0-6]')\n    day = 0\n  }\n\n  const d = new Date(date)\n  while (d.getDay() !== day) {\n    d.setDate(d.getDate() - 1)\n  }\n  return d\n}\n\nfunction endOfWeek (date: Date, locale: string) {\n  const d = new Date(date)\n  const lastDay = ((weekInfo(locale)?.firstDay ?? 0) + 6) % 7\n  while (d.getDay() !== lastDay) {\n    d.setDate(d.getDate() + 1)\n  }\n  return d\n}\n\nfunction startOfMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth(), 1)\n}\n\nfunction endOfMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth() + 1, 0)\n}\n\nfunction parseLocalDate (value: string): Date {\n  const parts = value.split('-').map(Number)\n\n  // new Date() uses local time zone when passing individual date component values\n  return new Date(parts[0], parts[1] - 1, parts[2])\n}\n\nconst _YYYMMDD = /^([12]\\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\\d|3[01]))$/\n\nfunction date (value?: any): Date | null {\n  if (value == null) return new Date()\n\n  if (value instanceof Date) return value\n\n  if (typeof value === 'string') {\n    let parsed\n\n    if (_YYYMMDD.test(value)) {\n      return parseLocalDate(value)\n    } else {\n      parsed = Date.parse(value)\n    }\n\n    if (!isNaN(parsed)) return new Date(parsed)\n  }\n\n  return null\n}\n\nconst sundayJanuarySecond2000 = new Date(2000, 0, 2)\n\nfunction getWeekdays (locale: string, firstDayOfWeek?: number, weekdayFormat?: 'long' | 'short' | 'narrow') {\n  const daysFromSunday = firstDayOfWeek ?? weekInfo(locale)?.firstDay ?? 0\n\n  return createRange(7).map(i => {\n    const weekday = new Date(sundayJanuarySecond2000)\n    weekday.setDate(sundayJanuarySecond2000.getDate() + daysFromSunday + i)\n    return new Intl.DateTimeFormat(locale, { weekday: weekdayFormat ?? 'narrow' }).format(weekday)\n  })\n}\n\nfunction format (\n  value: Date,\n  formatString: string,\n  locale: string,\n  formats?: Record<string, CustomDateFormat>\n): string {\n  const newDate = date(value) ?? new Date()\n  const customFormat = formats?.[formatString]\n\n  if (typeof customFormat === 'function') {\n    return customFormat(newDate, formatString, locale)\n  }\n\n  let options: Intl.DateTimeFormatOptions = {}\n  switch (formatString) {\n    case 'fullDate':\n      options = { year: 'numeric', month: 'short', day: 'numeric' }\n      break\n    case 'fullDateWithWeekday':\n      options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }\n      break\n    case 'normalDate':\n      const day = newDate.getDate()\n      const month = new Intl.DateTimeFormat(locale, { month: 'long' }).format(newDate)\n      return `${day} ${month}`\n    case 'normalDateWithWeekday':\n      options = { weekday: 'short', day: 'numeric', month: 'short' }\n      break\n    case 'shortDate':\n      options = { month: 'short', day: 'numeric' }\n      break\n    case 'year':\n      options = { year: 'numeric' }\n      break\n    case 'month':\n      options = { month: 'long' }\n      break\n    case 'monthShort':\n      options = { month: 'short' }\n      break\n    case 'monthAndYear':\n      options = { month: 'long', year: 'numeric' }\n      break\n    case 'monthAndDate':\n      options = { month: 'long', day: 'numeric' }\n      break\n    case 'weekday':\n      options = { weekday: 'long' }\n      break\n    case 'weekdayShort':\n      options = { weekday: 'short' }\n      break\n    case 'dayOfMonth':\n      return new Intl.NumberFormat(locale).format(newDate.getDate())\n    case 'hours12h':\n      options = { hour: 'numeric', hour12: true }\n      break\n    case 'hours24h':\n      options = { hour: 'numeric', hour12: false }\n      break\n    case 'minutes':\n      options = { minute: 'numeric' }\n      break\n    case 'seconds':\n      options = { second: 'numeric' }\n      break\n    case 'fullTime':\n      options = { hour: 'numeric', minute: 'numeric' }\n      break\n    case 'fullTime12h':\n      options = { hour: 'numeric', minute: 'numeric', hour12: true }\n      break\n    case 'fullTime24h':\n      options = { hour: 'numeric', minute: 'numeric', hour12: false }\n      break\n    case 'fullDateTime':\n      options = { year: 'numeric', month: 'short', day: 'numeric', hour: 'numeric', minute: 'numeric' }\n      break\n    case 'fullDateTime12h':\n      options = { year: 'numeric', month: 'short', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: true }\n      break\n    case 'fullDateTime24h':\n      options = { year: 'numeric', month: 'short', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: false }\n      break\n    case 'keyboardDate':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit' }\n      break\n    case 'keyboardDateTime':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: 'numeric', minute: 'numeric' }\n      return new Intl.DateTimeFormat(locale, options).format(newDate).replace(/, /g, ' ')\n    case 'keyboardDateTime12h':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: 'numeric', minute: 'numeric', hour12: true }\n      return new Intl.DateTimeFormat(locale, options).format(newDate).replace(/, /g, ' ')\n    case 'keyboardDateTime24h':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: 'numeric', minute: 'numeric', hour12: false }\n      return new Intl.DateTimeFormat(locale, options).format(newDate).replace(/, /g, ' ')\n    default:\n      options = customFormat ?? { timeZone: 'UTC', timeZoneName: 'short' }\n  }\n\n  return new Intl.DateTimeFormat(locale, options).format(newDate)\n}\n\nfunction toISO (adapter: DateAdapter<any>, value: Date) {\n  const date = adapter.toJsDate(value)\n  const year = date.getFullYear()\n  const month = padStart(String(date.getMonth() + 1), 2, '0')\n  const day = padStart(String(date.getDate()), 2, '0')\n\n  return `${year}-${month}-${day}`\n}\n\nfunction parseISO (value: string) {\n  const [year, month, day] = value.split('-').map(Number)\n\n  return new Date(year, month - 1, day)\n}\n\nfunction addMinutes (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setMinutes(d.getMinutes() + amount)\n  return d\n}\n\nfunction addHours (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setHours(d.getHours() + amount)\n  return d\n}\n\nfunction addDays (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setDate(d.getDate() + amount)\n  return d\n}\n\nfunction addWeeks (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setDate(d.getDate() + (amount * 7))\n  return d\n}\n\nfunction addMonths (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setDate(1)\n  d.setMonth(d.getMonth() + amount)\n  return d\n}\n\nfunction getYear (date: Date) {\n  return date.getFullYear()\n}\n\nfunction getMonth (date: Date) {\n  return date.getMonth()\n}\n\nfunction getWeek (date: Date, locale: string, firstDayOfWeek?: number, firstWeekMinSize?: number) {\n  const weekInfoFromLocale = weekInfo(locale)\n  const weekStart = firstDayOfWeek ?? weekInfoFromLocale?.firstDay ?? 0\n  const minWeekSize = firstWeekMinSize ?? weekInfoFromLocale?.firstWeekSize ?? 1\n  function firstWeekSize (year: number) {\n    const yearStart = new Date(year, 0, 1)\n    return 7 - getDiff(yearStart, startOfWeek(yearStart, locale, weekStart), 'days')\n  }\n\n  let year = getYear(date)\n  const currentWeekEnd = addDays(startOfWeek(date, locale, weekStart), 6)\n  if (year < getYear(currentWeekEnd) && firstWeekSize(year + 1) >= minWeekSize) {\n    year++\n  }\n\n  const yearStart = new Date(year, 0, 1)\n  const size = firstWeekSize(year)\n  const d1w1 = size >= minWeekSize\n    ? addDays(yearStart, size - 7)\n    : addDays(yearStart, size)\n\n  return 1 + getDiff(endOfDay(date), startOfDay(d1w1), 'weeks')\n}\n\nfunction getDate (date: Date) {\n  return date.getDate()\n}\n\nfunction getNextMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth() + 1, 1)\n}\n\nfunction getPreviousMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth() - 1, 1)\n}\n\nfunction getHours (date: Date) {\n  return date.getHours()\n}\n\nfunction getMinutes (date: Date) {\n  return date.getMinutes()\n}\n\nfunction startOfYear (date: Date) {\n  return new Date(date.getFullYear(), 0, 1)\n}\nfunction endOfYear (date: Date) {\n  return new Date(date.getFullYear(), 11, 31)\n}\n\nfunction isWithinRange (date: Date, range: [Date, Date]) {\n  return isAfter(date, range[0]) && isBefore(date, range[1])\n}\n\nfunction isValid (date: any) {\n  const d = new Date(date)\n\n  return d instanceof Date && !isNaN(d.getTime())\n}\n\nfunction isAfter (date: Date, comparing: Date) {\n  return date.getTime() > comparing.getTime()\n}\n\nfunction isAfterDay (date: Date, comparing: Date): boolean {\n  return isAfter(startOfDay(date), startOfDay(comparing))\n}\n\nfunction isBefore (date: Date, comparing: Date) {\n  return date.getTime() < comparing.getTime()\n}\n\nfunction isEqual (date: Date, comparing: Date) {\n  return date.getTime() === comparing.getTime()\n}\n\nfunction isSameDay (date: Date, comparing: Date) {\n  return date.getDate() === comparing.getDate() &&\n    date.getMonth() === comparing.getMonth() &&\n    date.getFullYear() === comparing.getFullYear()\n}\n\nfunction isSameMonth (date: Date, comparing: Date) {\n  return date.getMonth() === comparing.getMonth() &&\n    date.getFullYear() === comparing.getFullYear()\n}\n\nfunction isSameYear (date: Date, comparing: Date) {\n  return date.getFullYear() === comparing.getFullYear()\n}\n\nfunction getDiff (date: Date, comparing: Date | string, unit?: string) {\n  const d = new Date(date)\n  const c = new Date(comparing)\n\n  switch (unit) {\n    case 'years':\n      return d.getFullYear() - c.getFullYear()\n    case 'quarters':\n      return Math.floor((d.getMonth() - c.getMonth() + (d.getFullYear() - c.getFullYear()) * 12) / 4)\n    case 'months':\n      return d.getMonth() - c.getMonth() + (d.getFullYear() - c.getFullYear()) * 12\n    case 'weeks':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60 * 60 * 24 * 7))\n    case 'days':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60 * 60 * 24))\n    case 'hours':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60 * 60))\n    case 'minutes':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60))\n    case 'seconds':\n      return Math.floor((d.getTime() - c.getTime()) / 1000)\n    default: {\n      return d.getTime() - c.getTime()\n    }\n  }\n}\n\nfunction setHours (date: Date, count: number) {\n  const d = new Date(date)\n  d.setHours(count)\n  return d\n}\n\nfunction setMinutes (date: Date, count: number) {\n  const d = new Date(date)\n  d.setMinutes(count)\n  return d\n}\n\nfunction setMonth (date: Date, count: number) {\n  const d = new Date(date)\n  d.setMonth(count)\n  return d\n}\n\nfunction setDate (date: Date, day: number) {\n  const d = new Date(date)\n  d.setDate(day)\n  return d\n}\n\nfunction setYear (date: Date, year: number) {\n  const d = new Date(date)\n  d.setFullYear(year)\n  return d\n}\n\nfunction startOfDay (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0)\n}\n\nfunction endOfDay (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999)\n}\n\nexport class VuetifyDateAdapter implements DateAdapter<Date> {\n  locale: string\n  formats?: Record<string, CustomDateFormat>\n\n  constructor (options: { locale: string, formats?: Record<string, CustomDateFormat> }) {\n    this.locale = options.locale\n    this.formats = options.formats\n  }\n\n  date (value?: any) {\n    return date(value)\n  }\n\n  toJsDate (date: Date) {\n    return date\n  }\n\n  toISO (date: Date): string {\n    return toISO(this, date)\n  }\n\n  parseISO (date: string) {\n    return parseISO(date)\n  }\n\n  addMinutes (date: Date, amount: number) {\n    return addMinutes(date, amount)\n  }\n\n  addHours (date: Date, amount: number) {\n    return addHours(date, amount)\n  }\n\n  addDays (date: Date, amount: number) {\n    return addDays(date, amount)\n  }\n\n  addWeeks (date: Date, amount: number) {\n    return addWeeks(date, amount)\n  }\n\n  addMonths (date: Date, amount: number) {\n    return addMonths(date, amount)\n  }\n\n  getWeekArray (date: Date, firstDayOfWeek?: number | string) {\n    const firstDay = firstDayOfWeek !== undefined ? Number(firstDayOfWeek) : undefined\n    return getWeekArray(date, this.locale, firstDay)\n  }\n\n  startOfWeek (date: Date, firstDayOfWeek?: number | string): Date {\n    const firstDay = firstDayOfWeek !== undefined ? Number(firstDayOfWeek) : undefined\n    return startOfWeek(date, this.locale, firstDay)\n  }\n\n  endOfWeek (date: Date): Date {\n    return endOfWeek(date, this.locale)\n  }\n\n  startOfMonth (date: Date) {\n    return startOfMonth(date)\n  }\n\n  endOfMonth (date: Date) {\n    return endOfMonth(date)\n  }\n\n  format (date: Date, formatString: string) {\n    return format(date, formatString, this.locale, this.formats)\n  }\n\n  isEqual (date: Date, comparing: Date) {\n    return isEqual(date, comparing)\n  }\n\n  isValid (date: any) {\n    return isValid(date)\n  }\n\n  isWithinRange (date: Date, range: [Date, Date]) {\n    return isWithinRange(date, range)\n  }\n\n  isAfter (date: Date, comparing: Date) {\n    return isAfter(date, comparing)\n  }\n\n  isAfterDay (date: Date, comparing: Date) {\n    return isAfterDay(date, comparing)\n  }\n\n  isBefore (date: Date, comparing: Date) {\n    return !isAfter(date, comparing) && !isEqual(date, comparing)\n  }\n\n  isSameDay (date: Date, comparing: Date) {\n    return isSameDay(date, comparing)\n  }\n\n  isSameMonth (date: Date, comparing: Date) {\n    return isSameMonth(date, comparing)\n  }\n\n  isSameYear (date: Date, comparing: Date) {\n    return isSameYear(date, comparing)\n  }\n\n  setMinutes (date: Date, count: number) {\n    return setMinutes(date, count)\n  }\n\n  setHours (date: Date, count: number) {\n    return setHours(date, count)\n  }\n\n  setMonth (date: Date, count: number) {\n    return setMonth(date, count)\n  }\n\n  setDate (date: Date, day: number): Date {\n    return setDate(date, day)\n  }\n\n  setYear (date: Date, year: number) {\n    return setYear(date, year)\n  }\n\n  getDiff (date: Date, comparing: Date | string, unit?: string) {\n    return getDiff(date, comparing, unit)\n  }\n\n  getWeekdays (firstDayOfWeek?: number | string, weekdayFormat?: 'long' | 'short' | 'narrow') {\n    const firstDay = firstDayOfWeek !== undefined ? Number(firstDayOfWeek) : undefined\n    return getWeekdays(this.locale, firstDay, weekdayFormat)\n  }\n\n  getYear (date: Date) {\n    return getYear(date)\n  }\n\n  getMonth (date: Date) {\n    return getMonth(date)\n  }\n\n  getWeek (date: Date, firstDayOfWeek?: number | string, firstWeekMinSize?: number) {\n    const firstDay = firstDayOfWeek !== undefined ? Number(firstDayOfWeek) : undefined\n    return getWeek(date, this.locale, firstDay, firstWeekMinSize)\n  }\n\n  getDate (date: Date) {\n    return getDate(date)\n  }\n\n  getNextMonth (date: Date) {\n    return getNextMonth(date)\n  }\n\n  getPreviousMonth (date: Date) {\n    return getPreviousMonth(date)\n  }\n\n  getHours (date: Date) {\n    return getHours(date)\n  }\n\n  getMinutes (date: Date) {\n    return getMinutes(date)\n  }\n\n  startOfDay (date: Date) {\n    return startOfDay(date)\n  }\n\n  endOfDay (date: Date) {\n    return endOfDay(date)\n  }\n\n  startOfYear (date: Date) {\n    return startOfYear(date)\n  }\n\n  endOfYear (date: Date) {\n    return endOfYear(date)\n  }\n}\n", "// Composables\nimport { useLocale } from '@/composables/locale'\n\n// Utilities\nimport { inject, reactive, watch } from 'vue'\nimport { mergeDeep } from '@/util'\n\n// Types\nimport type { InjectionKey } from 'vue'\nimport type { DateAdapter } from './DateAdapter'\nimport type { LocaleInstance } from '@/composables/locale'\n\n// Adapters\nimport { VuetifyDateAdapter } from './adapters/vuetify'\n\nexport interface DateInstance extends DateModule.InternalAdapter {\n  locale?: any\n}\n\n/** Supports module augmentation to specify date adapter types */\nexport namespace DateModule {\n  interface Adapter {}\n\n  export type InternalAdapter = {} extends Adapter ? DateAdapter : Adapter\n}\n\nexport type InternalDateOptions = {\n  adapter: (new (options: { locale: any, formats?: any }) => DateInstance) | DateInstance\n  formats?: Record<string, any>\n  locale: Record<string, any>\n}\n\nexport type DateOptions = Partial<InternalDateOptions>\n\nexport const DateOptionsSymbol: InjectionKey<InternalDateOptions> = Symbol.for('vuetify:date-options')\nexport const DateAdapterSymbol: InjectionKey<DateInstance> = Symbol.for('vuetify:date-adapter')\n\nexport function createDate (options: DateOptions | undefined, locale: LocaleInstance) {\n  const _options = mergeDeep({\n    adapter: VuetifyDateAdapter,\n    locale: {\n      af: 'af-ZA',\n      // ar: '', # not the same value for all variants\n      bg: 'bg-BG',\n      ca: 'ca-ES',\n      ckb: '',\n      cs: 'cs-CZ',\n      de: 'de-DE',\n      el: 'el-GR',\n      en: 'en-US',\n      // es: '', # not the same value for all variants\n      et: 'et-EE',\n      fa: 'fa-IR',\n      fi: 'fi-FI',\n      // fr: '', #not the same value for all variants\n      hr: 'hr-HR',\n      hu: 'hu-HU',\n      he: 'he-IL',\n      id: 'id-ID',\n      it: 'it-IT',\n      ja: 'ja-JP',\n      ko: 'ko-KR',\n      lv: 'lv-LV',\n      lt: 'lt-LT',\n      nl: 'nl-NL',\n      no: 'no-NO',\n      pl: 'pl-PL',\n      pt: 'pt-PT',\n      ro: 'ro-RO',\n      ru: 'ru-RU',\n      sk: 'sk-SK',\n      sl: 'sl-SI',\n      srCyrl: 'sr-SP',\n      srLatn: 'sr-SP',\n      sv: 'sv-SE',\n      th: 'th-TH',\n      tr: 'tr-TR',\n      az: 'az-AZ',\n      uk: 'uk-UA',\n      vi: 'vi-VN',\n      zhHans: 'zh-CN',\n      zhHant: 'zh-TW',\n    },\n  }, options) as InternalDateOptions\n\n  return {\n    options: _options,\n    instance: createInstance(_options, locale),\n  }\n}\n\nexport function createDateRange (adapter: DateInstance, start: unknown, stop?: unknown) {\n  const diff = adapter.getDiff(\n    adapter.endOfDay(stop ?? start),\n    adapter.startOfDay(start),\n    'days'\n  )\n  const datesInRange = [start]\n\n  for (let i = 1; i < diff; i++) {\n    const nextDate = adapter.addDays(start, i)\n    datesInRange.push(nextDate)\n  }\n\n  if (stop) {\n    datesInRange.push(adapter.endOfDay(stop))\n  }\n\n  return datesInRange\n}\n\nfunction createInstance (options: InternalDateOptions, locale: LocaleInstance) {\n  const instance = reactive(\n    typeof options.adapter === 'function'\n      // eslint-disable-next-line new-cap\n      ? new options.adapter({\n        locale: options.locale[locale.current.value] ?? locale.current.value,\n        formats: options.formats,\n      })\n      : options.adapter\n  )\n\n  watch(locale.current, value => {\n    instance.locale = options.locale[value] ?? value ?? instance.locale\n  })\n\n  return instance\n}\n\nexport function useDate (): DateInstance {\n  const options = inject(DateOptionsSymbol)\n\n  if (!options) throw new Error('[Vuetify] Could not find injected date options')\n\n  const locale = useLocale()\n\n  return createInstance(options, locale)\n}\n", "// Utilities\nimport { computed, inject, onScopeDispose, reactive, shallowRef, toRef, toRefs, watchEffect } from 'vue'\nimport { getCurrentInstanceName, mergeDeep, propsFactory } from '@/util'\nimport { IN_BROWSER, SUPPORTS_TOUCH } from '@/util/globals'\n\n// Types\nimport type { InjectionKey, PropType, Ref } from 'vue'\n\nexport const breakpoints = ['sm', 'md', 'lg', 'xl', 'xxl'] as const // no xs\n\nexport type Breakpoint = typeof breakpoints[number]\n\nexport type DisplayBreakpoint = 'xs' | Breakpoint\n\nexport type DisplayThresholds = {\n  [key in DisplayBreakpoint]: number\n}\n\nexport interface DisplayProps {\n  mobile?: boolean | null\n  mobileBreakpoint?: number | DisplayBreakpoint\n}\n\nexport interface DisplayOptions {\n  mobileBreakpoint?: number | DisplayBreakpoint\n  thresholds?: Partial<DisplayThresholds>\n}\n\nexport interface InternalDisplayOptions {\n  mobileBreakpoint: number | DisplayBreakpoint\n  thresholds: DisplayThresholds\n}\n\nexport type SSROptions = boolean | {\n  clientWidth: number\n  clientHeight?: number\n}\n\nexport interface DisplayPlatform {\n  android: boolean\n  ios: boolean\n  cordova: boolean\n  electron: boolean\n  chrome: boolean\n  edge: boolean\n  firefox: boolean\n  opera: boolean\n  win: boolean\n  mac: boolean\n  linux: boolean\n  touch: boolean\n  ssr: boolean\n}\n\nexport interface DisplayInstance {\n  xs: Ref<boolean>\n  sm: Ref<boolean>\n  md: Ref<boolean>\n  lg: Ref<boolean>\n  xl: Ref<boolean>\n  xxl: Ref<boolean>\n  smAndUp: Ref<boolean>\n  mdAndUp: Ref<boolean>\n  lgAndUp: Ref<boolean>\n  xlAndUp: Ref<boolean>\n  smAndDown: Ref<boolean>\n  mdAndDown: Ref<boolean>\n  lgAndDown: Ref<boolean>\n  xlAndDown: Ref<boolean>\n  name: Ref<DisplayBreakpoint>\n  height: Ref<number>\n  width: Ref<number>\n  mobile: Ref<boolean>\n  mobileBreakpoint: Ref<number | DisplayBreakpoint>\n  platform: Ref<DisplayPlatform>\n  thresholds: Ref<DisplayThresholds>\n\n  /** @internal */\n  ssr: boolean\n\n  update (): void\n}\n\nexport const DisplaySymbol: InjectionKey<DisplayInstance> = Symbol.for('vuetify:display')\n\nconst defaultDisplayOptions: DisplayOptions = {\n  mobileBreakpoint: 'lg',\n  thresholds: {\n    xs: 0,\n    sm: 600,\n    md: 960,\n    lg: 1280,\n    xl: 1920,\n    xxl: 2560,\n  },\n}\n\nconst parseDisplayOptions = (options: DisplayOptions = defaultDisplayOptions) => {\n  return mergeDeep(defaultDisplayOptions, options) as InternalDisplayOptions\n}\n\nfunction getClientWidth (ssr?: SSROptions) {\n  return IN_BROWSER && !ssr\n    ? window.innerWidth\n    : (typeof ssr === 'object' && ssr.clientWidth) || 0\n}\n\nfunction getClientHeight (ssr?: SSROptions) {\n  return IN_BROWSER && !ssr\n    ? window.innerHeight\n    : (typeof ssr === 'object' && ssr.clientHeight) || 0\n}\n\nfunction getPlatform (ssr?: SSROptions): DisplayPlatform {\n  const userAgent = IN_BROWSER && !ssr\n    ? window.navigator.userAgent\n    : 'ssr'\n\n  function match (regexp: RegExp) {\n    return Boolean(userAgent.match(regexp))\n  }\n\n  const android = match(/android/i)\n  const ios = match(/iphone|ipad|ipod/i)\n  const cordova = match(/cordova/i)\n  const electron = match(/electron/i)\n  const chrome = match(/chrome/i)\n  const edge = match(/edge/i)\n  const firefox = match(/firefox/i)\n  const opera = match(/opera/i)\n  const win = match(/win/i)\n  const mac = match(/mac/i)\n  const linux = match(/linux/i)\n\n  return {\n    android,\n    ios,\n    cordova,\n    electron,\n    chrome,\n    edge,\n    firefox,\n    opera,\n    win,\n    mac,\n    linux,\n    touch: SUPPORTS_TOUCH,\n    ssr: userAgent === 'ssr',\n  }\n}\n\nexport function createDisplay (options?: DisplayOptions, ssr?: SSROptions): DisplayInstance {\n  const { thresholds, mobileBreakpoint } = parseDisplayOptions(options)\n\n  const height = shallowRef(getClientHeight(ssr))\n  const platform = shallowRef(getPlatform(ssr))\n  const state = reactive({} as DisplayInstance)\n  const width = shallowRef(getClientWidth(ssr))\n\n  function updateSize () {\n    height.value = getClientHeight()\n    width.value = getClientWidth()\n  }\n  function update () {\n    updateSize()\n    platform.value = getPlatform()\n  }\n\n  // eslint-disable-next-line max-statements\n  watchEffect(() => {\n    const xs = width.value < thresholds.sm\n    const sm = width.value < thresholds.md && !xs\n    const md = width.value < thresholds.lg && !(sm || xs)\n    const lg = width.value < thresholds.xl && !(md || sm || xs)\n    const xl = width.value < thresholds.xxl && !(lg || md || sm || xs)\n    const xxl = width.value >= thresholds.xxl\n    const name =\n      xs ? 'xs'\n      : sm ? 'sm'\n      : md ? 'md'\n      : lg ? 'lg'\n      : xl ? 'xl'\n      : 'xxl'\n    const breakpointValue = typeof mobileBreakpoint === 'number' ? mobileBreakpoint : thresholds[mobileBreakpoint]\n    const mobile = width.value < breakpointValue\n\n    state.xs = xs\n    state.sm = sm\n    state.md = md\n    state.lg = lg\n    state.xl = xl\n    state.xxl = xxl\n    state.smAndUp = !xs\n    state.mdAndUp = !(xs || sm)\n    state.lgAndUp = !(xs || sm || md)\n    state.xlAndUp = !(xs || sm || md || lg)\n    state.smAndDown = !(md || lg || xl || xxl)\n    state.mdAndDown = !(lg || xl || xxl)\n    state.lgAndDown = !(xl || xxl)\n    state.xlAndDown = !xxl\n    state.name = name\n    state.height = height.value\n    state.width = width.value\n    state.mobile = mobile\n    state.mobileBreakpoint = mobileBreakpoint\n    state.platform = platform.value\n    state.thresholds = thresholds\n  })\n\n  if (IN_BROWSER) {\n    window.addEventListener('resize', updateSize, { passive: true })\n\n    onScopeDispose(() => {\n      window.removeEventListener('resize', updateSize)\n    }, true)\n  }\n\n  return { ...toRefs(state), update, ssr: !!ssr }\n}\n\nexport const makeDisplayProps = propsFactory({\n  mobile: {\n    type: Boolean as PropType<boolean | null>,\n    default: false,\n  },\n  mobileBreakpoint: [Number, String] as PropType<number | DisplayBreakpoint>,\n}, 'display')\n\nexport function useDisplay (\n  props: DisplayProps = { mobile: null },\n  name = getCurrentInstanceName(),\n) {\n  const display = inject(DisplaySymbol)\n\n  if (!display) throw new Error('Could not find Vuetify display injection')\n\n  const mobile = computed(() => {\n    if (props.mobile) {\n      return true\n    } else if (typeof props.mobileBreakpoint === 'number') {\n      return display.width.value < props.mobileBreakpoint\n    } else if (props.mobileBreakpoint) {\n      return display.width.value < display.thresholds.value[props.mobileBreakpoint]\n    } else if (props.mobile === null) {\n      return display.mobile.value\n    } else {\n      return false\n    }\n  })\n\n  const displayClasses = toRef(() => {\n    if (!name) return {}\n\n    return { [`${name}--mobile`]: mobile.value }\n  })\n\n  return { ...display, displayClasses, mobile }\n}\n", "// Utilities\nimport { inject, toRef } from 'vue'\nimport { useRtl } from './locale'\nimport { clamp, consoleWarn, easingPatterns, mergeDeep, refElement } from '@/util'\n\n// Types\nimport type { ComponentPublicInstance, InjectionKey, Ref } from 'vue'\nimport type { LocaleInstance, RtlInstance } from './locale'\nimport type { EasingFunction } from '@/util'\n\nexport interface GoToInstance {\n  rtl: Ref<boolean>\n  options: InternalGoToOptions\n}\n\nexport interface InternalGoToOptions {\n  container: ComponentPublicInstance | HTMLElement | string\n  duration: number\n  layout: boolean\n  offset: number\n  easing: string | EasingFunction\n  patterns: Record<string, EasingFunction>\n}\n\nexport type GoToOptions = Partial<InternalGoToOptions>\n\nexport const GoToSymbol: InjectionKey<GoToInstance> = Symbol.for('vuetify:goto')\n\nfunction genDefaults () {\n  return {\n    container: undefined,\n    duration: 300,\n    layout: false,\n    offset: 0,\n    easing: 'easeInOutCubic' satisfies keyof typeof easingPatterns,\n    patterns: easingPatterns,\n  }\n}\n\nfunction getContainer (el?: ComponentPublicInstance | HTMLElement | string) {\n  return getTarget(el) ?? (document.scrollingElement || document.body) as HTMLElement\n}\n\nfunction getTarget (el: ComponentPublicInstance | HTMLElement | string | undefined) {\n  return (typeof el === 'string') ? document.querySelector<HTMLElement>(el) : refElement(el)\n}\n\nfunction getOffset (target: any, horizontal?: boolean, rtl?: boolean): number {\n  if (typeof target === 'number') return horizontal && rtl ? -target : target\n\n  let el = getTarget(target)\n  let totalOffset = 0\n  while (el) {\n    totalOffset += horizontal ? el.offsetLeft : el.offsetTop\n    el = el.offsetParent as HTMLElement\n  }\n\n  return totalOffset\n}\n\nexport function createGoTo (\n  options: GoToOptions| undefined,\n  locale: LocaleInstance & RtlInstance\n): GoToInstance {\n  return {\n    rtl: locale.isRtl,\n    options: mergeDeep(genDefaults(), options) as InternalGoToOptions,\n  }\n}\n\nexport async function scrollTo (\n  _target: ComponentPublicInstance | HTMLElement | number | string,\n  _options: GoToOptions,\n  horizontal?: boolean,\n  goTo?: GoToInstance,\n) {\n  const property = horizontal ? 'scrollLeft' : 'scrollTop'\n  const options = mergeDeep(goTo?.options ?? genDefaults(), _options)\n  const rtl = goTo?.rtl.value\n  const target = (typeof _target === 'number' ? _target : getTarget(_target)) ?? 0\n  const container = options.container === 'parent' && target instanceof HTMLElement\n    ? target.parentElement!\n    : getContainer(options.container)\n  const ease = typeof options.easing === 'function' ? options.easing : options.patterns[options.easing]\n\n  if (!ease) throw new TypeError(`Easing function \"${options.easing}\" not found.`)\n\n  let targetLocation: number\n  if (typeof target === 'number') {\n    targetLocation = getOffset(target, horizontal, rtl)\n  } else {\n    targetLocation = getOffset(target, horizontal, rtl) - getOffset(container, horizontal, rtl)\n\n    if (options.layout) {\n      const styles = window.getComputedStyle(target)\n      const layoutOffset = styles.getPropertyValue('--v-layout-top')\n\n      if (layoutOffset) targetLocation -= parseInt(layoutOffset, 10)\n    }\n  }\n\n  targetLocation += options.offset\n  targetLocation = clampTarget(container, targetLocation, !!rtl, !!horizontal)\n\n  const startLocation = container[property] ?? 0\n\n  if (targetLocation === startLocation) return Promise.resolve(targetLocation)\n\n  const startTime = performance.now()\n\n  return new Promise(resolve => requestAnimationFrame(function step (currentTime: number) {\n    const timeElapsed = currentTime - startTime\n    const progress = timeElapsed / options.duration\n    const location = Math.floor(\n      startLocation +\n      (targetLocation - startLocation) *\n      ease(clamp(progress, 0, 1))\n    )\n\n    container[property] = location\n\n    // Allow for some jitter if target time has elapsed\n    if (progress >= 1 && Math.abs(location - container[property]) < 10) {\n      return resolve(targetLocation)\n    } else if (progress > 2) {\n      // The target might not be reachable\n      consoleWarn('Scroll target is not reachable')\n      return resolve(container[property])\n    }\n\n    requestAnimationFrame(step)\n  }))\n}\n\nexport function useGoTo (_options: GoToOptions = {}) {\n  const goToInstance = inject(GoToSymbol)\n  const { isRtl } = useRtl()\n\n  if (!goToInstance) throw new Error('[Vuetify] Could not find injected goto instance')\n\n  const goTo = {\n    ...goToInstance,\n    // can be set via VLocaleProvider\n    rtl: toRef(() => goToInstance.rtl.value || isRtl.value),\n  }\n\n  async function go (\n    target: ComponentPublicInstance | HTMLElement | string | number,\n    options?: Partial<GoToOptions>,\n  ) {\n    return scrollTo(target, mergeDeep(_options, options), false, goTo)\n  }\n\n  go.horizontal = async (\n    target: ComponentPublicInstance | HTMLElement | string | number,\n    options?: Partial<GoToOptions>,\n  ) => {\n    return scrollTo(target, mergeDeep(_options, options), true, goTo)\n  }\n\n  return go\n}\n\n/**\n * Clamp target value to achieve a smooth scroll animation\n * when the value goes outside the scroll container size\n */\nfunction clampTarget (\n  container: HTMLElement,\n  value: number,\n  rtl: boolean,\n  horizontal: boolean,\n) {\n  const { scrollWidth, scrollHeight } = container\n  const [containerWidth, containerHeight] = container === document.scrollingElement\n    ? [window.innerWidth, window.innerHeight]\n    : [container.offsetWidth, container.offsetHeight]\n\n  let min: number\n  let max: number\n\n  if (horizontal) {\n    if (rtl) {\n      min = -(scrollWidth - containerWidth)\n      max = 0\n    } else {\n      min = 0\n      max = scrollWidth - containerWidth\n    }\n  } else {\n    min = 0\n    max = scrollHeight + -containerHeight\n  }\n\n  return clamp(value, min, max)\n}\n", "// Composables\nimport { VClassIcon } from '@/composables/icons'\n\n// Utilities\nimport { h } from 'vue'\n\n// Types\nimport type { IconAliases, IconSet } from '@/composables/icons'\n\nconst aliases: IconAliases = {\n  collapse: 'mdi-chevron-up',\n  complete: 'mdi-check',\n  cancel: 'mdi-close-circle',\n  close: 'mdi-close',\n  delete: 'mdi-close-circle', // delete (e.g. v-chip close)\n  clear: 'mdi-close-circle',\n  success: 'mdi-check-circle',\n  info: 'mdi-information',\n  warning: 'mdi-alert-circle',\n  error: 'mdi-close-circle',\n  prev: 'mdi-chevron-left',\n  next: 'mdi-chevron-right',\n  checkboxOn: 'mdi-checkbox-marked',\n  checkboxOff: 'mdi-checkbox-blank-outline',\n  checkboxIndeterminate: 'mdi-minus-box',\n  delimiter: 'mdi-circle', // for carousel\n  sortAsc: 'mdi-arrow-up',\n  sortDesc: 'mdi-arrow-down',\n  expand: 'mdi-chevron-down',\n  menu: 'mdi-menu',\n  subgroup: 'mdi-menu-down',\n  dropdown: 'mdi-menu-down',\n  radioOn: 'mdi-radiobox-marked',\n  radioOff: 'mdi-radiobox-blank',\n  edit: 'mdi-pencil',\n  ratingEmpty: 'mdi-star-outline',\n  ratingFull: 'mdi-star',\n  ratingHalf: 'mdi-star-half-full',\n  loading: 'mdi-cached',\n  first: 'mdi-page-first',\n  last: 'mdi-page-last',\n  unfold: 'mdi-unfold-more-horizontal',\n  file: 'mdi-paperclip',\n  plus: 'mdi-plus',\n  minus: 'mdi-minus',\n  calendar: 'mdi-calendar',\n  treeviewCollapse: 'mdi-menu-down',\n  treeviewExpand: 'mdi-menu-right',\n  eyeDropper: 'mdi-eyedropper',\n  upload: 'mdi-cloud-upload',\n  color: 'mdi-palette',\n  command: 'mdi-apple-keyboard-command',\n  ctrl: 'mdi-apple-keyboard-control',\n  space: 'mdi-keyboard-space',\n  shift: 'mdi-apple-keyboard-shift',\n  alt: 'mdi-apple-keyboard-option',\n  enter: 'mdi-keyboard-return',\n  arrowup: 'mdi-arrow-up',\n  arrowdown: 'mdi-arrow-down',\n  arrowleft: 'mdi-arrow-left',\n  arrowright: 'mdi-arrow-right',\n  backspace: 'mdi-backspace',\n  play: 'mdi-play',\n  pause: 'mdi-pause',\n  fullscreen: 'mdi-fullscreen',\n  fullscreenExit: 'mdi-fullscreen-exit',\n  volumeHigh: 'mdi-volume-high',\n  volumeMedium: 'mdi-volume-medium',\n  volumeLow: 'mdi-volume-low',\n  volumeOff: 'mdi-volume-variant-off',\n}\n\nconst mdi: IconSet = {\n  // Not using mergeProps here, functional components merge props by default (?)\n  component: (props: any) => h(VClassIcon, { ...props, class: 'mdi' }),\n}\n\nexport { aliases, mdi }\n", "// Icons\nimport { aliases, mdi } from '@/iconsets/mdi'\n\n// Utilities\nimport { computed, inject, toValue } from 'vue'\nimport { consoleWarn, defineComponent, genericComponent, mergeDeep, propsFactory } from '@/util'\n\n// Types\nimport type { InjectionKey, MaybeRefOrGetter, PropType } from 'vue'\nimport type { JSXComponent } from '@/util'\n\nexport type IconValue =\n  | string\n  | (string | [path: string, opacity: number])[]\n  | JSXComponent\nexport const IconValue = [String, Function, Object, Array] as PropType<IconValue>\n\nexport interface IconAliases {\n  [name: string]: IconValue\n  collapse: IconValue\n  complete: IconValue\n  cancel: IconValue\n  close: IconValue\n  delete: IconValue\n  clear: IconValue\n  success: IconValue\n  info: IconValue\n  warning: IconValue\n  error: IconValue\n  prev: IconValue\n  next: IconValue\n  checkboxOn: IconValue\n  checkboxOff: IconValue\n  checkboxIndeterminate: IconValue\n  delimiter: IconValue\n  sortAsc: IconValue\n  sortDesc: IconValue\n  expand: IconValue\n  menu: IconValue\n  subgroup: IconValue\n  dropdown: IconValue\n  radioOn: IconValue\n  radioOff: IconValue\n  edit: IconValue\n  ratingEmpty: IconValue\n  ratingFull: IconValue\n  ratingHalf: IconValue\n  loading: IconValue\n  first: IconValue\n  last: IconValue\n  unfold: IconValue\n  file: IconValue\n  plus: IconValue\n  minus: IconValue\n  calendar: IconValue\n  treeviewCollapse: IconValue\n  treeviewExpand: IconValue\n  eyeDropper: IconValue\n  upload: IconValue\n  color: IconValue\n  // Font Awesome does not have most of these icons!\n  command: IconValue\n  ctrl: IconValue\n  space: IconValue\n  shift: IconValue\n  alt: IconValue\n  enter: IconValue\n  arrowup: IconValue\n  arrowdown: IconValue\n  arrowleft: IconValue\n  arrowright: IconValue\n  backspace: IconValue\n}\n\nexport interface IconProps {\n  tag: string | JSXComponent\n  icon?: IconValue\n  disabled?: boolean\n}\n\ntype IconComponent = JSXComponent<IconProps>\n\nexport interface IconSet {\n  component: IconComponent\n}\n\nexport type InternalIconOptions = {\n  defaultSet: string\n  aliases: Partial<IconAliases>\n  sets: Record<string, IconSet>\n}\n\nexport type IconOptions = Partial<InternalIconOptions>\n\ntype IconInstance = {\n  component: IconComponent\n  icon?: IconValue\n}\n\nexport const IconSymbol: InjectionKey<InternalIconOptions> = Symbol.for('vuetify:icons')\n\nexport const makeIconProps = propsFactory({\n  icon: {\n    type: IconValue,\n  },\n  // Could not remove this and use makeTagProps, types complained because it is not required\n  tag: {\n    type: [String, Object, Function] as PropType<string | JSXComponent>,\n    required: true,\n  },\n}, 'icon')\n\nexport const VComponentIcon = genericComponent()({\n  name: 'VComponentIcon',\n\n  props: makeIconProps(),\n\n  setup (props, { slots }) {\n    return () => {\n      const Icon = props.icon as JSXComponent\n      return (\n        <props.tag>\n          { props.icon ? <Icon /> : slots.default?.() }\n        </props.tag>\n      )\n    }\n  },\n})\nexport type VComponentIcon = InstanceType<typeof VComponentIcon>\n\nexport const VSvgIcon = defineComponent({\n  name: 'VSvgIcon',\n\n  inheritAttrs: false,\n\n  props: makeIconProps(),\n\n  setup (props, { attrs }) {\n    return () => {\n      return (\n        <props.tag { ...attrs } style={ null }>\n          <svg\n            class=\"v-icon__svg\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 24 24\"\n            role=\"img\"\n            aria-hidden=\"true\"\n          >\n            { Array.isArray(props.icon)\n              ? props.icon.map(path => (\n                Array.isArray(path)\n                  ? <path d={ path[0] as string } fill-opacity={ path[1] }></path>\n                  : <path d={ path as string }></path>\n              ))\n              : <path d={ props.icon as string }></path>\n            }\n          </svg>\n        </props.tag>\n      )\n    }\n  },\n})\nexport type VSvgIcon = InstanceType<typeof VSvgIcon>\n\nexport const VLigatureIcon = defineComponent({\n  name: 'VLigatureIcon',\n\n  props: makeIconProps(),\n\n  setup (props) {\n    return () => {\n      return <props.tag>{ props.icon }</props.tag>\n    }\n  },\n})\nexport type VLigatureIcon = InstanceType<typeof VLigatureIcon>\n\nexport const VClassIcon = defineComponent({\n  name: 'VClassIcon',\n\n  props: makeIconProps(),\n\n  setup (props) {\n    return () => {\n      return <props.tag class={ props.icon }></props.tag>\n    }\n  },\n})\nexport type VClassIcon = InstanceType<typeof VClassIcon>\n\nfunction genDefaults (): Record<string, IconSet> {\n  return {\n    svg: {\n      component: VSvgIcon,\n    },\n    class: {\n      component: VClassIcon,\n    },\n  }\n}\n\n// Composables\nexport function createIcons (options?: IconOptions) {\n  const sets = genDefaults()\n  const defaultSet = options?.defaultSet ?? 'mdi'\n\n  if (defaultSet === 'mdi' && !sets.mdi) {\n    sets.mdi = mdi\n  }\n\n  return mergeDeep({\n    defaultSet,\n    sets,\n    aliases: {\n      ...aliases,\n      /* eslint-disable max-len */\n      vuetify: [\n        'M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z',\n        ['M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z', 0.6],\n      ],\n      'vuetify-outline': 'svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z',\n      'vuetify-play': [\n        'm6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z',\n        ['M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z', 0.6],\n      ],\n      /* eslint-enable max-len */\n    },\n  }, options) as InternalIconOptions\n}\n\nexport const useIcon = (props: MaybeRefOrGetter<IconValue | undefined>) => {\n  const icons = inject(IconSymbol)\n\n  if (!icons) throw new Error('Missing Vuetify Icons provide!')\n\n  const iconData = computed<IconInstance>(() => {\n    const iconAlias = toValue(props)\n\n    if (!iconAlias) return { component: VComponentIcon }\n\n    let icon: IconValue | undefined = iconAlias\n\n    if (typeof icon === 'string') {\n      icon = icon.trim()\n      if (icon.startsWith('$')) {\n        icon = icons.aliases?.[icon.slice(1)]\n      }\n    }\n\n    if (!icon) consoleWarn(`Could not find aliased icon \"${iconAlias}\"`)\n\n    if (Array.isArray(icon)) {\n      return {\n        component: VSvgIcon,\n        icon,\n      }\n    } else if (typeof icon !== 'string') {\n      return {\n        component: VComponentIcon,\n        icon,\n      }\n    }\n\n    const iconSetName = Object.keys(icons.sets).find(\n      setName => typeof icon === 'string' && icon.startsWith(`${setName}:`)\n    )\n\n    const iconName = iconSetName ? icon.slice(iconSetName.length + 1) : icon\n    const iconSet = icons.sets[iconSetName ?? icons.defaultSet]\n\n    return {\n      component: iconSet.component,\n      icon: iconName,\n    }\n  })\n\n  return { iconData }\n}\n", "// Utilities\nimport {\n  computed,\n  getCurrentScope,\n  inject,\n  onScopeDispose,\n  provide,\n  ref,\n  shallowRef,\n  toRef,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  consoleWarn,\n  createRange,\n  darken,\n  deprecate,\n  getCurrentInstance,\n  getForeground,\n  getLuma,\n  IN_BROWSER,\n  lighten,\n  mergeDeep,\n  parseColor,\n  propsFactory,\n  RGBtoHex,\n  SUPPORTS_MATCH_MEDIA,\n} from '@/util'\n\n// Types\nimport type { VueHeadClient } from '@unhead/vue/client'\nimport type { HeadClient } from '@vueuse/head'\nimport type { App, DeepReadonly, InjectionKey, Ref } from 'vue'\n\ntype DeepPartial<T> = T extends object ? { [P in keyof T]?: DeepPartial<T[P]> } : T\n\nexport type ThemeOptions = false | {\n  cspNonce?: string\n  defaultTheme?: 'light' | 'dark' | 'system' | string & {}\n  variations?: false | VariationsOptions\n  themes?: Record<string, ThemeDefinition>\n  stylesheetId?: string\n  scope?: string\n  unimportant?: boolean\n}\nexport type ThemeDefinition = DeepPartial<InternalThemeDefinition>\n\ninterface InternalThemeOptions {\n  cspNonce?: string\n  isDisabled: boolean\n  defaultTheme: 'light' | 'dark' | 'system' | string & {}\n  prefix: string\n  variations: false | VariationsOptions\n  themes: Record<string, InternalThemeDefinition>\n  stylesheetId: string\n  scope?: string\n  scoped: boolean\n  unimportant: boolean\n  utilities: boolean\n}\n\ninterface VariationsOptions {\n  colors: string[]\n  lighten: number\n  darken: number\n}\n\ninterface InternalThemeDefinition {\n  dark: boolean\n  colors: Colors\n  variables: Record<string, string | number>\n}\n\nexport interface Colors extends BaseColors, OnColors {\n  [key: string]: string\n}\n\ninterface BaseColors {\n  background: string\n  surface: string\n  primary: string\n  secondary: string\n  success: string\n  warning: string\n  error: string\n  info: string\n}\n\ninterface OnColors {\n  'on-background': string\n  'on-surface': string\n  'on-primary': string\n  'on-secondary': string\n  'on-success': string\n  'on-warning': string\n  'on-error': string\n  'on-info': string\n}\n\nexport interface ThemeInstance {\n  change: (themeName: string) => void\n  cycle: (themeArray?: string[]) => void\n  toggle: (themeArray?: [string, string]) => void\n\n  readonly isDisabled: boolean\n  readonly isSystem: Readonly<Ref<boolean>>\n  readonly themes: Ref<Record<string, InternalThemeDefinition>>\n\n  readonly name: Readonly<Ref<string>>\n  readonly current: DeepReadonly<Ref<InternalThemeDefinition>>\n  readonly computedThemes: DeepReadonly<Ref<Record<string, InternalThemeDefinition>>>\n  readonly prefix: string\n\n  readonly themeClasses: Readonly<Ref<string | undefined>>\n  readonly styles: Readonly<Ref<string>>\n\n  readonly global: {\n    readonly name: Ref<string>\n    readonly current: DeepReadonly<Ref<InternalThemeDefinition>>\n  }\n}\n\nexport const ThemeSymbol: InjectionKey<ThemeInstance> = Symbol.for('vuetify:theme')\n\nexport const makeThemeProps = propsFactory({\n  theme: String,\n}, 'theme')\n\nfunction genDefaults () {\n  return {\n    defaultTheme: 'light',\n    prefix: 'v-',\n    variations: { colors: [], lighten: 0, darken: 0 },\n    themes: {\n      light: {\n        dark: false,\n        colors: {\n          background: '#FFFFFF',\n          surface: '#FFFFFF',\n          'surface-bright': '#FFFFFF',\n          'surface-light': '#EEEEEE',\n          'surface-variant': '#424242',\n          'on-surface-variant': '#EEEEEE',\n          primary: '#1867C0',\n          'primary-darken-1': '#1F5592',\n          secondary: '#48A9A6',\n          'secondary-darken-1': '#018786',\n          error: '#B00020',\n          info: '#2196F3',\n          success: '#4CAF50',\n          warning: '#FB8C00',\n        },\n        variables: {\n          'border-color': '#000000',\n          'border-opacity': 0.12,\n          'high-emphasis-opacity': 0.87,\n          'medium-emphasis-opacity': 0.60,\n          'disabled-opacity': 0.38,\n          'idle-opacity': 0.04,\n          'hover-opacity': 0.04,\n          'focus-opacity': 0.12,\n          'selected-opacity': 0.08,\n          'activated-opacity': 0.12,\n          'pressed-opacity': 0.12,\n          'dragged-opacity': 0.08,\n          'theme-kbd': '#EEEEEE',\n          'theme-on-kbd': '#000000',\n          'theme-code': '#F5F5F5',\n          'theme-on-code': '#000000',\n        },\n      },\n      dark: {\n        dark: true,\n        colors: {\n          background: '#121212',\n          surface: '#212121',\n          'surface-bright': '#ccbfd6',\n          'surface-light': '#424242',\n          'surface-variant': '#c8c8c8',\n          'on-surface-variant': '#000000',\n          primary: '#2196F3',\n          'primary-darken-1': '#277CC1',\n          secondary: '#54B6B2',\n          'secondary-darken-1': '#48A9A6',\n          error: '#CF6679',\n          info: '#2196F3',\n          success: '#4CAF50',\n          warning: '#FB8C00',\n        },\n        variables: {\n          'border-color': '#FFFFFF',\n          'border-opacity': 0.12,\n          'high-emphasis-opacity': 1,\n          'medium-emphasis-opacity': 0.70,\n          'disabled-opacity': 0.50,\n          'idle-opacity': 0.10,\n          'hover-opacity': 0.04,\n          'focus-opacity': 0.12,\n          'selected-opacity': 0.08,\n          'activated-opacity': 0.12,\n          'pressed-opacity': 0.16,\n          'dragged-opacity': 0.08,\n          'theme-kbd': '#424242',\n          'theme-on-kbd': '#FFFFFF',\n          'theme-code': '#343434',\n          'theme-on-code': '#CCCCCC',\n        },\n      },\n    },\n    stylesheetId: 'vuetify-theme-stylesheet',\n    scoped: false,\n    unimportant: false,\n    utilities: true,\n  }\n}\n\nfunction parseThemeOptions (options: ThemeOptions = genDefaults()): InternalThemeOptions {\n  const defaults = genDefaults()\n\n  if (!options) return { ...defaults, isDisabled: true } as any\n\n  const themes: Record<string, InternalThemeDefinition> = {}\n  for (const [key, theme] of Object.entries(options.themes ?? {})) {\n    const defaultTheme = theme.dark || key === 'dark'\n      ? defaults.themes?.dark\n      : defaults.themes?.light\n    themes[key] = mergeDeep(defaultTheme, theme) as InternalThemeDefinition\n  }\n\n  return mergeDeep(\n    defaults,\n    { ...options, themes },\n  ) as InternalThemeOptions\n}\n\nfunction createCssClass (lines: string[], selector: string, content: string[], scope?: string) {\n  lines.push(\n    `${getScopedSelector(selector, scope)} {\\n`,\n    ...content.map(line => `  ${line};\\n`),\n    '}\\n',\n  )\n}\n\nfunction genCssVariables (theme: InternalThemeDefinition, prefix: string) {\n  const lightOverlay = theme.dark ? 2 : 1\n  const darkOverlay = theme.dark ? 1 : 2\n\n  const variables: string[] = []\n  for (const [key, value] of Object.entries(theme.colors)) {\n    const rgb = parseColor(value)\n    variables.push(`--${prefix}theme-${key}: ${rgb.r},${rgb.g},${rgb.b}`)\n    if (!key.startsWith('on-')) {\n      variables.push(`--${prefix}theme-${key}-overlay-multiplier: ${getLuma(value) > 0.18 ? lightOverlay : darkOverlay}`)\n    }\n  }\n\n  for (const [key, value] of Object.entries(theme.variables)) {\n    const color = typeof value === 'string' && value.startsWith('#') ? parseColor(value) : undefined\n    const rgb = color ? `${color.r}, ${color.g}, ${color.b}` : undefined\n    variables.push(`--${prefix}${key}: ${rgb ?? value}`)\n  }\n\n  return variables\n}\n\nfunction genVariation (name: string, color: string, variations: VariationsOptions | false) {\n  const object: Record<string, string> = {}\n  if (variations) {\n    for (const variation of (['lighten', 'darken'] as const)) {\n      const fn = variation === 'lighten' ? lighten : darken\n      for (const amount of createRange(variations[variation], 1)) {\n        object[`${name}-${variation}-${amount}`] = RGBtoHex(fn(parseColor(color), amount))\n      }\n    }\n  }\n  return object\n}\n\nfunction genVariations (colors: InternalThemeDefinition['colors'], variations: VariationsOptions | false) {\n  if (!variations) return {}\n\n  let variationColors = {}\n  for (const name of variations.colors) {\n    const color = colors[name]\n\n    if (!color) continue\n\n    variationColors = {\n      ...variationColors,\n      ...genVariation(name, color, variations),\n    }\n  }\n  return variationColors\n}\n\nfunction genOnColors (colors: InternalThemeDefinition['colors']) {\n  const onColors = {} as InternalThemeDefinition['colors']\n\n  for (const color of Object.keys(colors)) {\n    if (color.startsWith('on-') || colors[`on-${color}`]) continue\n\n    const onColor = `on-${color}` as keyof OnColors\n    const colorVal = parseColor(colors[color])\n\n    onColors[onColor] = getForeground(colorVal)\n  }\n\n  return onColors\n}\n\nfunction getScopedSelector (selector: string, scope?: string) {\n  if (!scope) return selector\n\n  const scopeSelector = `:where(${scope})`\n\n  return selector === ':root' ? scopeSelector : `${scopeSelector} ${selector}`\n}\n\nfunction upsertStyles (id: string, cspNonce: string | undefined, styles: string) {\n  const styleEl = getOrCreateStyleElement(id, cspNonce)\n\n  if (!styleEl) return\n\n  styleEl.innerHTML = styles\n}\n\nfunction getOrCreateStyleElement (id: string, cspNonce?: string) {\n  if (!IN_BROWSER) return null\n\n  let style = document.getElementById(id) as HTMLStyleElement | null\n\n  if (!style) {\n    style = document.createElement('style')\n    style.id = id\n    style.type = 'text/css'\n\n    if (cspNonce) style.setAttribute('nonce', cspNonce)\n\n    document.head.appendChild(style)\n  }\n\n  return style\n}\n\n// Composables\nexport function createTheme (options?: ThemeOptions): ThemeInstance & { install: (app: App) => void } {\n  const parsedOptions = parseThemeOptions(options)\n  const _name = shallowRef(parsedOptions.defaultTheme)\n  const themes = ref(parsedOptions.themes)\n  const systemName = shallowRef('light')\n\n  const name = computed({\n    get () {\n      return _name.value === 'system' ? systemName.value : _name.value\n    },\n    set (val: string) {\n      _name.value = val\n    },\n  })\n\n  const computedThemes = computed(() => {\n    const acc: Record<string, InternalThemeDefinition> = {}\n    for (const [name, original] of Object.entries(themes.value)) {\n      const colors = {\n        ...original.colors,\n        ...genVariations(original.colors, parsedOptions.variations),\n      }\n\n      acc[name] = {\n        ...original,\n        colors: {\n          ...colors,\n          ...genOnColors(colors),\n        },\n      }\n    }\n    return acc\n  })\n\n  const current = toRef(() => computedThemes.value[name.value])\n\n  const isSystem = toRef(() => _name.value === 'system')\n\n  const styles = computed(() => {\n    const lines: string[] = []\n    const important = parsedOptions.unimportant ? '' : ' !important'\n    const scoped = parsedOptions.scoped ? parsedOptions.prefix : ''\n\n    if (current.value?.dark) {\n      createCssClass(lines, ':root', ['color-scheme: dark'], parsedOptions.scope)\n    }\n\n    createCssClass(lines, ':root', genCssVariables(current.value, parsedOptions.prefix), parsedOptions.scope)\n\n    for (const [themeName, theme] of Object.entries(computedThemes.value)) {\n      createCssClass(lines, `.${parsedOptions.prefix}theme--${themeName}`, [\n        `color-scheme: ${theme.dark ? 'dark' : 'normal'}`,\n        ...genCssVariables(theme, parsedOptions.prefix),\n      ], parsedOptions.scope)\n    }\n\n    if (parsedOptions.utilities) {\n      const bgLines: string[] = []\n      const fgLines: string[] = []\n\n      const colors = new Set(Object.values(computedThemes.value).flatMap(theme => Object.keys(theme.colors)))\n      for (const key of colors) {\n        if (key.startsWith('on-')) {\n          createCssClass(fgLines, `.${key}`, [`color: rgb(var(--${parsedOptions.prefix}theme-${key}))${important}`], parsedOptions.scope)\n        } else {\n          createCssClass(bgLines, `.${scoped}bg-${key}`, [\n            `--${parsedOptions.prefix}theme-overlay-multiplier: var(--${parsedOptions.prefix}theme-${key}-overlay-multiplier)`,\n            `background-color: rgb(var(--${parsedOptions.prefix}theme-${key}))${important}`,\n            `color: rgb(var(--${parsedOptions.prefix}theme-on-${key}))${important}`,\n          ], parsedOptions.scope)\n          createCssClass(fgLines, `.${scoped}text-${key}`, [`color: rgb(var(--${parsedOptions.prefix}theme-${key}))${important}`], parsedOptions.scope)\n          createCssClass(fgLines, `.${scoped}border-${key}`, [`--${parsedOptions.prefix}border-color: var(--${parsedOptions.prefix}theme-${key})`], parsedOptions.scope)\n        }\n      }\n\n      lines.push(...bgLines, ...fgLines)\n    }\n\n    return lines.map((str, i) => i === 0 ? str : `    ${str}`).join('')\n  })\n\n  const themeClasses = toRef(() => parsedOptions.isDisabled ? undefined : `${parsedOptions.prefix}theme--${name.value}`)\n  const themeNames = toRef(() => Object.keys(computedThemes.value))\n\n  if (SUPPORTS_MATCH_MEDIA) {\n    const media = window.matchMedia('(prefers-color-scheme: dark)')\n\n    function updateSystemName () {\n      systemName.value = media.matches ? 'dark' : 'light'\n    }\n\n    updateSystemName()\n\n    media.addEventListener('change', updateSystemName, { passive: true })\n\n    if (getCurrentScope()) {\n      onScopeDispose(() => {\n        media.removeEventListener('change', updateSystemName)\n      })\n    }\n  }\n\n  function install (app: App) {\n    if (parsedOptions.isDisabled) return\n\n    const head = app._context.provides.usehead as HeadClient & VueHeadClient<any> | undefined\n    if (head) {\n      function getHead () {\n        return {\n          style: [{\n            textContent: styles.value,\n            id: parsedOptions.stylesheetId,\n            nonce: parsedOptions.cspNonce || false as never,\n          }],\n        }\n      }\n\n      if (head.push) {\n        const entry = head.push(getHead)\n        if (IN_BROWSER) {\n          watch(styles, () => { entry.patch(getHead) })\n        }\n      } else {\n        if (IN_BROWSER) {\n          head.addHeadObjs(toRef(getHead))\n          watchEffect(() => head.updateDOM())\n        } else {\n          head.addHeadObjs(getHead())\n        }\n      }\n    } else {\n      if (IN_BROWSER) {\n        watch(styles, updateStyles, { immediate: true })\n      } else {\n        updateStyles()\n      }\n\n      function updateStyles () {\n        upsertStyles(parsedOptions.stylesheetId, parsedOptions.cspNonce, styles.value)\n      }\n    }\n  }\n\n  function change (themeName: string) {\n    if (themeName !== 'system' && !themeNames.value.includes(themeName)) {\n      consoleWarn(`Theme \"${themeName}\" not found on the Vuetify theme instance`)\n      return\n    }\n\n    name.value = themeName\n  }\n\n  function cycle (themeArray: string[] = themeNames.value) {\n    const currentIndex = themeArray.indexOf(name.value)\n    const nextIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % themeArray.length\n\n    change(themeArray[nextIndex])\n  }\n\n  function toggle (themeArray: [string, string] = ['light', 'dark']) {\n    cycle(themeArray)\n  }\n\n  const globalName = new Proxy(name, {\n    get (target, prop) {\n      return Reflect.get(target, prop)\n    },\n    set (target, prop, val) {\n      if (prop === 'value') {\n        deprecate(`theme.global.name.value = ${val}`, `theme.change('${val}')`)\n      }\n      return Reflect.set(target, prop, val)\n    },\n  })\n\n  return {\n    install,\n    change,\n    cycle,\n    toggle,\n    isDisabled: parsedOptions.isDisabled,\n    isSystem,\n    name,\n    themes,\n    current,\n    computedThemes,\n    prefix: parsedOptions.prefix,\n    themeClasses,\n    styles,\n    global: {\n      name: globalName,\n      current,\n    },\n  }\n}\n\nexport function provideTheme (props: { theme?: string }) {\n  getCurrentInstance('provideTheme')\n\n  const theme = inject(ThemeSymbol, null)\n\n  if (!theme) throw new Error('Could not find Vuetify theme injection')\n\n  const name = toRef(() => props.theme ?? theme.name.value)\n  const current = toRef(() => theme.themes.value[name.value])\n\n  const themeClasses = toRef(() => theme.isDisabled ? undefined : `${theme.prefix}theme--${name.value}`)\n\n  const newTheme: ThemeInstance = {\n    ...theme,\n    name,\n    current,\n    themeClasses,\n  }\n\n  provide(ThemeSymbol, newTheme)\n\n  return newTheme\n}\n\nexport function useTheme () {\n  getCurrentInstance('useTheme')\n\n  const theme = inject(ThemeSymbol, null)\n\n  if (!theme) throw new Error('Could not find Vuetify theme injection')\n\n  return theme\n}\n", "// Composables\nimport { useResizeObserver } from '@/composables/resizeObserver'\n\n// Utilities\nimport {\n  computed,\n  inject,\n  onActivated,\n  onBeforeUnmount,\n  onDeactivated,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  shallowRef, toRef,\n  useId,\n} from 'vue'\nimport { consoleWarn, convertToUnit, findChildrenWithProvide, getCurrentInstance, propsFactory } from '@/util'\n\n// Types\nimport type { ComponentInternalInstance, CSSProperties, InjectionKey, Prop, Ref } from 'vue'\n\nexport type Position = 'top' | 'left' | 'right' | 'bottom'\n\ninterface Layer {\n  top: number\n  bottom: number\n  left: number\n  right: number\n}\n\ninterface LayoutItem extends Layer {\n  id: string\n  size: number\n  position: Position\n}\n\ninterface LayoutProvide {\n  register: (\n    vm: ComponentInternalInstance,\n    options: {\n      id: string\n      order: Ref<number>\n      position: Ref<Position>\n      layoutSize: Ref<number | string>\n      elementSize: Ref<number | string | undefined>\n      active: Ref<boolean>\n      disableTransitions?: Ref<boolean>\n      absolute: Ref<boolean | undefined>\n    }\n  ) => {\n    layoutItemStyles: Ref<CSSProperties>\n    layoutItemScrimStyles: Ref<CSSProperties>\n    zIndex: Ref<number>\n  }\n  unregister: (id: string) => void\n  mainRect: Ref<Layer>\n  mainStyles: Ref<CSSProperties>\n  getLayoutItem: (id: string) => LayoutItem | undefined\n  items: Ref<LayoutItem[]>\n  layoutRect: Ref<DOMRectReadOnly | undefined>\n  rootZIndex: Ref<number>\n}\n\nexport const VuetifyLayoutKey: InjectionKey<LayoutProvide> = Symbol.for('vuetify:layout')\nexport const VuetifyLayoutItemKey: InjectionKey<{ id: string }> = Symbol.for('vuetify:layout-item')\n\nconst ROOT_ZINDEX = 1000\n\nexport const makeLayoutProps = propsFactory({\n  overlaps: {\n    type: Array,\n    default: () => ([]),\n  } as Prop<string[]>,\n  fullHeight: Boolean,\n}, 'layout')\n\n// Composables\nexport const makeLayoutItemProps = propsFactory({\n  name: {\n    type: String,\n  },\n  order: {\n    type: [Number, String],\n    default: 0,\n  },\n  absolute: Boolean,\n}, 'layout-item')\n\nexport function useLayout () {\n  const layout = inject(VuetifyLayoutKey)\n\n  if (!layout) throw new Error('[Vuetify] Could not find injected layout')\n\n  return {\n    getLayoutItem: layout.getLayoutItem,\n    mainRect: layout.mainRect,\n    mainStyles: layout.mainStyles,\n  }\n}\n\nexport function useLayoutItem (options: {\n  id: string | undefined\n  order: Ref<number>\n  position: Ref<Position>\n  layoutSize: Ref<number | string>\n  elementSize: Ref<number | string | undefined>\n  active: Ref<boolean>\n  disableTransitions?: Ref<boolean>\n  absolute: Ref<boolean | undefined>\n}) {\n  const layout = inject(VuetifyLayoutKey)\n\n  if (!layout) throw new Error('[Vuetify] Could not find injected layout')\n\n  const id = options.id ?? `layout-item-${useId()}`\n\n  const vm = getCurrentInstance('useLayoutItem')\n\n  provide(VuetifyLayoutItemKey, { id })\n\n  const isKeptAlive = shallowRef(false)\n  onDeactivated(() => isKeptAlive.value = true)\n  onActivated(() => isKeptAlive.value = false)\n\n  const {\n    layoutItemStyles,\n    layoutItemScrimStyles,\n  } = layout.register(vm, {\n    ...options,\n    active: computed(() => isKeptAlive.value ? false : options.active.value),\n    id,\n  })\n\n  onBeforeUnmount(() => layout.unregister(id))\n\n  return { layoutItemStyles, layoutRect: layout.layoutRect, layoutItemScrimStyles }\n}\n\nconst generateLayers = (\n  layout: string[],\n  positions: Map<string, Ref<Position>>,\n  layoutSizes: Map<string, Ref<number | string>>,\n  activeItems: Map<string, Ref<boolean>>,\n): { id: string, layer: Layer }[] => {\n  let previousLayer: Layer = { top: 0, left: 0, right: 0, bottom: 0 }\n  const layers = [{ id: '', layer: { ...previousLayer } }]\n  for (const id of layout) {\n    const position = positions.get(id)\n    const amount = layoutSizes.get(id)\n    const active = activeItems.get(id)\n    if (!position || !amount || !active) continue\n\n    const layer = {\n      ...previousLayer,\n      [position.value]: parseInt(previousLayer[position.value], 10) + (active.value ? parseInt(amount.value, 10) : 0),\n    }\n\n    layers.push({\n      id,\n      layer,\n    })\n\n    previousLayer = layer\n  }\n\n  return layers\n}\n\nexport function createLayout (props: { overlaps?: string[], fullHeight?: boolean }) {\n  const parentLayout = inject(VuetifyLayoutKey, null)\n  const rootZIndex = computed(() => parentLayout ? parentLayout.rootZIndex.value - 100 : ROOT_ZINDEX)\n  const registered = ref<string[]>([])\n  const positions = reactive(new Map<string, Ref<Position>>())\n  const layoutSizes = reactive(new Map<string, Ref<number | string>>())\n  const priorities = reactive(new Map<string, Ref<number>>())\n  const activeItems = reactive(new Map<string, Ref<boolean>>())\n  const disabledTransitions = reactive(new Map<string, Ref<boolean>>())\n  const { resizeRef, contentRect: layoutRect } = useResizeObserver()\n\n  const computedOverlaps = computed(() => {\n    const map = new Map<string, { position: Position, amount: number }>()\n    const overlaps = props.overlaps ?? []\n    for (const overlap of overlaps.filter(item => item.includes(':'))) {\n      const [top, bottom] = overlap.split(':')\n      if (!registered.value.includes(top) || !registered.value.includes(bottom)) continue\n\n      const topPosition = positions.get(top)\n      const bottomPosition = positions.get(bottom)\n      const topAmount = layoutSizes.get(top)\n      const bottomAmount = layoutSizes.get(bottom)\n\n      if (!topPosition || !bottomPosition || !topAmount || !bottomAmount) continue\n\n      map.set(bottom, { position: topPosition.value, amount: parseInt(topAmount.value, 10) })\n      map.set(top, { position: bottomPosition.value, amount: -parseInt(bottomAmount.value, 10) })\n    }\n\n    return map\n  })\n\n  const layers = computed(() => {\n    const uniquePriorities = [...new Set([...priorities.values()].map(p => p.value))].sort((a, b) => a - b)\n    const layout = []\n    for (const p of uniquePriorities) {\n      const items = registered.value.filter(id => priorities.get(id)?.value === p)\n      layout.push(...items)\n    }\n    return generateLayers(layout, positions, layoutSizes, activeItems)\n  })\n\n  const transitionsEnabled = computed(() => {\n    return !Array.from(disabledTransitions.values()).some(ref => ref.value)\n  })\n\n  const mainRect = computed(() => {\n    return layers.value[layers.value.length - 1].layer\n  })\n\n  const mainStyles = toRef(() => {\n    return {\n      '--v-layout-left': convertToUnit(mainRect.value.left),\n      '--v-layout-right': convertToUnit(mainRect.value.right),\n      '--v-layout-top': convertToUnit(mainRect.value.top),\n      '--v-layout-bottom': convertToUnit(mainRect.value.bottom),\n      ...(transitionsEnabled.value ? undefined : { transition: 'none' }),\n    } satisfies CSSProperties\n  })\n\n  const items = computed(() => {\n    return layers.value.slice(1).map(({ id }, index) => {\n      const { layer } = layers.value[index]\n      const size = layoutSizes.get(id)\n      const position = positions.get(id)\n\n      return {\n        id,\n        ...layer,\n        size: Number(size!.value),\n        position: position!.value,\n      }\n    })\n  })\n\n  const getLayoutItem = (id: string) => {\n    return items.value.find(item => item.id === id)\n  }\n\n  const rootVm = getCurrentInstance('createLayout')\n\n  const isMounted = shallowRef(false)\n  onMounted(() => {\n    isMounted.value = true\n  })\n\n  provide(VuetifyLayoutKey, {\n    register: (\n      vm: ComponentInternalInstance,\n      {\n        id,\n        order,\n        position,\n        layoutSize,\n        elementSize,\n        active,\n        disableTransitions,\n        absolute,\n      }\n    ) => {\n      priorities.set(id, order)\n      positions.set(id, position)\n      layoutSizes.set(id, layoutSize)\n      activeItems.set(id, active)\n      disableTransitions && disabledTransitions.set(id, disableTransitions)\n\n      const instances = findChildrenWithProvide(VuetifyLayoutItemKey, rootVm?.vnode)\n      const instanceIndex = instances.indexOf(vm)\n\n      if (instanceIndex > -1) registered.value.splice(instanceIndex, 0, id)\n      else registered.value.push(id)\n\n      const index = computed(() => items.value.findIndex(i => i.id === id))\n      const zIndex = computed(() => rootZIndex.value + (layers.value.length * 2) - (index.value * 2))\n\n      const layoutItemStyles = computed<CSSProperties>(() => {\n        const isHorizontal = position.value === 'left' || position.value === 'right'\n        const isOppositeHorizontal = position.value === 'right'\n        const isOppositeVertical = position.value === 'bottom'\n        const size = elementSize.value ?? layoutSize.value\n        const unit = size === 0 ? '%' : 'px'\n\n        const styles = {\n          [position.value]: 0,\n          zIndex: zIndex.value,\n          transform: `translate${isHorizontal ? 'X' : 'Y'}(${(active.value ? 0 : -(size === 0 ? 100 : size)) * (isOppositeHorizontal || isOppositeVertical ? -1 : 1)}${unit})`,\n          position: absolute.value || rootZIndex.value !== ROOT_ZINDEX ? 'absolute' : 'fixed',\n          ...(transitionsEnabled.value ? undefined : { transition: 'none' }),\n        } as const\n\n        if (!isMounted.value) return styles\n\n        const item = items.value[index.value]\n\n        if (!item) consoleWarn(`[Vuetify] Could not find layout item \"${id}\"`)\n\n        const overlap = computedOverlaps.value.get(id)\n        if (overlap) {\n          item[overlap.position] += overlap.amount\n        }\n\n        return {\n          ...styles,\n          height:\n            isHorizontal ? `calc(100% - ${item.top}px - ${item.bottom}px)`\n            : elementSize.value ? `${elementSize.value}px`\n            : undefined,\n          left: isOppositeHorizontal ? undefined : `${item.left}px`,\n          right: isOppositeHorizontal ? `${item.right}px` : undefined,\n          top: position.value !== 'bottom' ? `${item.top}px` : undefined,\n          bottom: position.value !== 'top' ? `${item.bottom}px` : undefined,\n          width:\n            !isHorizontal ? `calc(100% - ${item.left}px - ${item.right}px)`\n            : elementSize.value ? `${elementSize.value}px`\n            : undefined,\n        }\n      })\n      const layoutItemScrimStyles = computed<CSSProperties>(() => ({\n        zIndex: zIndex.value - 1,\n      }))\n\n      return { layoutItemStyles, layoutItemScrimStyles, zIndex }\n    },\n    unregister: (id: string) => {\n      priorities.delete(id)\n      positions.delete(id)\n      layoutSizes.delete(id)\n      activeItems.delete(id)\n      disabledTransitions.delete(id)\n      registered.value = registered.value.filter(v => v !== id)\n    },\n    mainRect,\n    mainStyles,\n    getLayoutItem,\n    items,\n    layoutRect,\n    rootZIndex,\n  })\n\n  const layoutClasses = toRef(() => [\n    'v-layout',\n    { 'v-layout--full-height': props.fullHeight },\n  ])\n\n  const layoutStyles = toRef(() => ({\n    zIndex: parentLayout ? rootZIndex.value : undefined,\n    position: parentLayout ? 'relative' as const : undefined,\n    overflow: parentLayout ? 'hidden' : undefined,\n  }))\n\n  return {\n    layoutClasses,\n    layoutStyles,\n    getLayoutItem,\n    items,\n    layoutRect,\n    layoutRef: resizeRef,\n  }\n}\n", "/**\n * Centralized key alias mapping for consistent key normalization across the hotkey system.\n *\n * This maps various user-friendly aliases to canonical key names that match\n * KeyboardEvent.key values (in lowercase) where possible.\n */\nexport const keyAliasMap: Record<string, string> = {\n  // Modifier aliases (from vue-use, other libraries, and current implementation)\n  control: 'ctrl',\n  command: 'cmd',\n  option: 'alt',\n\n  // Arrow key aliases (common abbreviations)\n  up: 'arrowup',\n  down: 'arrowdown',\n  left: 'arrowleft',\n  right: 'arrowright',\n\n  // Other common key aliases\n  esc: 'escape',\n  spacebar: ' ',\n  space: ' ',\n  return: 'enter',\n  del: 'delete',\n\n  // Symbol aliases (existing from hotkey-parsing.ts)\n  minus: '-',\n  hyphen: '-',\n}\n\n/**\n * Normalizes a key string to its canonical form using the alias map.\n *\n * @param key - The key string to normalize\n * @returns The canonical key name in lowercase\n */\nexport function normalizeKey (key: string): string {\n  const lowerKey = key.toLowerCase()\n  return keyAliasMap[lowerKey] || lowerKey\n}\n", "// Utilities\nimport { normalize<PERSON>ey } from './key-aliases'\nimport { consoleWarn } from '../../util/console'\n\n// Types\nexport const MODIFIERS = ['ctrl', 'shift', 'alt', 'meta', 'cmd']\n\n/**\n * Splits a single combination string into individual key parts.\n *\n * A combination is a set of keys that must be pressed simultaneously.\n * e.g. `ctrl+k`, `shift--`\n */\nexport function splitKeyCombination (combination: string, isInternal = false): string[] {\n  if (!combination) {\n    if (!isInternal) consoleWarn('Invalid hotkey combination: empty string provided')\n    return []\n  }\n\n  // --- VALIDATION ---\n  const startsWithPlusOrUnderscore = combination.startsWith('+') || combination.startsWith('_')\n\n  const hasInvalidLeadingSeparator = (\n    // Starts with a single '+' or '_' followed by a non-separator character (e.g. '+a', '_a')\n    startsWithPlusOrUnderscore &&\n    !(combination.startsWith('++') || combination.startsWith('__'))\n  )\n\n  const hasInvalidStructure = (\n    // Invalid leading separator patterns\n    (combination.length > 1 && hasInvalidLeadingSeparator) ||\n    // Disallow literal + or _ keys (they require shift)\n    combination.includes('++') || combination.includes('__') ||\n    combination === '+' || combination === '_' ||\n    // Ends with a separator that is not part of a doubled literal\n    (combination.length > 1 && (combination.endsWith('+') || combination.endsWith('_')) && combination.at(-2) !== combination.at(-1)) ||\n    // Stand-alone doubled separators (dangling)\n    combination === '++' || combination === '--' || combination === '__'\n  )\n\n  if (hasInvalidStructure) {\n    if (!isInternal) consoleWarn(`Invalid hotkey combination: \"${combination}\" has invalid structure`)\n    return []\n  }\n\n  const keys: string[] = []\n  let buffer = ''\n\n  const flushBuffer = () => {\n    if (buffer) {\n      keys.push(normalizeKey(buffer))\n      buffer = ''\n    }\n  }\n\n  for (let i = 0; i < combination.length; i++) {\n    const char = combination[i]\n    const nextChar = combination[i + 1]\n\n    if (char === '+' || char === '_' || char === '-') {\n      if (char === nextChar) {\n        flushBuffer()\n        keys.push(char)\n        i++\n      } else if (char === '+' || char === '_') {\n        flushBuffer()\n      } else {\n        buffer += char\n      }\n    } else {\n      buffer += char\n    }\n  }\n  flushBuffer()\n\n  // Within a combination, `-` is only valid as a literal key (e.g., `ctrl+-`).\n  // `-` cannot be part of a longer key name within a combination.\n  const hasInvalidMinus = keys.some(key => key.length > 1 && key.includes('-') && key !== '--')\n  if (hasInvalidMinus) {\n    if (!isInternal) consoleWarn(`Invalid hotkey combination: \"${combination}\" has invalid structure`)\n    return []\n  }\n\n  if (keys.length === 0 && combination) {\n    return [normalizeKey(combination)]\n  }\n\n  return keys\n}\n\n/**\n * Splits a hotkey string into its constituent combination groups.\n *\n * A sequence is a series of combinations that must be pressed in order.\n * e.g. `a-b`, `ctrl+k-p`\n */\nexport function splitKeySequence (str: string): string[] {\n  if (!str) {\n    consoleWarn('Invalid hotkey sequence: empty string provided')\n    return []\n  }\n\n  // A sequence is invalid if it starts or ends with a separator,\n  // unless it is part of a combination (e.g., `shift+-`).\n  const hasInvalidStart = str.startsWith('-') && !['---', '--+'].includes(str)\n  const hasInvalidEnd = str.endsWith('-') && !str.endsWith('+-') && !str.endsWith('_-') && str !== '-' && str !== '---'\n\n  if (hasInvalidStart || hasInvalidEnd) {\n    consoleWarn(`Invalid hotkey sequence: \"${str}\" contains invalid combinations`)\n    return []\n  }\n\n  const result: string[] = []\n  let buffer = ''\n  let i = 0\n\n  while (i < str.length) {\n    const char = str[i]\n\n    if (char === '-') {\n      // Determine if this hyphen is part of the current combination\n      const prevChar = str[i - 1]\n      const prevPrevChar = i > 1 ? str[i - 2] : undefined\n\n      const precededBySinglePlusOrUnderscore = (\n        (prevChar === '+' || prevChar === '_') && prevPrevChar !== '+'\n      )\n\n      if (precededBySinglePlusOrUnderscore) {\n        // Treat as part of the combination (e.g., 'ctrl+-')\n        buffer += char\n        i++\n      } else {\n        // Treat as sequence separator\n        if (buffer) {\n          result.push(buffer)\n          buffer = ''\n        } else {\n          // Empty buffer means we have a literal '-' key\n          result.push('-')\n        }\n        i++\n      }\n    } else {\n      buffer += char\n      i++\n    }\n  }\n\n  // Add final buffer if it exists\n  if (buffer) {\n    result.push(buffer)\n  }\n\n  // Collapse runs of '-' so that every second '-' is removed\n  const collapsed: string[] = []\n  let minusCount = 0\n  for (const part of result) {\n    if (part === '-') {\n      if (minusCount % 2 === 0) collapsed.push('-')\n      minusCount++\n    } else {\n      minusCount = 0\n      collapsed.push(part)\n    }\n  }\n\n  // Validate that each part of the sequence is a valid combination\n  const areAllValid = collapsed.every(s => splitKeyCombination(s, true).length > 0)\n\n  if (!areAllValid) {\n    consoleWarn(`Invalid hotkey sequence: \"${str}\" contains invalid combinations`)\n    return []\n  }\n\n  return collapsed\n}\n", "// Composables\nimport { splitKeyCombination, splitKeySequence } from '@/composables/hotkey/hotkey-parsing'\n\n// Utilities\nimport { onBeforeUnmount, toValue, watch } from 'vue'\nimport { IN_BROWSER } from '@/util'\nimport { getCurrentInstance } from '@/util/getCurrentInstance'\n\n// Types\nimport type { MaybeRef } from '@/util'\n\ninterface HotkeyOptions {\n  event?: MaybeRef<'keydown' | 'keyup'>\n  inputs?: MaybeRef<boolean>\n  preventDefault?: MaybeRef<boolean>\n  sequenceTimeout?: MaybeRef<number>\n}\n\nexport function useHotkey (\n  keys: MaybeRef<string | undefined>,\n  callback: (e: KeyboardEvent) => void,\n  options: HotkeyOptions = {}\n) {\n  if (!IN_BROWSER) return function () {}\n\n  const {\n    event = 'keydown',\n    inputs = false,\n    preventDefault = true,\n    sequenceTimeout = 1000,\n  } = options\n\n  const isMac = navigator?.userAgent?.includes('Macintosh') ?? false\n  let timeout = 0\n  let keyGroups: string[]\n  let isSequence = false\n  let groupIndex = 0\n\n  function clearTimer () {\n    if (!timeout) return\n\n    clearTimeout(timeout)\n    timeout = 0\n  }\n\n  function isInputFocused () {\n    if (toValue(inputs)) return false\n\n    const activeElement = document.activeElement as HTMLElement\n\n    return activeElement && (\n      activeElement.tagName === 'INPUT' ||\n      activeElement.tagName === 'TEXTAREA' ||\n      activeElement.isContentEditable ||\n      activeElement.contentEditable === 'true'\n    )\n  }\n\n  function resetSequence () {\n    groupIndex = 0\n    clearTimer()\n  }\n\n  function handler (e: KeyboardEvent) {\n    const group = keyGroups[groupIndex]\n\n    if (!group || isInputFocused()) return\n\n    if (!matchesKeyGroup(e, group)) {\n      if (isSequence) resetSequence()\n      return\n    }\n\n    if (toValue(preventDefault)) e.preventDefault()\n\n    if (!isSequence) {\n      callback(e)\n      return\n    }\n\n    clearTimer()\n    groupIndex++\n\n    if (groupIndex === keyGroups.length) {\n      callback(e)\n      resetSequence()\n      return\n    }\n\n    timeout = window.setTimeout(resetSequence, toValue(sequenceTimeout))\n  }\n\n  function cleanup () {\n    window.removeEventListener(toValue(event), handler)\n    clearTimer()\n  }\n\n  watch(() => toValue(keys), function (unrefKeys) {\n    cleanup()\n\n    if (unrefKeys) {\n      const groups = splitKeySequence(unrefKeys.toLowerCase())\n      isSequence = groups.length > 1\n      keyGroups = groups\n      resetSequence()\n      window.addEventListener(toValue(event), handler)\n    }\n  }, { immediate: true })\n\n  // Watch for changes in the event type to re-register the listener\n  watch(() => toValue(event), function (newEvent, oldEvent) {\n    if (oldEvent && keyGroups && keyGroups.length > 0) {\n      window.removeEventListener(oldEvent, handler)\n      window.addEventListener(newEvent, handler)\n    }\n  })\n\n  try {\n    getCurrentInstance('useHotkey')\n    onBeforeUnmount(cleanup)\n  } catch {\n    // Not in Vue setup context\n  }\n\n  function parseKeyGroup (group: string) {\n    const MODIFIERS = ['ctrl', 'shift', 'alt', 'meta', 'cmd']\n\n    // Use the shared combination splitting logic\n    const parts = splitKeyCombination(group.toLowerCase())\n\n    // If the combination is invalid, return empty result\n    if (parts.length === 0) {\n      return { modifiers: Object.fromEntries(MODIFIERS.map(m => [m, false])), actualKey: undefined }\n    }\n\n    const modifiers = Object.fromEntries(MODIFIERS.map(m => [m, false])) as Record<string, boolean>\n    let actualKey: string | undefined\n\n    for (const part of parts) {\n      if (MODIFIERS.includes(part)) {\n        modifiers[part] = true\n      } else {\n        actualKey = part\n      }\n    }\n\n    return { modifiers, actualKey }\n  }\n\n  function matchesKeyGroup (e: KeyboardEvent, group: string) {\n    const { modifiers, actualKey } = parseKeyGroup(group)\n\n    const expectCtrl = modifiers.ctrl || (!isMac && (modifiers.cmd || modifiers.meta))\n    const expectMeta = isMac && (modifiers.cmd || modifiers.meta)\n\n    return (\n      e.ctrlKey === expectCtrl &&\n      e.metaKey === expectMeta &&\n      e.shiftKey === modifiers.shift &&\n      e.altKey === modifiers.alt &&\n      e.key.toLowerCase() === actualKey?.toLowerCase()\n    )\n  }\n\n  return cleanup\n}\n", "// Composables\nimport { createDate, DateAdapterSymbol, DateOptionsSymbol } from '@/composables/date/date'\nimport { createDefaults, DefaultsSymbol } from '@/composables/defaults'\nimport { createDisplay, DisplaySymbol } from '@/composables/display'\nimport { createGoTo, GoToSymbol } from '@/composables/goto'\nimport { createIcons, IconSymbol } from '@/composables/icons'\nimport { createLocale, LocaleSymbol } from '@/composables/locale'\nimport { createTheme, ThemeSymbol } from '@/composables/theme'\n\n// Utilities\nimport { effectScope, nextTick, reactive } from 'vue'\nimport { defineComponent, IN_BROWSER, mergeDeep } from '@/util'\n\n// Types\nimport type { App, ComponentPublicInstance, InjectionKey } from 'vue'\nimport type { DateOptions } from '@/composables/date'\nimport type { DefaultsOptions } from '@/composables/defaults'\nimport type { DisplayOptions, SSROptions } from '@/composables/display'\nimport type { GoToOptions } from '@/composables/goto'\nimport type { IconOptions } from '@/composables/icons'\nimport type { LocaleOptions, RtlOptions } from '@/composables/locale'\nimport type { ThemeOptions } from '@/composables/theme'\n\n// Exports\nexport * from './composables'\nexport * from './types'\n\nexport interface VuetifyOptions {\n  aliases?: Record<string, any>\n  blueprint?: Blueprint\n  components?: Record<string, any>\n  date?: DateOptions\n  directives?: Record<string, any>\n  defaults?: DefaultsOptions\n  display?: DisplayOptions\n  goTo?: GoToOptions\n  theme?: ThemeOptions\n  icons?: IconOptions\n  locale?: LocaleOptions & RtlOptions\n  ssr?: SSROptions\n}\n\nexport interface Blueprint extends Omit<VuetifyOptions, 'blueprint'> {}\n\nexport function createVuetify (vuetify: VuetifyOptions = {}) {\n  const { blueprint, ...rest } = vuetify\n  const options: VuetifyOptions = mergeDeep(blueprint, rest)\n  const {\n    aliases = {},\n    components = {},\n    directives = {},\n  } = options\n\n  const scope = effectScope()\n  return scope.run(() => {\n    const defaults = createDefaults(options.defaults)\n    const display = createDisplay(options.display, options.ssr)\n    const theme = createTheme(options.theme)\n    const icons = createIcons(options.icons)\n    const locale = createLocale(options.locale)\n    const date = createDate(options.date, locale)\n    const goTo = createGoTo(options.goTo, locale)\n\n    function install (app: App) {\n      for (const key in directives) {\n        app.directive(key, directives[key])\n      }\n\n      for (const key in components) {\n        app.component(key, components[key])\n      }\n\n      for (const key in aliases) {\n        app.component(key, defineComponent({\n          ...aliases[key],\n          name: key,\n          aliasName: aliases[key].name,\n        }))\n      }\n\n      const appScope = effectScope()\n      appScope.run(() => {\n        theme.install(app)\n      })\n      app.onUnmount(() => appScope.stop())\n\n      app.provide(DefaultsSymbol, defaults)\n      app.provide(DisplaySymbol, display)\n      app.provide(ThemeSymbol, theme)\n      app.provide(IconSymbol, icons)\n      app.provide(LocaleSymbol, locale)\n      app.provide(DateOptionsSymbol, date.options)\n      app.provide(DateAdapterSymbol, date.instance)\n      app.provide(GoToSymbol, goTo)\n\n      if (IN_BROWSER && options.ssr) {\n        if (app.$nuxt) {\n          app.$nuxt.hook('app:suspense:resolve', () => {\n            display.update()\n          })\n        } else {\n          const { mount } = app\n          app.mount = (...args) => {\n            const vm = mount(...args)\n            nextTick(() => display.update())\n            app.mount = mount\n            return vm\n          }\n        }\n      }\n\n      if (typeof __VUE_OPTIONS_API__ !== 'boolean' || __VUE_OPTIONS_API__) {\n        app.mixin({\n          computed: {\n            $vuetify () {\n              return reactive({\n                defaults: inject.call(this, DefaultsSymbol),\n                display: inject.call(this, DisplaySymbol),\n                theme: inject.call(this, ThemeSymbol),\n                icons: inject.call(this, IconSymbol),\n                locale: inject.call(this, LocaleSymbol),\n                date: inject.call(this, DateAdapterSymbol),\n              })\n            },\n          },\n        })\n      }\n    }\n\n    function unmount () {\n      scope.stop()\n    }\n\n    return {\n      install,\n      unmount,\n      defaults,\n      display,\n      theme,\n      icons,\n      locale,\n      date,\n      goTo,\n    }\n  })!\n}\n\nexport const version = __VUETIFY_VERSION__\ncreateVuetify.version = version\n\n// Vue's inject() can only be used in setup\nfunction inject (this: ComponentPublicInstance, key: InjectionKey<any> | string) {\n  const vm = this.$\n\n  const provides = vm.parent?.provides ?? vm.vnode.appContext?.provides\n\n  if (provides && (key as any) in provides) {\n    return provides[(key as string)]\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMO,SAASA,eAAgBC,QAA8BC,IAAiC;AAC7F,MAAIC;AACJ,WAASC,QAAS;AAChBD,YAAQE,YAAY;AACpBF,UAAMG,IAAI,MAAMJ,GAAGK,SACfL,GAAG,MAAM;AAAEC,qCAAOK;AAAQJ,YAAM;IAAE,CAAC,IAClCF,GAAW,CAChB;EACF;AAEAO,QAAMR,QAAQS,YAAU;AACtB,QAAIA,UAAU,CAACP,OAAO;AACpBC,YAAM;IACR,WAAW,CAACM,QAAQ;AAClBP,qCAAOK;AACPL,cAAQQ;IACV;EACF,GAAG;IAAEC,WAAW;EAAK,CAAC;AAEtBC,iBAAe,MAAM;AACnBV,mCAAOK;EACT,CAAC;AACH;;;AC5BO,IAAMM,aAAa,OAAOC,WAAW;AACrC,IAAMC,wBAAwBF,cAAc,0BAA0BC;AACtE,IAAME,iBAAiBH,eAAe,kBAAkBC,UAAUA,OAAOG,UAAUC,iBAAiB;AACpG,IAAMC,uBAAuBN,cAAc,gBAAgBC;AAC3D,IAAMM,uBAAuBP,cAAc,gBAAgBC,UAAU,OAAOA,OAAOO,eAAe;;;ACyBlG,SAASC,eAAgBC,KAAUC,MAA2BC,UAAqB;AACxF,QAAMC,OAAOF,KAAKG,SAAS;AAE3B,MAAID,OAAO,EAAG,QAAOH,QAAQK,SAAYH,WAAWF;AAEpD,WAASM,IAAI,GAAGA,IAAIH,MAAMG,KAAK;AAC7B,QAAIN,OAAO,MAAM;AACf,aAAOE;IACT;AACAF,UAAMA,IAAIC,KAAKK,CAAC,CAAC;EACnB;AAEA,MAAIN,OAAO,KAAM,QAAOE;AAExB,SAAOF,IAAIC,KAAKE,IAAI,CAAC,MAAME,SAAYH,WAAWF,IAAIC,KAAKE,IAAI,CAAC;AAClE;AA6BO,SAASI,qBAAsBC,KAAUC,MAAsBC,UAAqB;AAEzF,MAAIF,OAAO,QAAQ,CAACC,QAAQ,OAAOA,SAAS,SAAU,QAAOC;AAC7D,MAAIF,IAAIC,IAAI,MAAME,OAAW,QAAOH,IAAIC,IAAI;AAC5CA,SAAOA,KAAKG,QAAQ,cAAc,KAAK;AACvCH,SAAOA,KAAKG,QAAQ,OAAO,EAAE;AAC7B,SAAOC,eAAeL,KAAKC,KAAKK,MAAM,GAAG,GAAGJ,QAAQ;AACtD;AAoCO,SAASK,YAAaC,QAAqC;AAAA,MAArBC,QAAKC,UAAAF,SAAA,KAAAE,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAG;AACnD,SAAOE,MAAMC,KAAK;IAAEL;EAAO,GAAG,CAACM,GAAGC,MAAMN,QAAQM,CAAC;AACnD;AA+BO,SAASC,cAAeC,KAAsC;AACnE,MAAIC;AACJ,SAAOD,QAAQ,QAAQ,OAAOA,QAAQ,cACnCC,QAAQC,OAAOC,eAAeH,GAAG,OAAOE,OAAOE,aAChDH,UAAU;AAEd;AAEO,SAASI,WAAYL,KAA2E;AACrG,MAAIA,OAAO,SAASA,KAAK;AACvB,UAAMM,KAAKN,IAAIO;AACf,SAAID,yBAAIE,cAAaC,KAAKC,WAAW;AAEnC,aAAOJ,GAAGK;IACZ;AACA,WAAOL;EACT;AACA,SAAON;AACT;AAGO,IAAMY,WAAWV,OAAOW,OAAO;EACpCC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,OAAO;EACPC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,WAAW;EACXC,QAAQ;EACRC,QAAQ;EACRC,UAAU;EACVC,OAAO;AACT,CAAC;AAEM,IAAMC,YAAoC7B,OAAOW,OAAO;EAC7DC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,OAAO;EACPC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,WAAW;EACXC,QAAQ;EACRC,QAAQ;EACRC,UAAU;EACVC,OAAO;AACT,CAAC;AAMM,SAASE,IAAuBC,KAAaC,KAAqC;AACvF,SAAOA,IAAIC,MAAMC,OAAKH,IAAII,eAAeD,CAAC,CAAC;AAC7C;AAQO,SAASE,KAGbL,KAAQM,OAAsC;AAC/C,QAAMC,QAAa,CAAC;AAEpB,aAAWN,OAAOK,OAAO;AACvB,QAAIE,OAAOC,UAAUL,eAAeM,KAAKV,KAAKC,GAAG,GAAG;AAClDM,YAAMN,GAAG,IAAID,IAAIC,GAAG;IACtB;EACF;AAEA,SAAOM;AACT;AAmMO,SAASI,MAAOC,OAAiC;AAAA,MAAlBC,MAAGC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAC,MAAEG,MAAGH,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACnD,SAAOI,KAAKD,IAAIJ,KAAKK,KAAKL,IAAII,KAAKL,KAAK,CAAC;AAC3C;AASO,SAASO,OAAQC,KAAaC,QAA4B;AAAA,MAAZC,OAAIC,UAAAF,SAAA,KAAAE,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAG;AAC1D,SAAOH,MAAME,KAAKG,OAAOC,KAAKC,IAAI,GAAGN,SAASD,IAAIC,MAAM,CAAC;AAC3D;AAEO,SAASO,SAAUR,KAAaC,QAA4B;AAAA,MAAZC,OAAIC,UAAAF,SAAA,KAAAE,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAG;AAC5D,SAAOD,KAAKG,OAAOC,KAAKC,IAAI,GAAGN,SAASD,IAAIC,MAAM,CAAC,IAAID;AACzD;AAEO,SAASS,MAAOT,KAAuB;AAAA,MAAVU,OAAIP,UAAAF,SAAA,KAAAE,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAG;AACzC,QAAMQ,UAAoB,CAAA;AAC1B,MAAIC,QAAQ;AACZ,SAAOA,QAAQZ,IAAIC,QAAQ;AACzBU,YAAQE,KAAKb,IAAIc,OAAOF,OAAOF,IAAI,CAAC;AACpCE,aAASF;EACX;AACA,SAAOC;AACT;AAsBO,SAASI,YAId;AAAA,MAHAC,SAA2BC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAChCG,SAA2BH,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAChCI,UAAmDJ,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAEnD,QAAMG,MAA2B,CAAC;AAElC,aAAWC,OAAOP,QAAQ;AACxBM,QAAIC,GAAG,IAAIP,OAAOO,GAAG;EACvB;AAEA,aAAWA,OAAOH,QAAQ;AACxB,UAAMI,iBAAiBR,OAAOO,GAAG;AACjC,UAAME,iBAAiBL,OAAOG,GAAG;AAIjC,QAAIG,cAAcF,cAAc,KAAKE,cAAcD,cAAc,GAAG;AAClEH,UAAIC,GAAG,IAAIR,UAAUS,gBAAgBC,gBAAgBJ,OAAO;AAE5D;IACF;AAEA,QAAIA,WAAWM,MAAMC,QAAQJ,cAAc,KAAKG,MAAMC,QAAQH,cAAc,GAAG;AAC7EH,UAAIC,GAAG,IAAIF,QAAQG,gBAAgBC,cAAc;AAEjD;IACF;AAEAH,QAAIC,GAAG,IAAIE;EACb;AAEA,SAAOH;AACT;AAYO,SAASO,cAAuB;AAAA,MAAVC,MAAGC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACjC,MAAIF,YAAYK,MAAMC,IAAIL,GAAG,EAAG,QAAOD,YAAYK,MAAME,IAAIN,GAAG;AAChE,QAAMO,QAAQP,IACXQ,QAAQ,YAAY,GAAG,EACvBA,QAAQ,cAAc,KAAK,EAC3BC,YAAY;AACfV,cAAYK,MAAMM,IAAIV,KAAKO,KAAK;AAChC,SAAOA;AACT;AACAR,YAAYK,QAAQ,oBAAIO,IAAoB;;;ACvgB5C,IAAMC,UAAU;AAEhB,IAAMC,MAAM;AACZ,IAAMC,MAAM;AACZ,IAAMC,MAAM;AAOZ,IAAMC,SAAS;AACf,IAAMC,UAAU;AAChB,IAAMC,SAAS;AACf,IAAMC,QAAQ;AAId,IAAMC,UAAU;AAChB,IAAMC,UAAU;AAChB,IAAMC,YAAY;AAClB,IAAMC,WAAW;AACjB,IAAMC,WAAW;AACjB,IAAMC,cAAc;AACpB,IAAMC,cAAc;AACpB,IAAMC,cAAc;AACpB,IAAMC,SAAS;AAER,SAASC,aAAcC,MAAWC,YAAiB;AAExD,QAAMC,QAAQF,KAAKG,IAAI,QAAQrB;AAC/B,QAAMsB,QAAQJ,KAAKK,IAAI,QAAQvB;AAC/B,QAAMwB,QAAQN,KAAKO,IAAI,QAAQzB;AAE/B,QAAM0B,OAAOP,WAAWE,IAAI,QAAQrB;AACpC,QAAM2B,OAAOR,WAAWI,IAAI,QAAQvB;AACpC,QAAM4B,OAAOT,WAAWM,IAAI,QAAQzB;AAGpC,MAAI6B,OAAQT,OAAOnB,MAAQqB,OAAOpB,MAAQsB,OAAOrB;AACjD,MAAI2B,MAAOJ,MAAMzB,MAAQ0B,MAAMzB,MAAQ0B,MAAMzB;AAI7C,MAAI0B,QAAQrB,QAASqB,UAASrB,UAAUqB,SAASpB;AACjD,MAAIqB,OAAOtB,QAASsB,SAAQtB,UAAUsB,QAAQrB;AAG9C,MAAIsB,KAAKC,IAAIF,MAAMD,IAAI,IAAInB,UAAW,QAAO;AAI7C,MAAIuB;AACJ,MAAIH,MAAMD,MAAM;AAId,UAAMK,QAASJ,OAAO1B,SAAWyB,QAAQxB,WAAYM;AAOrDsB,qBACGC,OAAOlB,SAAU,IACfkB,OAAOrB,cAAeqB,OAAOA,OAAOpB,cAAcC,cACnDmB,OAAOnB;EACb,OAAO;AAIL,UAAMmB,QAASJ,OAAOvB,QAAUsB,QAAQvB,UAAWM;AAEnDqB,qBACGC,OAAO,CAAClB,SAAU,IAChBkB,OAAO,CAACrB,cAAeqB,OAAOA,OAAOpB,cAAcC,cACpDmB,OAAOnB;EACb;AAEA,SAAOkB,iBAAiB;AAC1B;;;AC5FO,SAASE,YAAaC,SAAuB;AAClDC,OAAK,YAAYD,OAAO,EAAE;AAC5B;AAEO,SAASE,aAAcF,SAAuB;AACnDC,OAAK,kBAAkBD,OAAO,EAAE;AAClC;AAEO,SAASG,UAAWC,UAAkBC,aAAgC;AAC3EA,gBAAcC,MAAMC,QAAQF,WAAW,IACnCA,YAAYG,MAAM,GAAG,EAAE,EAAEC,IAAIC,OAAK,IAAIA,CAAC,GAAG,EAAEC,KAAK,IAAI,IAAI,QAAQN,YAAYO,GAAG,EAAE,CAAC,MACnF,IAAIP,WAAW;AACnBJ,OAAK,sBAAsBG,QAAQ,wBAAwBC,WAAW,WAAW;AACnF;;;ACfA,IAAMQ,QAAQ;AAEd,IAAMC,yBAA0BC,OAC9BA,IAAIF,SAAS,IACTG,KAAKC,KAAKF,CAAC,IACVA,KAAK,IAAIF,SAAS,KAAM,IAAI;AAGnC,IAAMK,yBAA0BH,OAC9BA,IAAIF,QACAE,KAAK,IACJ,IAAIF,SAAS,KAAME,IAAI,IAAI;AAG3B,SAASI,QAASC,KAAe;AACtC,QAAMC,YAAYP;AAClB,QAAMQ,eAAeD,UAAUD,IAAI,CAAC,CAAC;AAErC,SAAO,CACL,MAAME,eAAe,IACrB,OAAOD,UAAUD,IAAI,CAAC,IAAI,OAAO,IAAIE,eACrC,OAAOA,eAAeD,UAAUD,IAAI,CAAC,IAAI,OAAO,EAAE;AAEtD;AAEO,SAASG,MAAOC,KAAe;AACpC,QAAMH,YAAYH;AAClB,QAAMO,MAAMD,IAAI,CAAC,IAAI,MAAM;AAC3B,SAAO,CACLH,UAAUI,KAAKD,IAAI,CAAC,IAAI,GAAG,IAAI,SAC/BH,UAAUI,EAAE,GACZJ,UAAUI,KAAKD,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO;AAE1C;;;AC7BA,IAAME,oBAAoB,CACxB,CAAC,QAAQ,SAAS,OAAO,GACzB,CAAC,SAAS,QAAQ,MAAM,GACxB,CAAC,QAAQ,QAAS,KAAM,CAAC;AAI3B,IAAMC,uBAAwBC,OAC5BA,KAAK,WACDA,IAAI,QACJ,QAAQA,MAAM,IAAI,OAAO;AAI/B,IAAMC,oBAAoB,CACxB,CAAC,QAAQ,QAAQ,MAAM,GACvB,CAAC,QAAQ,QAAQ,MAAM,GACvB,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAI1B,IAAMC,uBAAwBF,OAC5BA,KAAK,UACDA,IAAI,UACFA,IAAI,SAAS,UAAU;AAGxB,SAASG,SAASC,KAAe;AACtC,QAAMC,MAAMC,MAAM,CAAC;AACnB,QAAMC,YAAYR;AAClB,QAAMS,SAASV;AAGf,WAASW,IAAI,GAAGA,IAAI,GAAG,EAAEA,GAAG;AAE1BJ,QAAII,CAAC,IAAIC,KAAKC,MAAMC,MAAML,UACxBC,OAAOC,CAAC,EAAE,CAAC,IAAIL,IAAI,CAAC,IACpBI,OAAOC,CAAC,EAAE,CAAC,IAAIL,IAAI,CAAC,IACpBI,OAAOC,CAAC,EAAE,CAAC,IAAIL,IAAI,CAAC,CACtB,CAAC,IAAI,GAAG;EACV;AAEA,SAAO;IACLS,GAAGR,IAAI,CAAC;IACRS,GAAGT,IAAI,CAAC;IACRU,GAAGV,IAAI,CAAC;EACV;AACF;AAEO,SAASW,OAAKC,MAAyB;AAAA,MAAvB;IAAEJ;IAAGC;IAAGC;EAAO,IAACE;AACrC,QAAMb,MAAW,CAAC,GAAG,GAAG,CAAC;AACzB,QAAMG,YAAYL;AAClB,QAAMM,SAASP;AAGfY,MAAIN,UAAUM,IAAI,GAAG;AACrBC,MAAIP,UAAUO,IAAI,GAAG;AACrBC,MAAIR,UAAUQ,IAAI,GAAG;AAGrB,WAASN,IAAI,GAAGA,IAAI,GAAG,EAAEA,GAAG;AAC1BL,QAAIK,CAAC,IAAID,OAAOC,CAAC,EAAE,CAAC,IAAII,IAAIL,OAAOC,CAAC,EAAE,CAAC,IAAIK,IAAIN,OAAOC,CAAC,EAAE,CAAC,IAAIM;EAChE;AAEA,SAAOX;AACT;;;AC9CA,IAAMc,aAAa;AACnB,IAAMC,UAAU;EACdC,KAAKA,CAACC,GAAWC,GAAWC,GAAWC,OAAgB;IAAEH;IAAGC;IAAGC;IAAGC;EAAE;EACpEC,MAAMA,CAACJ,GAAWC,GAAWC,GAAWC,OAAgB;IAAEH;IAAGC;IAAGC;IAAGC;EAAE;EACrEE,KAAKA,CAACC,IAAWC,GAAWC,GAAWL,MAAeM,SAAS;IAAEH,GAAAA;IAAGC;IAAGC;IAAGL;EAAE,CAAC;EAC7EO,MAAMA,CAACJ,IAAWC,GAAWC,GAAWL,MAAeM,SAAS;IAAEH,GAAAA;IAAGC;IAAGC;IAAGL;EAAE,CAAC;EAC9EQ,KAAKA,CAACL,IAAWC,GAAWK,GAAWT,MAAeU,SAAS;IAAEP,GAAAA;IAAGC;IAAGK;IAAGT;EAAE,CAAC;EAC7EW,MAAMA,CAACR,IAAWC,GAAWK,GAAWT,MAAeU,SAAS;IAAEP,GAAAA;IAAGC;IAAGK;IAAGT;EAAE,CAAC;AAChF;AAEO,SAASY,WAAYC,OAAmB;AAC7C,MAAI,OAAOA,UAAU,UAAU;AAC7B,QAAIC,MAAMD,KAAK,KAAKA,QAAQ,KAAKA,QAAQ,UAAU;AACjDE,kBAAY,IAAIF,KAAK,4BAA4B;IACnD;AAEA,WAAO;MACLhB,IAAIgB,QAAQ,aAAa;MACzBf,IAAIe,QAAQ,UAAW;MACvBd,GAAIc,QAAQ;IACd;EACF,WAAW,OAAOA,UAAU,YAAYnB,WAAWsB,KAAKH,KAAK,GAAG;AAC9D,UAAM;MAAEI;IAAO,IAAIJ,MAAMK,MAAMxB,UAAU;AACzC,UAAM;MAAEyB;MAAIC;IAAO,IAAIH;AACvB,UAAMI,aAAaD,OAAOE,MAAM,mBAAmB,EAChDC,IAAI,CAACd,GAAGe,MAAM;AACb,UACEf,EAAEgB,SAAS,GAAG;MAEbD,IAAI,KAAKA,IAAI,KAAK,CAAC,OAAO,QAAQ,OAAO,MAAM,EAAEE,SAASP,EAAE,GAC7D;AACA,eAAOQ,WAAWlB,CAAC,IAAI;MACzB,OAAO;AACL,eAAOkB,WAAWlB,CAAC;MACrB;IACF,CAAC;AAEH,WAAOd,QAAQwB,EAAE,EAAE,GAAGE,UAAU;EAClC,WAAW,OAAOR,UAAU,UAAU;AACpC,QAAIe,MAAMf,MAAMgB,WAAW,GAAG,IAAIhB,MAAMiB,MAAM,CAAC,IAAIjB;AAEnD,QAAI,CAAC,GAAG,CAAC,EAAEa,SAASE,IAAIG,MAAM,GAAG;AAC/BH,YAAMA,IAAIN,MAAM,EAAE,EAAEC,IAAIS,UAAQA,OAAOA,IAAI,EAAEC,KAAK,EAAE;IACtD,WAAW,CAAC,CAAC,GAAG,CAAC,EAAEP,SAASE,IAAIG,MAAM,GAAG;AACvChB,kBAAY,IAAIF,KAAK,+BAA+B;IACtD;AAEA,UAAMqB,MAAMC,SAASP,KAAK,EAAE;AAC5B,QAAId,MAAMoB,GAAG,KAAKA,MAAM,KAAKA,MAAM,YAAY;AAC7CnB,kBAAY,IAAIF,KAAK,+BAA+B;IACtD;AAEA,WAAOuB,SAASR,GAAU;EAC5B,WAAW,OAAOf,UAAU,UAAU;AACpC,QAAIwB,IAAIxB,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AAC/B,aAAOA;IACT,WAAWwB,IAAIxB,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AACtC,aAAOH,SAAS4B,SAASzB,KAAK,CAAC;IACjC,WAAWwB,IAAIxB,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AACtC,aAAOH,SAASG,KAAK;IACvB;EACF;AAEA,QAAM,IAAI0B,UAAU,kBAAkB1B,SAAS,OAAOA,QAAS2B,OAAO3B,KAAK,KAAMA,MAAc4B,YAAYC,IAAK;qEAAwE;AAC1L;AA6BO,SAASC,SAAUC,MAAgB;AACxC,QAAM;IAAEC,GAAAA;IAAGC;IAAGC;IAAGC;EAAE,IAAIJ;AACvB,QAAMK,IAAKC,OAAc;AACvB,UAAMC,KAAKD,IAAKL,KAAI,MAAO;AAC3B,WAAOE,IAAIA,IAAID,IAAIM,KAAKC,IAAID,KAAKE,IAAIH,GAAG,IAAIA,GAAG,CAAC,GAAG,CAAC;EACtD;AAEA,QAAMI,MAAM,CAACN,EAAE,CAAC,GAAGA,EAAE,CAAC,GAAGA,EAAE,CAAC,CAAC,EAAEO,IAAIT,CAAAA,OAAKK,KAAKK,MAAMV,KAAI,GAAG,CAAC;AAE3D,SAAO;IAAEW,GAAGH,IAAI,CAAC;IAAGI,GAAGJ,IAAI,CAAC;IAAGK,GAAGL,IAAI,CAAC;IAAGP;EAAE;AAC9C;AAEO,SAASa,SAAUC,MAAgB;AACxC,SAAOnB,SAASoB,SAASD,IAAI,CAAC;AAChC;AA0CO,SAASE,SAAUC,KAAe;AACvC,QAAM;IAAEC,GAAAA;IAAGC;IAAGC;IAAGC;EAAE,IAAIJ;AAEvB,QAAMK,IAAIF,IAAID,IAAII,KAAKC,IAAIJ,GAAG,IAAIA,CAAC;AAEnC,QAAMK,SAASH,MAAM,IAAI,IAAI,IAAK,IAAIF,IAAIE;AAE1C,SAAO;IAAEJ,GAAAA;IAAGC,GAAGM;IAAQH;IAAGD;EAAE;AAC9B;AAUA,SAASK,MAAOC,GAAW;AACzB,QAAMC,KAAIC,KAAKC,MAAMH,CAAC,EAAEI,SAAS,EAAE;AACnC,UAAQ,KAAKC,OAAO,GAAG,IAAIJ,GAAEK,MAAM,IAAIL,IAAGM,YAAY;AACxD;AAEO,SAASC,SAAQC,OAA4B;AAAA,MAA1B;IAAEC;IAAGC;IAAGC;IAAGC;EAAO,IAACJ;AAC3C,SAAO,IAAI,CACTV,MAAMW,CAAC,GACPX,MAAMY,CAAC,GACPZ,MAAMa,CAAC,GACPC,MAAMC,SAAYf,MAAMG,KAAKC,MAAMU,IAAI,GAAG,CAAC,IAAI,EAAE,EACjDE,KAAK,EAAE,CAAC;AACZ;AAEO,SAASC,SAAUC,KAAe;AACvCA,QAAMC,SAASD,GAAG;AAClB,MAAI,CAACP,GAAGC,GAAGC,GAAGC,CAAC,IAAIM,MAAMF,KAAK,CAAC,EAAEG,IAAKC,OAAcC,SAASD,GAAG,EAAE,CAAC;AACnER,MAAIA,MAAMC,SAAYD,IAAKA,IAAI;AAE/B,SAAO;IAAEH;IAAGC;IAAGC;IAAGC;EAAE;AACtB;AAWO,SAASU,SAAUC,KAAkB;AAC1C,MAAIA,IAAIC,WAAW,GAAG,GAAG;AACvBD,UAAMA,IAAIE,MAAM,CAAC;EACnB;AAEAF,QAAMA,IAAIG,QAAQ,iBAAiB,GAAG;AAEtC,MAAIH,IAAII,WAAW,KAAKJ,IAAII,WAAW,GAAG;AACxCJ,UAAMA,IAAIK,MAAM,EAAE,EAAEC,IAAIC,OAAKA,IAAIA,CAAC,EAAEC,KAAK,EAAE;EAC7C;AAEA,MAAIR,IAAII,WAAW,GAAG;AACpBJ,UAAMS,OAAOA,OAAOT,KAAK,CAAC,GAAG,GAAG,GAAG;EACrC;AAEA,SAAOA;AACT;AAcO,SAASU,QAASC,OAAYC,QAAqB;AACxD,QAAMC,MAAaC,QAAaC,OAAMJ,KAAK,CAAC;AAC5CE,MAAI,CAAC,IAAIA,IAAI,CAAC,IAAID,SAAS;AAE3B,SAAYE,SAAeC,MAAMF,GAAG,CAAC;AACvC;AAEO,SAASG,OAAQL,OAAYC,QAAqB;AACvD,QAAMC,MAAaC,QAAaC,OAAMJ,KAAK,CAAC;AAC5CE,MAAI,CAAC,IAAIA,IAAI,CAAC,IAAID,SAAS;AAE3B,SAAYE,SAAeC,MAAMF,GAAG,CAAC;AACvC;AAMO,SAASI,QAASC,OAAc;AACrC,QAAMC,MAAMC,WAAWF,KAAK;AAE5B,SAAYH,OAAMI,GAAG,EAAE,CAAC;AAC1B;AAgBO,SAASE,cAAeC,OAAc;AAC3C,QAAMC,gBAAgBC,KAAKC,IAAIC,aAAaC,WAAW,CAAC,GAAGA,WAAWL,KAAK,CAAC,CAAC;AAC7E,QAAMM,gBAAgBJ,KAAKC,IAAIC,aAAaC,WAAW,QAAQ,GAAGA,WAAWL,KAAK,CAAC,CAAC;AAYpF,SAAOM,gBAAgBJ,KAAKK,IAAIN,eAAe,EAAE,IAAI,SAAS;AAChE;;;ACrRO,SAASO,aAEbC,OAAqBC,QAAgB;AACtC,SACEC,cAC0C;AAC1C,WAAOC,OAAOC,KAAKJ,KAAK,EAAEK,OAAY,CAACC,KAAKC,SAAS;AACnD,YAAMC,qBAAqB,OAAOR,MAAMO,IAAI,MAAM,YAAYP,MAAMO,IAAI,KAAK,QAAQ,CAACE,MAAMC,QAAQV,MAAMO,IAAI,CAAC;AAC/G,YAAMI,aAAaH,qBAAqBR,MAAMO,IAAI,IAAI;QAAEK,MAAMZ,MAAMO,IAAI;MAAE;AAE1E,UAAIL,YAAYK,QAAQL,UAAU;AAChCI,YAAIC,IAAI,IAAI;UACV,GAAGI;UACHE,SAASX,SAASK,IAAI;QACxB;MACF,OAAO;AACLD,YAAIC,IAAI,IAAII;MACd;AAEA,UAAIV,UAAU,CAACK,IAAIC,IAAI,EAAEN,QAAQ;AAC/BK,YAAIC,IAAI,EAAEN,SAASA;MACrB;AAEA,aAAOK;IACT,GAAG,CAAC,CAAC;EACP;AACF;;;AC1CO,IAAMQ,qBAAqBC,aAAa;EAC7CC,OAAO,CAACC,QAAQC,OAAOC,MAAM;EAC7BC,OAAO;IACLC,MAAM,CAACJ,QAAQC,OAAOC,MAAM;IAC5BG,SAAS;EACX;AACF,GAAG,WAAW;;;AChBP,SAASC,oBAAoBC,MAAcC,SAAkB;AAClE,QAAMC,KAAKC,mBAAoB;AAE/B,MAAI,CAACD,IAAI;AACP,UAAM,IAAIE,MAAM,aAAaJ,IAAI,IAAIC,WAAW,6CAA6C,EAAE;EACjG;AAEA,SAAOC;AACT;AAEO,SAASG,yBAA8C;AAAA,MAAtBL,OAAIM,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAC7C,QAAMJ,KAAKH,oBAAmBC,IAAI,EAAES;AAEpC,SAAOC,aAAYR,yBAAIS,eAAaT,yBAAIF,KAAI;AAC9C;;;ACXO,SAASY,WAAYC,KAAwE;AAAA,MAAvCC,KAAEC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGG,oBAAmB,YAAY;AAC/F,QAAM;IAAEC;EAAS,IAAIL;AAErB,MAAIK,YAAaN,OAA2BM,UAAU;AAEpD,WAAOA,SAASN,GAAG;EACrB;AACA,SAAOI;AACT;;;ACEO,IAAMG,iBAAsDC,OAAOC,IAAI,kBAAkB;AAEzF,SAASC,eAAgBC,SAAmD;AACjF,SAAOC,IAAID,OAAO;AACpB;AAEO,SAASE,iBAAkB;AAChC,QAAMC,WAAWC,OAAOR,cAAc;AAEtC,MAAI,CAACO,SAAU,OAAM,IAAIE,MAAM,4CAA4C;AAE3E,SAAOF;AACT;AAyDA,SAASG,cAAeC,OAAcC,MAAc;AAClD,SAAOD,MAAME,UAAU,OAAOF,MAAME,MAAMD,IAAI,MAAM,eAClD,OAAOD,MAAME,MAAMC,YAAYF,IAAI,CAAC,MAAM;AAC9C;AAEO,SAASG,sBAId;AAAA,MAHAF,QAA0BG,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAC/BG,OAAaH,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAAA,MACbE,WAAQJ,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGK,eAAe;AAE1B,QAAMC,KAAKC,oBAAmB,aAAa;AAE3CJ,SAAOA,QAAQG,GAAGE,KAAKL,QAAQG,GAAGE,KAAKC;AACvC,MAAI,CAACN,MAAM;AACT,UAAM,IAAIO,MAAM,8CAA8C;EAChE;AAEA,QAAMC,oBAAoBC,SAAS,MAAA;AAvGrC;AAuG2CR,0BAASS,UAATT,mBAAiBP,MAAMiB,OAAOX;GAAK;AAC5E,QAAMY,SAAS,IAAIC,MAAMnB,OAAO;IAC9BoB,IAAKC,QAAQtB,MAAc;AAzG/B;AA0GM,YAAMuB,YAAYC,QAAQH,IAAIC,QAAQtB,IAAI;AAC1C,UAAIA,SAAS,WAAWA,SAAS,SAAS;AACxC,eAAO,EAACe,uBAAkBE,UAAlBF,mBAA0Bf,OAAOuB,SAAS,EAAEE,OAAOC,OAAKA,KAAK,IAAI;MAC3E;AACA,UAAI5B,cAAcY,GAAGX,OAAOC,IAAI,EAAG,QAAOuB;AAC1C,YAAMI,qBAAoBZ,uBAAkBE,UAAlBF,mBAA0Bf;AACpD,UAAI2B,sBAAsBrB,OAAW,QAAOqB;AAC5C,YAAMC,kBAAiBpB,oBAASS,UAATT,mBAAgBqB,WAAhBrB,mBAAyBR;AAChD,UAAI4B,mBAAmBtB,OAAW,QAAOsB;AACzC,aAAOL;IACT;EACF,CAAC;AAED,QAAMO,wBAAwBC,WAAW;AACzCC,cAAY,MAAM;AAChB,QAAIjB,kBAAkBE,OAAO;AAC3B,YAAMgB,gBAAgBC,OAAOC,QAAQpB,kBAAkBE,KAAK,EACzDQ,OAAOW,UAAA;AAAA,YAAC,CAACC,GAAG,IAACD;AAAA,eAAKC,IAAIC,WAAWD,IAAI,CAAC,EAAEE,YAAY,CAAC;MAAC,CAAA;AACzDT,4BAAsBb,QAAQgB,cAAc5B,SAAS6B,OAAOM,YAAYP,aAAa,IAAI3B;IAC3F,OAAO;AACLwB,4BAAsBb,QAAQX;IAChC;EACF,CAAC;AAED,WAASmC,qBAAsB;AAC7B,UAAMC,WAAWC,WAAWC,gBAAgBlC,EAAE;AAC9CmC,YAAQD,gBAAgB5B,SAAS,MAAM;AACrC,aAAOc,sBAAsBb,QAAQ6B,WACnCJ,qCAAUzB,UAAS,CAAC,GACpBa,sBAAsBb,KACxB,IAAIyB,qCAAUzB;IAChB,CAAC,CAAC;EACJ;AAEA,SAAO;IAAEhB,OAAOkB;IAAQsB;EAAmB;AAC7C;AAIO,SAASM,cAGd;AAAA,MAFA9C,QAA0BG,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAC/BG,OAAaH,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAEb,QAAM;IAAEL,OAAOkB;IAAQsB;EAAmB,IAAItC,oBAAoBF,OAAOM,IAAI;AAC7EkC,qBAAmB;AACnB,SAAOtB;AACT;;;ACnDO,SAAS6B,iBAAiBC,SAA2B;AAC1DA,UAAQC,SAASD,QAAQC,UAAUD,QAAQE;AAE3C,MAAI,CAACF,QAAQG,MAAM;AACjBC,gBAAY,kFAAkF;AAE9F,WAAOJ;EACT;AAEA,MAAIA,QAAQC,QAAQ;AAClBD,YAAQK,QAAQC,aAAaN,QAAQK,SAAS,CAAC,GAAGL,QAAQG,IAAI,EAAE;AAChE,UAAMI,WAAWC,OAAOC,KAAKT,QAAQK,KAAK,EAAEK,OAAOC,SAAOA,QAAQ,WAAWA,QAAQ,OAAO;AAC5FX,YAAQY,cAAc,SAASA,YAAaP,OAA4B;AACtE,aAAOQ,KAAKR,OAAOE,QAAQ;IAC7B;AAEAP,YAAQK,MAAMS,MAAMC;AACpBf,YAAQE,QAAQ,SAASA,MAAOG,OAA4BW,KAAK;AAC/D,YAAMC,WAAWC,eAAe;AAGhC,UAAI,CAACD,SAASE,MAAO,QAAOnB,QAAQC,OAAOI,OAAOW,GAAG;AAErD,YAAM;QAAEX,OAAOe;QAAQC;MAAmB,IAAIC,oBAAoBjB,OAAOA,MAAMS,OAAOd,QAAQG,MAAMc,QAAQ;AAE5G,YAAMM,gBAAgBvB,QAAQC,OAAOmB,QAAQJ,GAAG;AAEhDK,yBAAmB;AAEnB,aAAOE;IACT;EACF;AAEA,SAAOvB;AACT;AA4HO,SAASwB,mBAAyC;AAAA,MAAvBC,iBAAcC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACjD,SAAQ1B,cAAmByB,iBAAiB1B,mBAAkB8B,iBAA0B7B,OAAO;AACjG;;;ACxPO,IAAM8B,iBAAiB;EAC5BC,QAASC,OAAcA;EACvBC,YAAaD,OAAcA,KAAK;EAChCE,aAAcF,OAAcA,KAAK,IAAIA;EACrCG,eAAgBH,OAAeA,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAI,IAAIA,KAAKA;EACzEI,aAAcJ,OAAcA,KAAK;EACjCK,cAAeL,OAAc,EAAEA,KAAK,IAAI;EACxCM,gBAAiBN,OAAcA,IAAI,MAAM,IAAIA,KAAK,KAAKA,IAAI,MAAM,IAAIA,IAAI,MAAM,IAAIA,IAAI,KAAK;EAC5FO,aAAcP,OAAcA,KAAK;EACjCQ,cAAeR,OAAc,IAAI,EAAEA,KAAK;EACxCS,gBAAiBT,OAAeA,IAAI,MAAM,IAAIA,KAAK,IAAI,IAAI,IAAI,EAAEA,KAAK;EACtEU,aAAcV,OAAcA,KAAK;EACjCW,cAAeX,OAAc,IAAI,EAAEA,KAAK;EACxCY,gBAAiBZ,OAAcA,IAAI,MAAM,KAAKA,KAAK,IAAI,IAAI,KAAK,EAAEA,KAAK;AACzE;;;ACbO,SAASa,gBAKdC,OACAC,MACAC,cAGA;AAAA,MAFAC,cAA2CC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAIG,OAAWA;AAAC,MAC3DC,eAA2CJ,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAIG,OAAWA;AAE1D,QAAME,KAAKC,oBAAmB,iBAAiB;AAC/C,QAAMC,WAAWC,IAAIZ,MAAMC,IAAI,MAAMK,SAAYN,MAAMC,IAAI,IAAIC,YAAY;AAC3E,QAAMW,YAAYC,YAAYb,IAAI;AAClC,QAAMc,aAAaF,cAAcZ;AAEjC,QAAMe,eAAeD,aACjBE,SAAS,MAAM;AA/BrB;AAgCM,SAAKjB,MAAMC,IAAI;AACf,WAAO,CAAC,KACLQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAelB,YAASQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAeN,mBACvEJ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAe,YAAYlB,IAAI,UAAOQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAe,YAAYN,SAAS;EAE/G,CAAC,IACCI,SAAS,MAAM;AAtCrB;AAuCM,SAAKjB,MAAMC,IAAI;AACf,WAAO,CAAC,IAAEQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAelB,YAASQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAe,YAAYlB,IAAI;EACnG,CAAC;AAEHmB,iBAAe,MAAM,CAACJ,aAAaK,OAAO,MAAM;AAC9CC,UAAM,MAAMtB,MAAMC,IAAI,GAAGsB,SAAO;AAC9BZ,eAASU,QAAQE;IACnB,CAAC;EACH,CAAC;AAED,QAAMC,QAAQP,SAAS;IACrBQ,MAAY;AACV,YAAMC,gBAAgB1B,MAAMC,IAAI;AAChC,aAAOE,YAAYa,aAAaK,QAAQK,gBAAgBf,SAASU,KAAK;IACxE;IACAM,IAAKC,eAAe;AAClB,YAAMC,WAAWrB,aAAaoB,aAAa;AAC3C,YAAMP,QAAQS,MAAMd,aAAaK,QAAQrB,MAAMC,IAAI,IAAIU,SAASU,KAAK;AACrE,UAAIA,UAAUQ,YAAY1B,YAAYkB,KAAK,MAAMO,eAAe;AAC9D;MACF;AACAjB,eAASU,QAAQQ;AACjBpB,+BAAIsB,KAAK,UAAU9B,IAAI,IAAI4B;IAC7B;EACF,CAAC;AAEDG,SAAOC,eAAeT,OAAO,iBAAiB;IAC5CC,KAAKA,MAAMT,aAAaK,QAAQrB,MAAMC,IAAI,IAAIU,SAASU;EACzD,CAAC;AAED,SAAOG;AACT;;;ACtEA,IAAA,aAAe;EACbU,OAAO;EACPC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC,aAAa;IACXC,IAAI;IACJC,QAAQ;EACV;EACAC,cAAc;IACZC,eAAe;IACfC,aAAa;EACf;EACAC,WAAW;IACTC,kBAAkB;IAClBC,WAAW;MACTC,gBAAgB;MAChBC,eAAe;MACfC,UAAU;MACVC,cAAc;MACdC,oBAAoB;MACpBC,mBAAmB;IACrB;IACAC,QAAQ;EACV;EACAC,YAAY;IACVT,kBAAkB;IAClBU,iBAAiB;IACjBC,UAAU;IACVC,UAAU;IACVC,WAAW;IACXC,UAAU;IACVC,UAAU;EACZ;EACAC,gBAAgB;IACdC,SAAS;EACX;EACAC,YAAY;IACVC,eAAe;IACfC,OAAO;MACLC,OAAO;MACPC,QAAQ;IACV;IACAD,OAAO;IACPC,QAAQ;IACRC,OAAO;MACLC,aAAa;IACf;IACAvB,WAAW;MACTwB,eAAe;MACfC,WAAW;MACXC,YAAY;MACZC,YAAY;;MACZC,aAAa;IACf;EACF;EACAC,YAAY;EACZC,UAAU;IACRC,MAAM;IACNC,MAAM;IACNhC,WAAW;MACTiC,WAAW;IACb;EACF;EACAC,UAAU;IACRC,YAAY;IACZC,OAAO;EACT;EACAd,OAAO;IACLe,OAAO;IACPC,eAAe;IACfC,cAAc;IACdC,KAAK;EACP;EACAC,WAAW;IACTC,SAAS;IACTC,aAAa;EACf;EACAC,YAAY;IACVxB,OAAO;IACPJ,SAAS;IACT6B,QAAQ;EACV;EACAC,YAAY;IACVC,IAAI;IACJC,IAAI;IACJ5B,OAAO;EACT;EACA6B,YAAY;IACVjD,WAAW;MACTkD,MAAM;MACNlB,MAAM;MACNmB,UAAU;MACVC,MAAM;MACNC,aAAa;MACbC,OAAO;MACPC,MAAM;IACR;EACF;EACAC,SAAS;IACPxB,MAAM;IACND,MAAM;EACR;EACA0B,QAAQ;IACNzD,WAAW;MACT0D,MAAM;IACR;EACF;EACAC,SAAS;EACTC,gBAAgB;IACdC,UAAU;IACVC,OAAO;EACT;EACAC,OAAO;IACLC,UAAU;IACVC,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC,WAAW;IACXC,cAAc;IACdC,SAAS;IACTC,UAAU;IACVC,SAAS;EACX;EACAC,QAAQ;IACNC,MAAM;IACNC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC,WAAW;IACXC,WAAW;IACXC,YAAY;IACZC,WAAW;IACXC,QAAQ;IACRC,MAAM;IACNC,UAAU;EACZ;EACAC,OAAO;IACLC,MAAM;IACNC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,YAAY;IACZC,MAAM;IACNC,QAAQ;IACRC,iBAAiB;IACjBC,gBAAgB;EAClB;AACF;;;AC7IA,IAAMC,cAAc;AAEpB,IAAMC,UAAUA,CAACC,KAAaC,WAAsB;AAClD,SAAOD,IAAID,QAAQ,cAAc,CAACG,OAAeC,UAAkB;AACjE,WAAOC,OAAOH,OAAOI,OAAOF,KAAK,CAAC,CAAC;EACrC,CAAC;AACH;AAEA,IAAMG,0BAA0BA,CAC9BC,SACAC,UACAC,aACG;AACH,SAAO,SAACC,KAAsC;AAAA,aAAAC,OAAAC,UAAAC,QAAtBZ,SAAM,IAAAa,MAAAH,OAAA,IAAAA,OAAA,IAAA,CAAA,GAAAI,OAAA,GAAAA,OAAAJ,MAAAI,QAAA;AAANd,aAAMc,OAAA,CAAA,IAAAH,UAAAG,IAAA;IAAA;AAC5B,QAAI,CAACL,IAAIM,WAAWlB,WAAW,GAAG;AAChC,aAAOC,QAAQW,KAAKT,MAAM;IAC5B;AAEA,UAAMgB,WAAWP,IAAIX,QAAQD,aAAa,EAAE;AAC5C,UAAMoB,gBAAgBX,QAAQY,SAASV,SAASU,MAAMZ,QAAQY,KAAK;AACnE,UAAMC,iBAAiBZ,SAASW,SAASV,SAASU,MAAMX,SAASW,KAAK;AAEtE,QAAInB,MAAcqB,qBAAqBH,eAAeD,UAAU,IAAI;AAEpE,QAAI,CAACjB,KAAK;AACRsB,kBAAY,oBAAoBZ,GAAG,mBAAmBH,QAAQY,KAAK,2BAA2B;AAC9FnB,YAAMqB,qBAAqBD,gBAAgBH,UAAU,IAAI;IAC3D;AAEA,QAAI,CAACjB,KAAK;AACRuB,mBAAa,oBAAoBb,GAAG,yBAAyB;AAC7DV,YAAMU;IACR;AAEA,QAAI,OAAOV,QAAQ,UAAU;AAC3BuB,mBAAa,oBAAoBb,GAAG,0BAA0B;AAC9DV,YAAMU;IACR;AAEA,WAAOX,QAAQC,KAAKC,MAAM;EAC5B;AACF;AAEA,SAASuB,qBAAsBjB,SAAsBC,UAAuB;AAC1E,SAAO,CAACW,OAAeM,YAAuC;AAC5D,UAAMC,eAAe,IAAIC,KAAKC,aAAa,CAACrB,QAAQY,OAAOX,SAASW,KAAK,GAAGM,OAAO;AAEnF,WAAOC,aAAaG,OAAOV,KAAK;EAClC;AACF;AAEA,SAASW,sBAAuBvB,SAAsBC,UAAuB;AAC3E,QAAMqB,UAASL,qBAAqBjB,SAASC,QAAQ;AACrD,SAAOqB,QAAO,GAAG,EAAEE,SAAS,GAAG,IAAI,MAAM;AAC3C;AAEA,SAASC,YAAiBC,OAAYC,MAAcC,UAAkB;AACpE,QAAMC,WAAWC,gBAAgBJ,OAAOC,MAAMD,MAAMC,IAAI,KAAKC,SAAShB,KAAK;AAG3EiB,WAASjB,QAAQc,MAAMC,IAAI,KAAKC,SAAShB;AAEzCmB,QAAMH,UAAUI,OAAK;AACnB,QAAIN,MAAMC,IAAI,KAAK,MAAM;AACvBE,eAASjB,QAAQgB,SAAShB;IAC5B;EACF,CAAC;AAED,SAAOiB;AACT;AAEA,SAASI,sBAAuBC,OAAuF;AACrH,SAAQR,WAAyC;AAC/C,UAAM1B,UAAUyB,YAAYC,OAAO,UAAUQ,MAAMlC,OAAO;AAC1D,UAAMC,WAAWwB,YAAYC,OAAO,YAAYQ,MAAMjC,QAAQ;AAC9D,UAAMC,WAAWuB,YAAYC,OAAO,YAAYQ,MAAMhC,QAAQ;AAE9D,WAAO;MACLiC,MAAM;MACNnC;MACAC;MACAC;MACAkC,kBAAkBC,MAAM,MAAMd,sBAAsBvB,SAASC,QAAQ,CAAC;MACtEqC,GAAGvC,wBAAwBC,SAASC,UAAUC,QAAQ;MACtDqC,GAAGtB,qBAAqBjB,SAASC,QAAQ;MACzCuC,SAASP,sBAAsB;QAAEjC;QAASC;QAAUC;MAAS,CAAC;IAChE;EACF;AACF;AAEO,SAASuC,qBAAsBvB,SAAyC;AAC7E,QAAMlB,UAAU0C,YAAWxB,mCAASyB,WAAU,IAAI;AAClD,QAAM1C,WAAWyC,YAAWxB,mCAASjB,aAAY,IAAI;AACrD,QAAMC,WAAW0C,IAAI;IAAEC;IAAI,GAAG3B,mCAAShB;EAAS,CAAC;AAEjD,SAAO;IACLiC,MAAM;IACNnC;IACAC;IACAC;IACAkC,kBAAkBC,MAAM,OAAMnB,mCAASkB,qBAAoBb,sBAAsBvB,SAASC,QAAQ,CAAC;IACnGqC,GAAGvC,wBAAwBC,SAASC,UAAUC,QAAQ;IACtDqC,GAAGtB,qBAAqBjB,SAASC,QAAQ;IACzCuC,SAASP,sBAAsB;MAAEjC;MAASC;MAAUC;IAAS,CAAC;EAChE;AACF;;;ACzFO,IAAM4C,eAA2DC,OAAOC,IAAI,gBAAgB;AAEnG,SAASC,iBAAkBC,KAAiC;AAC1D,SAAOA,IAAIC,QAAQ;AACrB;AAEO,SAASC,aAAcC,SAAsC;AAClE,QAAMC,QAAOD,mCAASE,YAAWN,iBAAiBI,mCAASE,OAAO,IAAIF,mCAASE,UAAUC,qBAAqBH,OAAO;AACrH,QAAMI,MAAMC,UAAUJ,MAAMD,OAAO;AAEnC,SAAO;IAAE,GAAGC;IAAM,GAAGG;EAAI;AAC3B;AAEO,SAASE,YAAa;AAC3B,QAAMC,SAASC,OAAOf,YAAY;AAElC,MAAI,CAACc,OAAQ,OAAM,IAAIE,MAAM,mDAAmD;AAEhF,SAAOF;AACT;AAiCO,IAAMG,YAAuCC,OAAOC,IAAI,aAAa;AAE5E,SAASC,cAAe;AACtB,SAAO;IACLC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,QAAQ;EACV;AACF;AAEO,SAASC,UAAWC,MAAsBC,SAAmC;AAClF,QAAMC,MAAMC,KAA6BF,mCAASC,QAAO9C,YAAY,CAAC;AACtE,QAAMgD,QAAQC,SAAS,MAAMH,IAAII,MAAMN,KAAKO,QAAQD,KAAK,KAAK,KAAK;AAEnE,SAAO;IACLF;IACAF;IACAM,YAAYC,MAAM,MAAM,gBAAgBL,MAAME,QAAQ,QAAQ,KAAK,EAAE;EACvE;AACF;AAYO,SAASI,SAAU;AACxB,QAAMC,SAASC,OAAOC,YAAY;AAElC,MAAI,CAACF,OAAQ,OAAM,IAAIG,MAAM,gDAAgD;AAE7E,SAAO;IAAEC,OAAOJ,OAAOI;IAAOC,YAAYL,OAAOK;EAAW;AAC9D;;;ACtJA,SAASC,SAAUC,QAAoE;AAGrF,QAAMC,OAAOD,OAAOE,MAAM,EAAE,EAAEC,YAAY;AAC1C,UAAQ,MAAI;IACV,KAAKH,WAAW,kBAAkB;AAChC,aAAO;QAAEI,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA,KAAKL,WAAW,OAAO;AACrB,aAAO;QAAEI,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA,KAAK;;uBAEcC,SAASL,IAAI,GAAG;AACjC,aAAO;QAAEG,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA,KAAK;wDAC+CC,SAASL,IAAI,GAAG;AAClE,aAAO;QAAEG,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA,KAAK;kDACyCC,SAASL,IAAI,GAAG;AAC5D,aAAO;QAAEG,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA,KAAK,+CAA+CC,SAASL,IAAI,GAAG;AAClE,aAAO;QAAEG,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA,KAAKJ,SAAS,MAAM;AAClB,aAAO;QAAEG,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA,KAAKJ,SAAS,MAAM;AAClB,aAAO;QAAEG,UAAU;QAAGC,eAAe;MAAE;IACzC;IACA;AAAS,aAAO;EAClB;AACF;AAEA,SAASE,aAAcC,OAAYR,QAAgBS,gBAAyB;AA7C5E;AA8CE,QAAMC,QAAQ,CAAA;AACd,MAAIC,cAAc,CAAA;AAClB,QAAMC,kBAAkBC,aAAaL,KAAI;AACzC,QAAMM,iBAAiBC,WAAWP,KAAI;AACtC,QAAMQ,QAAQP,oBAAkBV,cAASC,MAAM,MAAfD,mBAAkBK,aAAY;AAC9D,QAAMa,qBAAqBL,gBAAgBM,OAAO,IAAIF,QAAQ,KAAK;AACnE,QAAMG,oBAAoBL,eAAeI,OAAO,IAAIF,QAAQ,KAAK;AAEjE,WAASI,IAAI,GAAGA,IAAIH,mBAAmBG,KAAK;AAC1C,UAAMC,cAAc,IAAIC,KAAKV,eAAe;AAC5CS,gBAAYE,QAAQF,YAAYG,QAAQ,KAAKP,oBAAoBG,EAAE;AACnET,gBAAYc,KAAKJ,WAAW;EAC9B;AAEA,WAASD,IAAI,GAAGA,KAAKN,eAAeU,QAAQ,GAAGJ,KAAK;AAClD,UAAMM,MAAM,IAAIJ,KAAKd,MAAKmB,YAAY,GAAGnB,MAAKoB,SAAS,GAAGR,CAAC;AAG3DT,gBAAYc,KAAKC,GAAG;AAGpB,QAAIf,YAAYkB,WAAW,GAAG;AAC5BnB,YAAMe,KAAKd,WAAW;AACtBA,oBAAc,CAAA;IAChB;EACF;AAEA,WAASS,IAAI,GAAGA,IAAI,IAAID,kBAAkBC,KAAK;AAC7C,UAAMC,cAAc,IAAIC,KAAKR,cAAc;AAC3CO,gBAAYE,QAAQF,YAAYG,QAAQ,IAAIJ,CAAC;AAC7CT,gBAAYc,KAAKJ,WAAW;EAC9B;AAEA,MAAIV,YAAYkB,SAAS,GAAG;AAC1BnB,UAAMe,KAAKd,WAAW;EACxB;AAEA,SAAOD;AACT;AAEA,SAASoB,YAAatB,OAAYR,QAAgBS,gBAAyB;AAtF3E;AAuFE,MAAIiB,OAAOjB,oBAAkBV,cAASC,MAAM,MAAfD,mBAAkBK,aAAY,KAAK;AAGhE,MAAI,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAEE,SAASoB,GAAG,GAAG;AACxCK,gBAAY,iEAAiE;AAC7EL,UAAM;EACR;AAEA,QAAMM,IAAI,IAAIV,KAAKd,KAAI;AACvB,SAAOwB,EAAEd,OAAO,MAAMQ,KAAK;AACzBM,MAAET,QAAQS,EAAER,QAAQ,IAAI,CAAC;EAC3B;AACA,SAAOQ;AACT;AAEA,SAASC,UAAWzB,OAAYR,QAAgB;AAtGhD;AAuGE,QAAMgC,IAAI,IAAIV,KAAKd,KAAI;AACvB,QAAM0B,cAAYnC,cAASC,MAAM,MAAfD,mBAAkBK,aAAY,KAAK,KAAK;AAC1D,SAAO4B,EAAEd,OAAO,MAAMgB,SAAS;AAC7BF,MAAET,QAAQS,EAAER,QAAQ,IAAI,CAAC;EAC3B;AACA,SAAOQ;AACT;AAEA,SAASnB,aAAcL,OAAY;AACjC,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAGnB,MAAKoB,SAAS,GAAG,CAAC;AACxD;AAEA,SAASb,WAAYP,OAAY;AAC/B,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAGnB,MAAKoB,SAAS,IAAI,GAAG,CAAC;AAC5D;AAEA,SAASO,eAAgBC,OAAqB;AAC5C,QAAMC,QAAQD,MAAME,MAAM,GAAG,EAAEC,IAAIC,MAAM;AAGzC,SAAO,IAAIlB,KAAKe,MAAM,CAAC,GAAGA,MAAM,CAAC,IAAI,GAAGA,MAAM,CAAC,CAAC;AAClD;AAEA,IAAMI,WAAW;AAEjB,SAASjC,KAAM4B,OAA0B;AACvC,MAAIA,SAAS,KAAM,QAAO,oBAAId,KAAK;AAEnC,MAAIc,iBAAiBd,KAAM,QAAOc;AAElC,MAAI,OAAOA,UAAU,UAAU;AAC7B,QAAIM;AAEJ,QAAID,SAASE,KAAKP,KAAK,GAAG;AACxB,aAAOD,eAAeC,KAAK;IAC7B,OAAO;AACLM,eAASpB,KAAKsB,MAAMR,KAAK;IAC3B;AAEA,QAAI,CAACS,MAAMH,MAAM,EAAG,QAAO,IAAIpB,KAAKoB,MAAM;EAC5C;AAEA,SAAO;AACT;AAEA,IAAMI,0BAA0B,IAAIxB,KAAK,KAAM,GAAG,CAAC;AAEnD,SAASyB,YAAa/C,QAAgBS,gBAAyBuC,eAA6C;AAtJ5G;AAuJE,QAAMC,iBAAiBxC,oBAAkBV,cAASC,MAAM,MAAfD,mBAAkBK,aAAY;AAEvE,SAAO8C,YAAY,CAAC,EAAEX,IAAInB,OAAK;AAC7B,UAAM+B,UAAU,IAAI7B,KAAKwB,uBAAuB;AAChDK,YAAQ5B,QAAQuB,wBAAwBtB,QAAQ,IAAIyB,iBAAiB7B,CAAC;AACtE,WAAO,IAAIgC,KAAKC,eAAerD,QAAQ;MAAEmD,SAASH,iBAAiB;IAAS,CAAC,EAAEM,OAAOH,OAAO;EAC/F,CAAC;AACH;AAEA,SAASG,OACPlB,OACAmB,cACAvD,QACAwD,SACQ;AACR,QAAMC,UAAUjD,KAAK4B,KAAK,KAAK,oBAAId,KAAK;AACxC,QAAMoC,eAAeF,mCAAUD;AAE/B,MAAI,OAAOG,iBAAiB,YAAY;AACtC,WAAOA,aAAaD,SAASF,cAAcvD,MAAM;EACnD;AAEA,MAAI2D,UAAsC,CAAC;AAC3C,UAAQJ,cAAY;IAClB,KAAK;AACHI,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAASnC,KAAK;MAAU;AAC5D;IACF,KAAK;AACHiC,gBAAU;QAAER,SAAS;QAAQS,MAAM;QAAWC,OAAO;QAAQnC,KAAK;MAAU;AAC5E;IACF,KAAK;AACH,YAAMA,MAAM+B,QAAQjC,QAAQ;AAC5B,YAAMqC,QAAQ,IAAIT,KAAKC,eAAerD,QAAQ;QAAE6D,OAAO;MAAO,CAAC,EAAEP,OAAOG,OAAO;AAC/E,aAAO,GAAG/B,GAAG,IAAImC,KAAK;IACxB,KAAK;AACHF,gBAAU;QAAER,SAAS;QAASzB,KAAK;QAAWmC,OAAO;MAAQ;AAC7D;IACF,KAAK;AACHF,gBAAU;QAAEE,OAAO;QAASnC,KAAK;MAAU;AAC3C;IACF,KAAK;AACHiC,gBAAU;QAAEC,MAAM;MAAU;AAC5B;IACF,KAAK;AACHD,gBAAU;QAAEE,OAAO;MAAO;AAC1B;IACF,KAAK;AACHF,gBAAU;QAAEE,OAAO;MAAQ;AAC3B;IACF,KAAK;AACHF,gBAAU;QAAEE,OAAO;QAAQD,MAAM;MAAU;AAC3C;IACF,KAAK;AACHD,gBAAU;QAAEE,OAAO;QAAQnC,KAAK;MAAU;AAC1C;IACF,KAAK;AACHiC,gBAAU;QAAER,SAAS;MAAO;AAC5B;IACF,KAAK;AACHQ,gBAAU;QAAER,SAAS;MAAQ;AAC7B;IACF,KAAK;AACH,aAAO,IAAIC,KAAKU,aAAa9D,MAAM,EAAEsD,OAAOG,QAAQjC,QAAQ,CAAC;IAC/D,KAAK;AACHmC,gBAAU;QAAEI,MAAM;QAAWC,QAAQ;MAAK;AAC1C;IACF,KAAK;AACHL,gBAAU;QAAEI,MAAM;QAAWC,QAAQ;MAAM;AAC3C;IACF,KAAK;AACHL,gBAAU;QAAEM,QAAQ;MAAU;AAC9B;IACF,KAAK;AACHN,gBAAU;QAAEO,QAAQ;MAAU;AAC9B;IACF,KAAK;AACHP,gBAAU;QAAEI,MAAM;QAAWE,QAAQ;MAAU;AAC/C;IACF,KAAK;AACHN,gBAAU;QAAEI,MAAM;QAAWE,QAAQ;QAAWD,QAAQ;MAAK;AAC7D;IACF,KAAK;AACHL,gBAAU;QAAEI,MAAM;QAAWE,QAAQ;QAAWD,QAAQ;MAAM;AAC9D;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAASnC,KAAK;QAAWqC,MAAM;QAAWE,QAAQ;MAAU;AAChG;IACF,KAAK;AACHN,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAASnC,KAAK;QAAWqC,MAAM;QAAWE,QAAQ;QAAWD,QAAQ;MAAK;AAC9G;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAASnC,KAAK;QAAWqC,MAAM;QAAWE,QAAQ;QAAWD,QAAQ;MAAM;AAC/G;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWnC,KAAK;MAAU;AAC9D;IACF,KAAK;AACHiC,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWnC,KAAK;QAAWqC,MAAM;QAAWE,QAAQ;MAAU;AAClG,aAAO,IAAIb,KAAKC,eAAerD,QAAQ2D,OAAO,EAAEL,OAAOG,OAAO,EAAEU,QAAQ,OAAO,GAAG;IACpF,KAAK;AACHR,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWnC,KAAK;QAAWqC,MAAM;QAAWE,QAAQ;QAAWD,QAAQ;MAAK;AAChH,aAAO,IAAIZ,KAAKC,eAAerD,QAAQ2D,OAAO,EAAEL,OAAOG,OAAO,EAAEU,QAAQ,OAAO,GAAG;IACpF,KAAK;AACHR,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWnC,KAAK;QAAWqC,MAAM;QAAWE,QAAQ;QAAWD,QAAQ;MAAM;AACjH,aAAO,IAAIZ,KAAKC,eAAerD,QAAQ2D,OAAO,EAAEL,OAAOG,OAAO,EAAEU,QAAQ,OAAO,GAAG;IACpF;AACER,gBAAUD,gBAAgB;QAAEU,UAAU;QAAOC,cAAc;MAAQ;EACvE;AAEA,SAAO,IAAIjB,KAAKC,eAAerD,QAAQ2D,OAAO,EAAEL,OAAOG,OAAO;AAChE;AAEA,SAASa,MAAOC,SAA2BnC,OAAa;AACtD,QAAM5B,QAAO+D,QAAQC,SAASpC,KAAK;AACnC,QAAMwB,OAAOpD,MAAKmB,YAAY;AAC9B,QAAMkC,QAAQY,SAASC,OAAOlE,MAAKoB,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG;AAC1D,QAAMF,MAAM+C,SAASC,OAAOlE,MAAKgB,QAAQ,CAAC,GAAG,GAAG,GAAG;AAEnD,SAAO,GAAGoC,IAAI,IAAIC,KAAK,IAAInC,GAAG;AAChC;AAEA,SAASiD,SAAUvC,OAAe;AAChC,QAAM,CAACwB,MAAMC,OAAOnC,GAAG,IAAIU,MAAME,MAAM,GAAG,EAAEC,IAAIC,MAAM;AAEtD,SAAO,IAAIlB,KAAKsC,MAAMC,QAAQ,GAAGnC,GAAG;AACtC;AAEA,SAASkD,WAAYpE,OAAYqE,QAAgB;AAC/C,QAAM7C,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAE8C,WAAW9C,EAAE+C,WAAW,IAAIF,MAAM;AACpC,SAAO7C;AACT;AAEA,SAASgD,SAAUxE,OAAYqE,QAAgB;AAC7C,QAAM7C,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAEiD,SAASjD,EAAEkD,SAAS,IAAIL,MAAM;AAChC,SAAO7C;AACT;AAEA,SAASmD,QAAS3E,OAAYqE,QAAgB;AAC5C,QAAM7C,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAET,QAAQS,EAAER,QAAQ,IAAIqD,MAAM;AAC9B,SAAO7C;AACT;AAEA,SAASoD,SAAU5E,OAAYqE,QAAgB;AAC7C,QAAM7C,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAET,QAAQS,EAAER,QAAQ,IAAKqD,SAAS,CAAE;AACpC,SAAO7C;AACT;AAEA,SAASqD,UAAW7E,OAAYqE,QAAgB;AAC9C,QAAM7C,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAET,QAAQ,CAAC;AACXS,IAAEsD,SAAStD,EAAEJ,SAAS,IAAIiD,MAAM;AAChC,SAAO7C;AACT;AAEA,SAASuD,QAAS/E,OAAY;AAC5B,SAAOA,MAAKmB,YAAY;AAC1B;AAEA,SAASC,SAAUpB,OAAY;AAC7B,SAAOA,MAAKoB,SAAS;AACvB;AAEA,SAAS4D,QAAShF,OAAYR,QAAgBS,gBAAyBgF,kBAA2B;AAChG,QAAMC,qBAAqB3F,SAASC,MAAM;AAC1C,QAAM2F,YAAYlF,mBAAkBiF,yDAAoBtF,aAAY;AACpE,QAAMwF,cAAcH,qBAAoBC,yDAAoBrF,kBAAiB;AAC7E,WAASA,cAAeuD,OAAc;AACpC,UAAMiC,aAAY,IAAIvE,KAAKsC,OAAM,GAAG,CAAC;AACrC,WAAO,IAAIkC,QAAQD,YAAW/D,YAAY+D,YAAW7F,QAAQ2F,SAAS,GAAG,MAAM;EACjF;AAEA,MAAI/B,OAAO2B,QAAQ/E,KAAI;AACvB,QAAMuF,iBAAiBZ,QAAQrD,YAAYtB,OAAMR,QAAQ2F,SAAS,GAAG,CAAC;AACtE,MAAI/B,OAAO2B,QAAQQ,cAAc,KAAK1F,cAAcuD,OAAO,CAAC,KAAKgC,aAAa;AAC5EhC;EACF;AAEA,QAAMiC,YAAY,IAAIvE,KAAKsC,MAAM,GAAG,CAAC;AACrC,QAAMoC,OAAO3F,cAAcuD,IAAI;AAC/B,QAAMqC,OAAOD,QAAQJ,cACjBT,QAAQU,WAAWG,OAAO,CAAC,IAC3Bb,QAAQU,WAAWG,IAAI;AAE3B,SAAO,IAAIF,QAAQI,SAAS1F,KAAI,GAAG2F,WAAWF,IAAI,GAAG,OAAO;AAC9D;AAEA,SAASzE,QAAShB,OAAY;AAC5B,SAAOA,MAAKgB,QAAQ;AACtB;AAEA,SAAS4E,aAAc5F,OAAY;AACjC,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAGnB,MAAKoB,SAAS,IAAI,GAAG,CAAC;AAC5D;AAEA,SAASyE,iBAAkB7F,OAAY;AACrC,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAGnB,MAAKoB,SAAS,IAAI,GAAG,CAAC;AAC5D;AAEA,SAASsD,SAAU1E,OAAY;AAC7B,SAAOA,MAAK0E,SAAS;AACvB;AAEA,SAASH,WAAYvE,OAAY;AAC/B,SAAOA,MAAKuE,WAAW;AACzB;AAEA,SAASuB,YAAa9F,OAAY;AAChC,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAG,GAAG,CAAC;AAC1C;AACA,SAAS4E,UAAW/F,OAAY;AAC9B,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAG,IAAI,EAAE;AAC5C;AAEA,SAAS6E,cAAehG,OAAYiG,OAAqB;AACvD,SAAOC,QAAQlG,OAAMiG,MAAM,CAAC,CAAC,KAAKE,SAASnG,OAAMiG,MAAM,CAAC,CAAC;AAC3D;AAEA,SAASG,QAASpG,OAAW;AAC3B,QAAMwB,IAAI,IAAIV,KAAKd,KAAI;AAEvB,SAAOwB,aAAaV,QAAQ,CAACuB,MAAMb,EAAE6E,QAAQ,CAAC;AAChD;AAEA,SAASH,QAASlG,OAAYsG,WAAiB;AAC7C,SAAOtG,MAAKqG,QAAQ,IAAIC,UAAUD,QAAQ;AAC5C;AAEA,SAASE,WAAYvG,OAAYsG,WAA0B;AACzD,SAAOJ,QAAQP,WAAW3F,KAAI,GAAG2F,WAAWW,SAAS,CAAC;AACxD;AAEA,SAASH,SAAUnG,OAAYsG,WAAiB;AAC9C,SAAOtG,MAAKqG,QAAQ,IAAIC,UAAUD,QAAQ;AAC5C;AAEA,SAASG,QAASxG,OAAYsG,WAAiB;AAC7C,SAAOtG,MAAKqG,QAAQ,MAAMC,UAAUD,QAAQ;AAC9C;AAEA,SAASI,UAAWzG,OAAYsG,WAAiB;AAC/C,SAAOtG,MAAKgB,QAAQ,MAAMsF,UAAUtF,QAAQ,KAC1ChB,MAAKoB,SAAS,MAAMkF,UAAUlF,SAAS,KACvCpB,MAAKmB,YAAY,MAAMmF,UAAUnF,YAAY;AACjD;AAEA,SAASuF,YAAa1G,OAAYsG,WAAiB;AACjD,SAAOtG,MAAKoB,SAAS,MAAMkF,UAAUlF,SAAS,KAC5CpB,MAAKmB,YAAY,MAAMmF,UAAUnF,YAAY;AACjD;AAEA,SAASwF,WAAY3G,OAAYsG,WAAiB;AAChD,SAAOtG,MAAKmB,YAAY,MAAMmF,UAAUnF,YAAY;AACtD;AAEA,SAASmE,QAAStF,OAAYsG,WAA0BM,MAAe;AACrE,QAAMpF,IAAI,IAAIV,KAAKd,KAAI;AACvB,QAAM6G,IAAI,IAAI/F,KAAKwF,SAAS;AAE5B,UAAQM,MAAI;IACV,KAAK;AACH,aAAOpF,EAAEL,YAAY,IAAI0F,EAAE1F,YAAY;IACzC,KAAK;AACH,aAAO2F,KAAKC,OAAOvF,EAAEJ,SAAS,IAAIyF,EAAEzF,SAAS,KAAKI,EAAEL,YAAY,IAAI0F,EAAE1F,YAAY,KAAK,MAAM,CAAC;IAChG,KAAK;AACH,aAAOK,EAAEJ,SAAS,IAAIyF,EAAEzF,SAAS,KAAKI,EAAEL,YAAY,IAAI0F,EAAE1F,YAAY,KAAK;IAC7E,KAAK;AACH,aAAO2F,KAAKC,OAAOvF,EAAE6E,QAAQ,IAAIQ,EAAER,QAAQ,MAAM,MAAO,KAAK,KAAK,KAAK,EAAE;IAC3E,KAAK;AACH,aAAOS,KAAKC,OAAOvF,EAAE6E,QAAQ,IAAIQ,EAAER,QAAQ,MAAM,MAAO,KAAK,KAAK,GAAG;IACvE,KAAK;AACH,aAAOS,KAAKC,OAAOvF,EAAE6E,QAAQ,IAAIQ,EAAER,QAAQ,MAAM,MAAO,KAAK,GAAG;IAClE,KAAK;AACH,aAAOS,KAAKC,OAAOvF,EAAE6E,QAAQ,IAAIQ,EAAER,QAAQ,MAAM,MAAO,GAAG;IAC7D,KAAK;AACH,aAAOS,KAAKC,OAAOvF,EAAE6E,QAAQ,IAAIQ,EAAER,QAAQ,KAAK,GAAI;IACtD,SAAS;AACP,aAAO7E,EAAE6E,QAAQ,IAAIQ,EAAER,QAAQ;IACjC;EACF;AACF;AAEA,SAAS5B,SAAUzE,OAAYgH,OAAe;AAC5C,QAAMxF,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAEiD,SAASuC,KAAK;AAChB,SAAOxF;AACT;AAEA,SAAS8C,WAAYtE,OAAYgH,OAAe;AAC9C,QAAMxF,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAE8C,WAAW0C,KAAK;AAClB,SAAOxF;AACT;AAEA,SAASsD,SAAU9E,OAAYgH,OAAe;AAC5C,QAAMxF,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAEsD,SAASkC,KAAK;AAChB,SAAOxF;AACT;AAEA,SAAST,QAASf,OAAYkB,KAAa;AACzC,QAAMM,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAET,QAAQG,GAAG;AACb,SAAOM;AACT;AAEA,SAASyF,QAASjH,OAAYoD,MAAc;AAC1C,QAAM5B,IAAI,IAAIV,KAAKd,KAAI;AACvBwB,IAAE0F,YAAY9D,IAAI;AAClB,SAAO5B;AACT;AAEA,SAASmE,WAAY3F,OAAY;AAC/B,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAGnB,MAAKoB,SAAS,GAAGpB,MAAKgB,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;AACjF;AAEA,SAAS0E,SAAU1F,OAAY;AAC7B,SAAO,IAAIc,KAAKd,MAAKmB,YAAY,GAAGnB,MAAKoB,SAAS,GAAGpB,MAAKgB,QAAQ,GAAG,IAAI,IAAI,IAAI,GAAG;AACtF;AAEO,IAAMmG,qBAAN,MAAsD;EAI3DC,YAAajE,SAAyE;AACpF,SAAK3D,SAAS2D,QAAQ3D;AACtB,SAAKwD,UAAUG,QAAQH;EACzB;EAEAhD,KAAM4B,OAAa;AACjB,WAAO5B,KAAK4B,KAAK;EACnB;EAEAoC,SAAUhE,OAAY;AACpB,WAAOA;EACT;EAEA8D,MAAO9D,OAAoB;AACzB,WAAO8D,MAAM,MAAM9D,KAAI;EACzB;EAEAmE,SAAUnE,OAAc;AACtB,WAAOmE,SAASnE,KAAI;EACtB;EAEAoE,WAAYpE,OAAYqE,QAAgB;AACtC,WAAOD,WAAWpE,OAAMqE,MAAM;EAChC;EAEAG,SAAUxE,OAAYqE,QAAgB;AACpC,WAAOG,SAASxE,OAAMqE,MAAM;EAC9B;EAEAM,QAAS3E,OAAYqE,QAAgB;AACnC,WAAOM,QAAQ3E,OAAMqE,MAAM;EAC7B;EAEAO,SAAU5E,OAAYqE,QAAgB;AACpC,WAAOO,SAAS5E,OAAMqE,MAAM;EAC9B;EAEAQ,UAAW7E,OAAYqE,QAAgB;AACrC,WAAOQ,UAAU7E,OAAMqE,MAAM;EAC/B;EAEAtE,aAAcC,OAAYC,gBAAkC;AAC1D,UAAML,WAAWK,mBAAmBoH,SAAYrF,OAAO/B,cAAc,IAAIoH;AACzE,WAAOtH,aAAaC,OAAM,KAAKR,QAAQI,QAAQ;EACjD;EAEA0B,YAAatB,OAAYC,gBAAwC;AAC/D,UAAML,WAAWK,mBAAmBoH,SAAYrF,OAAO/B,cAAc,IAAIoH;AACzE,WAAO/F,YAAYtB,OAAM,KAAKR,QAAQI,QAAQ;EAChD;EAEA6B,UAAWzB,OAAkB;AAC3B,WAAOyB,UAAUzB,OAAM,KAAKR,MAAM;EACpC;EAEAa,aAAcL,OAAY;AACxB,WAAOK,aAAaL,KAAI;EAC1B;EAEAO,WAAYP,OAAY;AACtB,WAAOO,WAAWP,KAAI;EACxB;EAEA8C,OAAQ9C,OAAY+C,cAAsB;AACxC,WAAOD,OAAO9C,OAAM+C,cAAc,KAAKvD,QAAQ,KAAKwD,OAAO;EAC7D;EAEAwD,QAASxG,OAAYsG,WAAiB;AACpC,WAAOE,QAAQxG,OAAMsG,SAAS;EAChC;EAEAF,QAASpG,OAAW;AAClB,WAAOoG,QAAQpG,KAAI;EACrB;EAEAgG,cAAehG,OAAYiG,OAAqB;AAC9C,WAAOD,cAAchG,OAAMiG,KAAK;EAClC;EAEAC,QAASlG,OAAYsG,WAAiB;AACpC,WAAOJ,QAAQlG,OAAMsG,SAAS;EAChC;EAEAC,WAAYvG,OAAYsG,WAAiB;AACvC,WAAOC,WAAWvG,OAAMsG,SAAS;EACnC;EAEAH,SAAUnG,OAAYsG,WAAiB;AACrC,WAAO,CAACJ,QAAQlG,OAAMsG,SAAS,KAAK,CAACE,QAAQxG,OAAMsG,SAAS;EAC9D;EAEAG,UAAWzG,OAAYsG,WAAiB;AACtC,WAAOG,UAAUzG,OAAMsG,SAAS;EAClC;EAEAI,YAAa1G,OAAYsG,WAAiB;AACxC,WAAOI,YAAY1G,OAAMsG,SAAS;EACpC;EAEAK,WAAY3G,OAAYsG,WAAiB;AACvC,WAAOK,WAAW3G,OAAMsG,SAAS;EACnC;EAEAhC,WAAYtE,OAAYgH,OAAe;AACrC,WAAO1C,WAAWtE,OAAMgH,KAAK;EAC/B;EAEAvC,SAAUzE,OAAYgH,OAAe;AACnC,WAAOvC,SAASzE,OAAMgH,KAAK;EAC7B;EAEAlC,SAAU9E,OAAYgH,OAAe;AACnC,WAAOlC,SAAS9E,OAAMgH,KAAK;EAC7B;EAEAjG,QAASf,OAAYkB,KAAmB;AACtC,WAAOH,QAAQf,OAAMkB,GAAG;EAC1B;EAEA+F,QAASjH,OAAYoD,MAAc;AACjC,WAAO6D,QAAQjH,OAAMoD,IAAI;EAC3B;EAEAkC,QAAStF,OAAYsG,WAA0BM,MAAe;AAC5D,WAAOtB,QAAQtF,OAAMsG,WAAWM,IAAI;EACtC;EAEArE,YAAatC,gBAAkCuC,eAA6C;AAC1F,UAAM5C,WAAWK,mBAAmBoH,SAAYrF,OAAO/B,cAAc,IAAIoH;AACzE,WAAO9E,YAAY,KAAK/C,QAAQI,UAAU4C,aAAa;EACzD;EAEAuC,QAAS/E,OAAY;AACnB,WAAO+E,QAAQ/E,KAAI;EACrB;EAEAoB,SAAUpB,OAAY;AACpB,WAAOoB,SAASpB,KAAI;EACtB;EAEAgF,QAAShF,OAAYC,gBAAkCgF,kBAA2B;AAChF,UAAMrF,WAAWK,mBAAmBoH,SAAYrF,OAAO/B,cAAc,IAAIoH;AACzE,WAAOrC,QAAQhF,OAAM,KAAKR,QAAQI,UAAUqF,gBAAgB;EAC9D;EAEAjE,QAAShB,OAAY;AACnB,WAAOgB,QAAQhB,KAAI;EACrB;EAEA4F,aAAc5F,OAAY;AACxB,WAAO4F,aAAa5F,KAAI;EAC1B;EAEA6F,iBAAkB7F,OAAY;AAC5B,WAAO6F,iBAAiB7F,KAAI;EAC9B;EAEA0E,SAAU1E,OAAY;AACpB,WAAO0E,SAAS1E,KAAI;EACtB;EAEAuE,WAAYvE,OAAY;AACtB,WAAOuE,WAAWvE,KAAI;EACxB;EAEA2F,WAAY3F,OAAY;AACtB,WAAO2F,WAAW3F,KAAI;EACxB;EAEA0F,SAAU1F,OAAY;AACpB,WAAO0F,SAAS1F,KAAI;EACtB;EAEA8F,YAAa9F,OAAY;AACvB,WAAO8F,YAAY9F,KAAI;EACzB;EAEA+F,UAAW/F,OAAY;AACrB,WAAO+F,UAAU/F,KAAI;EACvB;AACF;;;AChnBO,IAAMsH,oBAAuDC,OAAOC,IAAI,sBAAsB;AAC9F,IAAMC,oBAAgDF,OAAOC,IAAI,sBAAsB;AAEvF,SAASE,WAAYC,SAAkCC,QAAwB;AACpF,QAAMC,WAAWC,UAAU;IACzBC,SAASC;IACTJ,QAAQ;MACNK,IAAI;;MAEJC,IAAI;MACJC,IAAI;MACJC,KAAK;MACLC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;;MAEJC,IAAI;MACJC,IAAI;MACJC,IAAI;;MAEJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,QAAQ;MACRC,QAAQ;MACRC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,QAAQ;MACRC,QAAQ;IACV;EACF,GAAG3C,OAAO;AAEV,SAAO;IACLA,SAASE;IACT0C,UAAUC,eAAe3C,UAAUD,MAAM;EAC3C;AACF;AAsBA,SAAS6C,eAAgBC,SAA8BC,QAAwB;AAC7E,QAAMC,WAAWC,SACf,OAAOH,QAAQI,YAAY,aAEvB,IAAIJ,QAAQI,QAAQ;IACpBH,QAAQD,QAAQC,OAAOA,OAAOI,QAAQC,KAAK,KAAKL,OAAOI,QAAQC;IAC/DC,SAASP,QAAQO;EACnB,CAAC,IACCP,QAAQI,OACd;AAEAI,QAAMP,OAAOI,SAASC,WAAS;AAC7BJ,aAASD,SAASD,QAAQC,OAAOK,KAAK,KAAKA,SAASJ,SAASD;EAC/D,CAAC;AAED,SAAOC;AACT;AAEO,SAASO,UAAyB;AACvC,QAAMT,UAAUU,OAAOC,iBAAiB;AAExC,MAAI,CAACX,QAAS,OAAM,IAAIY,MAAM,gDAAgD;AAE9E,QAAMX,SAASY,UAAU;AAEzB,SAAOd,eAAeC,SAASC,MAAM;AACvC;;;ACtDO,IAAMa,gBAA+CC,OAAOC,IAAI,iBAAiB;AAExF,IAAMC,wBAAwC;EAC5CC,kBAAkB;EAClBC,YAAY;IACVC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,KAAK;EACP;AACF;AAEA,IAAMC,sBAAsB,WAAqD;AAAA,MAApDC,UAAuBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGX;AACrD,SAAOc,UAAUd,uBAAuBU,OAAO;AACjD;AAEA,SAASK,eAAgBC,KAAkB;AACzC,SAAOC,cAAc,CAACD,MAClBE,OAAOC,aACN,OAAOH,QAAQ,YAAYA,IAAII,eAAgB;AACtD;AAEA,SAASC,gBAAiBL,KAAkB;AAC1C,SAAOC,cAAc,CAACD,MAClBE,OAAOI,cACN,OAAON,QAAQ,YAAYA,IAAIO,gBAAiB;AACvD;AAEA,SAASC,YAAaR,KAAmC;AACvD,QAAMS,YAAYR,cAAc,CAACD,MAC7BE,OAAOQ,UAAUD,YACjB;AAEJ,WAASE,MAAOC,QAAgB;AAC9B,WAAOC,QAAQJ,UAAUE,MAAMC,MAAM,CAAC;EACxC;AAEA,QAAME,UAAUH,MAAM,UAAU;AAChC,QAAMI,MAAMJ,MAAM,mBAAmB;AACrC,QAAMK,UAAUL,MAAM,UAAU;AAChC,QAAMM,WAAWN,MAAM,WAAW;AAClC,QAAMO,SAASP,MAAM,SAAS;AAC9B,QAAMQ,OAAOR,MAAM,OAAO;AAC1B,QAAMS,UAAUT,MAAM,UAAU;AAChC,QAAMU,QAAQV,MAAM,QAAQ;AAC5B,QAAMW,MAAMX,MAAM,MAAM;AACxB,QAAMY,MAAMZ,MAAM,MAAM;AACxB,QAAMa,QAAQb,MAAM,QAAQ;AAE5B,SAAO;IACLG;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC,OAAOC;IACP1B,KAAKS,cAAc;EACrB;AACF;AAEO,SAASkB,cAAejC,SAA0BM,KAAmC;AAC1F,QAAM;IAAEd;IAAYD;EAAiB,IAAIQ,oBAAoBC,OAAO;AAEpE,QAAMkC,SAASC,WAAWxB,gBAAgBL,GAAG,CAAC;AAC9C,QAAM8B,WAAWD,WAAWrB,YAAYR,GAAG,CAAC;AAC5C,QAAM+B,QAAQC,SAAS,CAAC,CAAoB;AAC5C,QAAMC,QAAQJ,WAAW9B,eAAeC,GAAG,CAAC;AAE5C,WAASkC,aAAc;AACrBN,WAAOO,QAAQ9B,gBAAgB;AAC/B4B,UAAME,QAAQpC,eAAe;EAC/B;AACA,WAASqC,SAAU;AACjBF,eAAW;AACXJ,aAASK,QAAQ3B,YAAY;EAC/B;AAGA6B,cAAY,MAAM;AAChB,UAAMlD,KAAK8C,MAAME,QAAQjD,WAAWE;AACpC,UAAMA,KAAK6C,MAAME,QAAQjD,WAAWG,MAAM,CAACF;AAC3C,UAAME,KAAK4C,MAAME,QAAQjD,WAAWI,MAAM,EAAEF,MAAMD;AAClD,UAAMG,KAAK2C,MAAME,QAAQjD,WAAWK,MAAM,EAAEF,MAAMD,MAAMD;AACxD,UAAMI,KAAK0C,MAAME,QAAQjD,WAAWM,OAAO,EAAEF,MAAMD,MAAMD,MAAMD;AAC/D,UAAMK,MAAMyC,MAAME,SAASjD,WAAWM;AACtC,UAAM8C,OACJnD,KAAK,OACHC,KAAK,OACLC,KAAK,OACLC,KAAK,OACLC,KAAK,OACL;AACJ,UAAMgD,kBAAkB,OAAOtD,qBAAqB,WAAWA,mBAAmBC,WAAWD,gBAAgB;AAC7G,UAAMuD,SAASP,MAAME,QAAQI;AAE7BR,UAAM5C,KAAKA;AACX4C,UAAM3C,KAAKA;AACX2C,UAAM1C,KAAKA;AACX0C,UAAMzC,KAAKA;AACXyC,UAAMxC,KAAKA;AACXwC,UAAMvC,MAAMA;AACZuC,UAAMU,UAAU,CAACtD;AACjB4C,UAAMW,UAAU,EAAEvD,MAAMC;AACxB2C,UAAMY,UAAU,EAAExD,MAAMC,MAAMC;AAC9B0C,UAAMa,UAAU,EAAEzD,MAAMC,MAAMC,MAAMC;AACpCyC,UAAMc,YAAY,EAAExD,MAAMC,MAAMC,MAAMC;AACtCuC,UAAMe,YAAY,EAAExD,MAAMC,MAAMC;AAChCuC,UAAMgB,YAAY,EAAExD,MAAMC;AAC1BuC,UAAMiB,YAAY,CAACxD;AACnBuC,UAAMO,OAAOA;AACbP,UAAMH,SAASA,OAAOO;AACtBJ,UAAME,QAAQA,MAAME;AACpBJ,UAAMS,SAASA;AACfT,UAAM9C,mBAAmBA;AACzB8C,UAAMD,WAAWA,SAASK;AAC1BJ,UAAM7C,aAAaA;EACrB,CAAC;AAED,MAAIe,YAAY;AACdC,WAAO+C,iBAAiB,UAAUf,YAAY;MAAEgB,SAAS;IAAK,CAAC;AAE/DC,mBAAe,MAAM;AACnBjD,aAAOkD,oBAAoB,UAAUlB,UAAU;IACjD,GAAG,IAAI;EACT;AAEA,SAAO;IAAE,GAAGmB,OAAOtB,KAAK;IAAGK;IAAQpC,KAAK,CAAC,CAACA;EAAI;AAChD;AAEO,IAAMsD,mBAAmBC,aAAa;EAC3Cf,QAAQ;IACNgB,MAAM3C;IACN4C,SAAS;EACX;EACAxE,kBAAkB,CAACyE,QAAQC,MAAM;AACnC,GAAG,SAAS;AAEL,SAASC,aAGd;AAAA,MAFAC,QAAmBlE,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;IAAE6C,QAAQ;EAAK;AAAC,MACtCF,OAAI3C,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGmE,uBAAuB;AAE9B,QAAMC,UAAUC,OAAOnF,aAAa;AAEpC,MAAI,CAACkF,QAAS,OAAM,IAAIE,MAAM,0CAA0C;AAExE,QAAMzB,SAAS0B,SAAS,MAAM;AAC5B,QAAIL,MAAMrB,QAAQ;AAChB,aAAO;IACT,WAAW,OAAOqB,MAAM5E,qBAAqB,UAAU;AACrD,aAAO8E,QAAQ9B,MAAME,QAAQ0B,MAAM5E;IACrC,WAAW4E,MAAM5E,kBAAkB;AACjC,aAAO8E,QAAQ9B,MAAME,QAAQ4B,QAAQ7E,WAAWiD,MAAM0B,MAAM5E,gBAAgB;IAC9E,WAAW4E,MAAMrB,WAAW,MAAM;AAChC,aAAOuB,QAAQvB,OAAOL;IACxB,OAAO;AACL,aAAO;IACT;EACF,CAAC;AAED,QAAMgC,iBAAiBC,MAAM,MAAM;AACjC,QAAI,CAAC9B,KAAM,QAAO,CAAC;AAEnB,WAAO;MAAE,CAAC,GAAGA,IAAI,UAAU,GAAGE,OAAOL;IAAM;EAC7C,CAAC;AAED,SAAO;IAAE,GAAG4B;IAASI;IAAgB3B;EAAO;AAC9C;;;ACvOO,IAAM6B,aAAyCC,OAAOC,IAAI,cAAc;AAE/E,SAASC,eAAe;AACtB,SAAO;IACLC,WAAWC;IACXC,UAAU;IACVC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,UAAUC;EACZ;AACF;AAEA,SAASC,aAAcC,IAAqD;AAC1E,SAAOC,UAAUD,EAAE,MAAME,SAASC,oBAAoBD,SAASE;AACjE;AAEA,SAASH,UAAWD,IAAgE;AAClF,SAAQ,OAAOA,OAAO,WAAYE,SAASG,cAA2BL,EAAE,IAAIM,WAAWN,EAAE;AAC3F;AAEA,SAASO,UAAWC,QAAaC,YAAsBC,KAAuB;AAC5E,MAAI,OAAOF,WAAW,SAAU,QAAOC,cAAcC,MAAM,CAACF,SAASA;AAErE,MAAIR,KAAKC,UAAUO,MAAM;AACzB,MAAIG,cAAc;AAClB,SAAOX,IAAI;AACTW,mBAAeF,aAAaT,GAAGY,aAAaZ,GAAGa;AAC/Cb,SAAKA,GAAGc;EACV;AAEA,SAAOH;AACT;AAEO,SAASI,WACdC,SACAC,QACc;AACd,SAAO;IACLP,KAAKO,OAAOC;IACZF,SAASG,UAAU7B,aAAY,GAAG0B,OAAO;EAC3C;AACF;AAEA,eAAsBI,SACpBC,SACAC,UACAb,YACAc,MACA;AACA,QAAMC,WAAWf,aAAa,eAAe;AAC7C,QAAMO,UAAUG,WAAUI,6BAAMP,YAAW1B,aAAY,GAAGgC,QAAQ;AAClE,QAAMZ,MAAMa,6BAAMb,IAAIe;AACtB,QAAMjB,UAAU,OAAOa,YAAY,WAAWA,UAAUpB,UAAUoB,OAAO,MAAM;AAC/E,QAAM9B,YAAYyB,QAAQzB,cAAc,YAAYiB,kBAAkBkB,cAClElB,OAAOmB,gBACP5B,aAAaiB,QAAQzB,SAAS;AAClC,QAAMqC,OAAO,OAAOZ,QAAQpB,WAAW,aAAaoB,QAAQpB,SAASoB,QAAQnB,SAASmB,QAAQpB,MAAM;AAEpG,MAAI,CAACgC,KAAM,OAAM,IAAIC,UAAU,oBAAoBb,QAAQpB,MAAM,cAAc;AAE/E,MAAIkC;AACJ,MAAI,OAAOtB,WAAW,UAAU;AAC9BsB,qBAAiBvB,UAAUC,QAAQC,YAAYC,GAAG;EACpD,OAAO;AACLoB,qBAAiBvB,UAAUC,QAAQC,YAAYC,GAAG,IAAIH,UAAUhB,WAAWkB,YAAYC,GAAG;AAE1F,QAAIM,QAAQtB,QAAQ;AAClB,YAAMqC,SAASC,OAAOC,iBAAiBzB,MAAM;AAC7C,YAAM0B,eAAeH,OAAOI,iBAAiB,gBAAgB;AAE7D,UAAID,aAAcJ,mBAAkBM,SAASF,cAAc,EAAE;IAC/D;EACF;AAEAJ,oBAAkBd,QAAQrB;AAC1BmC,mBAAiBO,YAAY9C,WAAWuC,gBAAgB,CAAC,CAACpB,KAAK,CAAC,CAACD,UAAU;AAE3E,QAAM6B,gBAAgB/C,UAAUiC,QAAQ,KAAK;AAE7C,MAAIM,mBAAmBQ,cAAe,QAAOC,QAAQC,QAAQV,cAAc;AAE3E,QAAMW,YAAYC,YAAYC,IAAI;AAElC,SAAO,IAAIJ,QAAQC,aAAWI,sBAAsB,SAASC,KAAMC,aAAqB;AACtF,UAAMC,cAAcD,cAAcL;AAClC,UAAMO,WAAWD,cAAc/B,QAAQvB;AACvC,UAAMwD,WAAWC,KAAKC,MACpBb,iBACCR,iBAAiBQ,iBAClBV,KAAKwB,MAAMJ,UAAU,GAAG,CAAC,CAAC,CAC5B;AAEAzD,cAAUiC,QAAQ,IAAIyB;AAGtB,QAAID,YAAY,KAAKE,KAAKG,IAAIJ,WAAW1D,UAAUiC,QAAQ,CAAC,IAAI,IAAI;AAClE,aAAOgB,QAAQV,cAAc;IAC/B,WAAWkB,WAAW,GAAG;AAEvBM,kBAAY,gCAAgC;AAC5C,aAAOd,QAAQjD,UAAUiC,QAAQ,CAAC;IACpC;AAEAoB,0BAAsBC,IAAI;EAC5B,CAAC,CAAC;AACJ;AAEO,SAASU,UAAqC;AAAA,MAA5BjC,WAAqBkC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAhE,SAAAgE,UAAA,CAAA,IAAG,CAAC;AAChD,QAAME,eAAeC,OAAOxE,UAAU;AACtC,QAAM;IAAE+B;EAAM,IAAI0C,OAAO;AAEzB,MAAI,CAACF,aAAc,OAAM,IAAIG,MAAM,iDAAiD;AAEpF,QAAMtC,OAAO;IACX,GAAGmC;;IAEHhD,KAAKoD,MAAM,MAAMJ,aAAahD,IAAIe,SAASP,MAAMO,KAAK;EACxD;AAEA,iBAAesC,GACbvD,QACAQ,SACA;AACA,WAAOI,SAASZ,QAAQW,UAAUG,UAAUN,OAAO,GAAG,OAAOO,IAAI;EACnE;AAEAwC,KAAGtD,aAAa,OACdD,QACAQ,YACG;AACH,WAAOI,SAASZ,QAAQW,UAAUG,UAAUN,OAAO,GAAG,MAAMO,IAAI;EAClE;AAEA,SAAOwC;AACT;AAMA,SAAS1B,YACP9C,WACAkC,OACAf,KACAD,YACA;AACA,QAAM;IAAEuD;IAAaC;EAAa,IAAI1E;AACtC,QAAM,CAAC2E,gBAAgBC,eAAe,IAAI5E,cAAcW,SAASC,mBAC7D,CAAC6B,OAAOoC,YAAYpC,OAAOqC,WAAW,IACtC,CAAC9E,UAAU+E,aAAa/E,UAAUgF,YAAY;AAElD,MAAIC;AACJ,MAAIC;AAEJ,MAAIhE,YAAY;AACd,QAAIC,KAAK;AACP8D,YAAM,EAAER,cAAcE;AACtBO,YAAM;IACR,OAAO;AACLD,YAAM;AACNC,YAAMT,cAAcE;IACtB;EACF,OAAO;AACLM,UAAM;AACNC,UAAMR,eAAe,CAACE;EACxB;AAEA,SAAOf,MAAM3B,OAAO+C,KAAKC,GAAG;AAC9B;;;AC1LA,IAAMC,UAAuB;EAC3BC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,QAAQ;;EACRC,OAAO;EACPC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,MAAM;EACNC,YAAY;EACZC,aAAa;EACbC,uBAAuB;EACvBC,WAAW;;EACXC,SAAS;EACTC,UAAU;EACVC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,UAAU;EACVC,SAAS;EACTC,UAAU;EACVC,MAAM;EACNC,aAAa;EACbC,YAAY;EACZC,YAAY;EACZC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,UAAU;EACVC,kBAAkB;EAClBC,gBAAgB;EAChBC,YAAY;EACZC,QAAQ;EACRC,OAAO;EACPC,SAAS;EACTC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC,KAAK;EACLC,OAAO;EACPC,SAAS;EACTC,WAAW;EACXC,WAAW;EACXC,YAAY;EACZC,WAAW;EACXC,MAAM;EACNC,OAAO;EACPC,YAAY;EACZC,gBAAgB;EAChBC,YAAY;EACZC,cAAc;EACdC,WAAW;EACXC,WAAW;AACb;AAEA,IAAMC,MAAe;;EAEnBC,WAAYC,WAAeC,EAAEC,YAAY;IAAE,GAAGF;IAAOG,OAAO;EAAM,CAAC;AACrE;;;AC5DO,IAAMC,YAAY,CAACC,QAAQC,UAAUC,QAAQC,KAAK;AAoFlD,IAAMC,aAAgDC,OAAOC,IAAI,eAAe;AAEhF,IAAMC,gBAAgBC,aAAa;EACxCC,MAAM;IACJC,MAAMX;EACR;;EAEAY,KAAK;IACHD,MAAM,CAACV,QAAQE,QAAQD,QAAQ;IAC/BW,UAAU;EACZ;AACF,GAAG,MAAM;AAEF,IAAMC,iBAAiBC,iBAAiB,EAAE;EAC/CC,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAKE,MAAa;AAAA,QAAX;MAAEC;IAAM,IAACD;AACrB,WAAO,MAAM;AACX,YAAME,OAAOJ,MAAMP;AACnB,aAAAY,YAAAL,MAAAL,KAAA,MAAA;QAAAW,SAAAA,MAAA;;AAAA,kBAEMN,MAAMP,OAAIY,YAAAD,MAAA,MAAA,IAAA,KAAcD,WAAMG,YAANH,8BAAiB;;MAAA,CAAA;IAGjD;EACF;AACF,CAAC;AAGM,IAAMI,WAAWC,iBAAgB;EACtCT,MAAM;EAENU,cAAc;EAEdT,OAAOT,cAAc;EAErBU,MAAOD,OAAKU,OAAa;AAAA,QAAX;MAAEC;IAAM,IAACD;AACrB,WAAO,MAAM;AACX,aAAAL,YAAAL,MAAAL,KAAAiB,WACkBD,OAAK;QAAA,SAAW;MAAI,CAAA,GAAA;QAAAL,SAAAA,MAAA,CAAAO,gBAAA,OAAA;UAAA,SAAA;UAAA,SAAA;UAAA,WAAA;UAAA,QAAA;UAAA,eAAA;QAAA,GAAA,CAQ9B1B,MAAM2B,QAAQd,MAAMP,IAAI,IACtBO,MAAMP,KAAKsB,IAAIC,UACf7B,MAAM2B,QAAQE,IAAI,IAACH,gBAAA,QAAA;UAAA,KACLG,KAAK,CAAC;UAAC,gBAA4BA,KAAK,CAAC;QAAC,GAAA,IAAA,IAAAH,gBAAA,QAAA;UAAA,KAC1CG;QAAI,GAAA,IAAA,CACnB,IAACH,gBAAA,QAAA;UAAA,KACUb,MAAMP;QAAI,GAAA,IAAA,CAAoB,CAAA,CAAA;MAAA,CAAA;IAKpD;EACF;AACF,CAAC;AAGM,IAAMwB,gBAAgBT,iBAAgB;EAC3CT,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAO;AACZ,WAAO,MAAM;AACX,aAAAK,YAAAL,MAAAL,KAAA,MAAA;QAAAW,SAAAA,MAAA,CAAoBN,MAAMP,IAAI;MAAA,CAAA;IAChC;EACF;AACF,CAAC;AAGM,IAAMyB,aAAaV,iBAAgB;EACxCT,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAO;AACZ,WAAO,MAAM;AACX,aAAAK,YAAAL,MAAAL,KAAA;QAAA,SAAAwB,eAA0BnB,MAAMP,IAAI;MAAA,GAAA,IAAA;IACtC;EACF;AACF,CAAC;AAGD,SAAS2B,eAAwC;AAC/C,SAAO;IACLC,KAAK;MACHC,WAAWf;IACb;IACAgB,OAAO;MACLD,WAAWJ;IACb;EACF;AACF;AAGO,SAASM,YAAaC,SAAuB;AAClD,QAAMC,OAAON,aAAY;AACzB,QAAMO,cAAaF,mCAASE,eAAc;AAE1C,MAAIA,eAAe,SAAS,CAACD,KAAKE,KAAK;AACrCF,SAAKE,MAAMA;EACb;AAEA,SAAOC,UAAU;IACfF;IACAD;IACAI,SAAS;MACP,GAAGA;;MAEHC,SAAS,CACP,sDACA,CAAC,0FAA0F,GAAG,CAAC;MAEjG,mBAAmB;MACnB,gBAAgB,CACd,wYACA,CAAC,sdAAsd,GAAG,CAAC;;IAG/d;EACF,GAAGN,OAAO;AACZ;;;ACzGO,IAAMO,cAA2CC,OAAOC,IAAI,eAAe;AAE3E,IAAMC,iBAAiBC,aAAa;EACzCC,OAAOC;AACT,GAAG,OAAO;AAEV,SAASC,eAAe;AACtB,SAAO;IACLC,cAAc;IACdC,QAAQ;IACRC,YAAY;MAAEC,QAAQ,CAAA;MAAIC,SAAS;MAAGC,QAAQ;IAAE;IAChDC,QAAQ;MACNC,OAAO;QACLC,MAAM;QACNL,QAAQ;UACNM,YAAY;UACZC,SAAS;UACT,kBAAkB;UAClB,iBAAiB;UACjB,mBAAmB;UACnB,sBAAsB;UACtBC,SAAS;UACT,oBAAoB;UACpBC,WAAW;UACX,sBAAsB;UACtBC,OAAO;UACPC,MAAM;UACNC,SAAS;UACTC,SAAS;QACX;QACAC,WAAW;UACT,gBAAgB;UAChB,kBAAkB;UAClB,yBAAyB;UACzB,2BAA2B;UAC3B,oBAAoB;UACpB,gBAAgB;UAChB,iBAAiB;UACjB,iBAAiB;UACjB,oBAAoB;UACpB,qBAAqB;UACrB,mBAAmB;UACnB,mBAAmB;UACnB,aAAa;UACb,gBAAgB;UAChB,cAAc;UACd,iBAAiB;QACnB;MACF;MACAT,MAAM;QACJA,MAAM;QACNL,QAAQ;UACNM,YAAY;UACZC,SAAS;UACT,kBAAkB;UAClB,iBAAiB;UACjB,mBAAmB;UACnB,sBAAsB;UACtBC,SAAS;UACT,oBAAoB;UACpBC,WAAW;UACX,sBAAsB;UACtBC,OAAO;UACPC,MAAM;UACNC,SAAS;UACTC,SAAS;QACX;QACAC,WAAW;UACT,gBAAgB;UAChB,kBAAkB;UAClB,yBAAyB;UACzB,2BAA2B;UAC3B,oBAAoB;UACpB,gBAAgB;UAChB,iBAAiB;UACjB,iBAAiB;UACjB,oBAAoB;UACpB,qBAAqB;UACrB,mBAAmB;UACnB,mBAAmB;UACnB,aAAa;UACb,gBAAgB;UAChB,cAAc;UACd,iBAAiB;QACnB;MACF;IACF;IACAC,cAAc;IACdC,QAAQ;IACRC,aAAa;IACbC,WAAW;EACb;AACF;AAEA,SAASC,oBAAgF;AAzNzF;AAyNyF,MAA7DC,UAAqBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGzB,aAAY;AAC9D,QAAM4B,WAAW5B,aAAY;AAE7B,MAAI,CAACwB,QAAS,QAAO;IAAE,GAAGI;IAAUC,YAAY;EAAK;AAErD,QAAMtB,SAAkD,CAAC;AACzD,aAAW,CAACuB,KAAKhC,KAAK,KAAKiC,OAAOC,QAAQR,QAAQjB,UAAU,CAAC,CAAC,GAAG;AAC/D,UAAMN,eAAeH,MAAMW,QAAQqB,QAAQ,UACvCF,cAASrB,WAATqB,mBAAiBnB,QACjBmB,cAASrB,WAATqB,mBAAiBpB;AACrBD,WAAOuB,GAAG,IAAIG,UAAUhC,cAAcH,KAAK;EAC7C;AAEA,SAAOmC,UACLL,UACA;IAAE,GAAGJ;IAASjB;EAAO,CACvB;AACF;AAEA,SAAS2B,eAAgBC,OAAiBC,UAAkBC,SAAmBC,OAAgB;AAC7FH,QAAMI,KACJ,GAAGC,kBAAkBJ,UAAUE,KAAK,CAAC;GACrC,GAAGD,QAAQI,IAAIC,UAAQ,KAAKA,IAAI;CAAK,GACrC,KACF;AACF;AAEA,SAASC,gBAAiB7C,OAAgCI,QAAgB;AACxE,QAAM0C,eAAe9C,MAAMW,OAAO,IAAI;AACtC,QAAMoC,cAAc/C,MAAMW,OAAO,IAAI;AAErC,QAAMS,YAAsB,CAAA;AAC5B,aAAW,CAACY,KAAKgB,KAAK,KAAKf,OAAOC,QAAQlC,MAAMM,MAAM,GAAG;AACvD,UAAM2C,MAAMC,WAAWF,KAAK;AAC5B5B,cAAUqB,KAAK,KAAKrC,MAAM,SAAS4B,GAAG,KAAKiB,IAAIE,CAAC,IAAIF,IAAIG,CAAC,IAAIH,IAAII,CAAC,EAAE;AACpE,QAAI,CAACrB,IAAIsB,WAAW,KAAK,GAAG;AAC1BlC,gBAAUqB,KAAK,KAAKrC,MAAM,SAAS4B,GAAG,wBAAwBuB,QAAQP,KAAK,IAAI,OAAOF,eAAeC,WAAW,EAAE;IACpH;EACF;AAEA,aAAW,CAACf,KAAKgB,KAAK,KAAKf,OAAOC,QAAQlC,MAAMoB,SAAS,GAAG;AAC1D,UAAMoC,QAAQ,OAAOR,UAAU,YAAYA,MAAMM,WAAW,GAAG,IAAIJ,WAAWF,KAAK,IAAInB;AACvF,UAAMoB,MAAMO,QAAQ,GAAGA,MAAML,CAAC,KAAKK,MAAMJ,CAAC,KAAKI,MAAMH,CAAC,KAAKxB;AAC3DT,cAAUqB,KAAK,KAAKrC,MAAM,GAAG4B,GAAG,KAAKiB,OAAOD,KAAK,EAAE;EACrD;AAEA,SAAO5B;AACT;AAEA,SAASqC,aAAcC,MAAcF,OAAenD,YAAuC;AACzF,QAAMsD,SAAiC,CAAC;AACxC,MAAItD,YAAY;AACd,eAAWuD,aAAc,CAAC,WAAW,QAAQ,GAAa;AACxD,YAAMC,KAAKD,cAAc,YAAYrD,UAAUC;AAC/C,iBAAWsD,UAAUC,YAAY1D,WAAWuD,SAAS,GAAG,CAAC,GAAG;AAC1DD,eAAO,GAAGD,IAAI,IAAIE,SAAS,IAAIE,MAAM,EAAE,IAAIE,SAASH,GAAGX,WAAWM,KAAK,GAAGM,MAAM,CAAC;MACnF;IACF;EACF;AACA,SAAOH;AACT;AAEA,SAASM,cAAe3D,QAA2CD,YAAuC;AACxG,MAAI,CAACA,WAAY,QAAO,CAAC;AAEzB,MAAI6D,kBAAkB,CAAC;AACvB,aAAWR,QAAQrD,WAAWC,QAAQ;AACpC,UAAMkD,QAAQlD,OAAOoD,IAAI;AAEzB,QAAI,CAACF,MAAO;AAEZU,sBAAkB;MAChB,GAAGA;MACH,GAAGT,aAAaC,MAAMF,OAAOnD,UAAU;IACzC;EACF;AACA,SAAO6D;AACT;AAEA,SAASC,YAAa7D,QAA2C;AAC/D,QAAM8D,WAAW,CAAC;AAElB,aAAWZ,SAASvB,OAAOoC,KAAK/D,MAAM,GAAG;AACvC,QAAIkD,MAAMF,WAAW,KAAK,KAAKhD,OAAO,MAAMkD,KAAK,EAAE,EAAG;AAEtD,UAAMc,UAAU,MAAMd,KAAK;AAC3B,UAAMe,WAAWrB,WAAW5C,OAAOkD,KAAK,CAAC;AAEzCY,aAASE,OAAO,IAAIE,cAAcD,QAAQ;EAC5C;AAEA,SAAOH;AACT;AAEA,SAAS1B,kBAAmBJ,UAAkBE,OAAgB;AAC5D,MAAI,CAACA,MAAO,QAAOF;AAEnB,QAAMmC,gBAAgB,UAAUjC,KAAK;AAErC,SAAOF,aAAa,UAAUmC,gBAAgB,GAAGA,aAAa,IAAInC,QAAQ;AAC5E;AAEA,SAASoC,aAAcC,IAAYC,UAA8BC,QAAgB;AAC/E,QAAMC,UAAUC,wBAAwBJ,IAAIC,QAAQ;AAEpD,MAAI,CAACE,QAAS;AAEdA,UAAQE,YAAYH;AACtB;AAEA,SAASE,wBAAyBJ,IAAYC,UAAmB;AAC/D,MAAI,CAACK,WAAY,QAAO;AAExB,MAAIC,QAAQC,SAASC,eAAeT,EAAE;AAEtC,MAAI,CAACO,OAAO;AACVA,YAAQC,SAASE,cAAc,OAAO;AACtCH,UAAMP,KAAKA;AACXO,UAAMI,OAAO;AAEb,QAAIV,SAAUM,OAAMK,aAAa,SAASX,QAAQ;AAElDO,aAASK,KAAKC,YAAYP,KAAK;EACjC;AAEA,SAAOA;AACT;AAGO,SAASQ,YAAahE,SAAyE;AACpG,QAAMiE,gBAAgBlE,kBAAkBC,OAAO;AAC/C,QAAMkE,QAAQC,WAAWF,cAAcxF,YAAY;AACnD,QAAMM,SAASqF,IAAIH,cAAclF,MAAM;AACvC,QAAMsF,aAAaF,WAAW,OAAO;AAErC,QAAMnC,OAAOsC,SAAS;IACpBC,MAAO;AACL,aAAOL,MAAM5C,UAAU,WAAW+C,WAAW/C,QAAQ4C,MAAM5C;IAC7D;IACAkD,IAAKC,KAAa;AAChBP,YAAM5C,QAAQmD;IAChB;EACF,CAAC;AAED,QAAMC,iBAAiBJ,SAAS,MAAM;AACpC,UAAMK,MAA+C,CAAC;AACtD,eAAW,CAAC3C,OAAM4C,QAAQ,KAAKrE,OAAOC,QAAQzB,OAAOuC,KAAK,GAAG;AAC3D,YAAM1C,SAAS;QACb,GAAGgG,SAAShG;QACZ,GAAG2D,cAAcqC,SAAShG,QAAQqF,cAActF,UAAU;MAC5D;AAEAgG,UAAI3C,KAAI,IAAI;QACV,GAAG4C;QACHhG,QAAQ;UACN,GAAGA;UACH,GAAG6D,YAAY7D,MAAM;QACvB;MACF;IACF;AACA,WAAO+F;EACT,CAAC;AAED,QAAME,UAAUC,MAAM,MAAMJ,eAAepD,MAAMU,KAAKV,KAAK,CAAC;AAE5D,QAAMyD,WAAWD,MAAM,MAAMZ,MAAM5C,UAAU,QAAQ;AAErD,QAAM6B,SAASmB,SAAS,MAAM;AAhYhC;AAiYI,UAAM3D,QAAkB,CAAA;AACxB,UAAMqE,YAAYf,cAAcpE,cAAc,KAAK;AACnD,UAAMD,SAASqE,cAAcrE,SAASqE,cAAcvF,SAAS;AAE7D,SAAImG,aAAQvD,UAARuD,mBAAe5F,MAAM;AACvByB,qBAAeC,OAAO,SAAS,CAAC,oBAAoB,GAAGsD,cAAcnD,KAAK;IAC5E;AAEAJ,mBAAeC,OAAO,SAASQ,gBAAgB0D,QAAQvD,OAAO2C,cAAcvF,MAAM,GAAGuF,cAAcnD,KAAK;AAExG,eAAW,CAACmE,WAAW3G,KAAK,KAAKiC,OAAOC,QAAQkE,eAAepD,KAAK,GAAG;AACrEZ,qBAAeC,OAAO,IAAIsD,cAAcvF,MAAM,UAAUuG,SAAS,IAAI,CACnE,iBAAiB3G,MAAMW,OAAO,SAAS,QAAQ,IAC/C,GAAGkC,gBAAgB7C,OAAO2F,cAAcvF,MAAM,CAAC,GAC9CuF,cAAcnD,KAAK;IACxB;AAEA,QAAImD,cAAcnE,WAAW;AAC3B,YAAMoF,UAAoB,CAAA;AAC1B,YAAMC,UAAoB,CAAA;AAE1B,YAAMvG,SAAS,IAAIwG,IAAI7E,OAAO8E,OAAOX,eAAepD,KAAK,EAAEgE,QAAQhH,WAASiC,OAAOoC,KAAKrE,MAAMM,MAAM,CAAC,CAAC;AACtG,iBAAW0B,OAAO1B,QAAQ;AACxB,YAAI0B,IAAIsB,WAAW,KAAK,GAAG;AACzBlB,yBAAeyE,SAAS,IAAI7E,GAAG,IAAI,CAAC,oBAAoB2D,cAAcvF,MAAM,SAAS4B,GAAG,KAAK0E,SAAS,EAAE,GAAGf,cAAcnD,KAAK;QAChI,OAAO;AACLJ,yBAAewE,SAAS,IAAItF,MAAM,MAAMU,GAAG,IAAI,CAC7C,KAAK2D,cAAcvF,MAAM,mCAAmCuF,cAAcvF,MAAM,SAAS4B,GAAG,wBAC5F,+BAA+B2D,cAAcvF,MAAM,SAAS4B,GAAG,KAAK0E,SAAS,IAC7E,oBAAoBf,cAAcvF,MAAM,YAAY4B,GAAG,KAAK0E,SAAS,EAAE,GACtEf,cAAcnD,KAAK;AACtBJ,yBAAeyE,SAAS,IAAIvF,MAAM,QAAQU,GAAG,IAAI,CAAC,oBAAoB2D,cAAcvF,MAAM,SAAS4B,GAAG,KAAK0E,SAAS,EAAE,GAAGf,cAAcnD,KAAK;AAC5IJ,yBAAeyE,SAAS,IAAIvF,MAAM,UAAUU,GAAG,IAAI,CAAC,KAAK2D,cAAcvF,MAAM,uBAAuBuF,cAAcvF,MAAM,SAAS4B,GAAG,GAAG,GAAG2D,cAAcnD,KAAK;QAC/J;MACF;AAEAH,YAAMI,KAAK,GAAGmE,SAAS,GAAGC,OAAO;IACnC;AAEA,WAAOxE,MAAMM,IAAI,CAACsE,KAAKC,MAAMA,MAAM,IAAID,MAAM,OAAOA,GAAG,EAAE,EAAEE,KAAK,EAAE;EACpE,CAAC;AAED,QAAMC,eAAeZ,MAAM,MAAMb,cAAc5D,aAAaF,SAAY,GAAG8D,cAAcvF,MAAM,UAAUsD,KAAKV,KAAK,EAAE;AACrH,QAAMqE,aAAab,MAAM,MAAMvE,OAAOoC,KAAK+B,eAAepD,KAAK,CAAC;AAEhE,MAAIsE,sBAAsB;AAGxB,QAASC,mBAAT,WAA6B;AAC3BxB,iBAAW/C,QAAQwE,MAAMC,UAAU,SAAS;IAC9C;AAJA,UAAMD,QAAQE,OAAOC,WAAW,8BAA8B;AAM9DJ,qBAAiB;AAEjBC,UAAMI,iBAAiB,UAAUL,kBAAkB;MAAEM,SAAS;IAAK,CAAC;AAEpE,QAAIC,gBAAgB,GAAG;AACrBC,qBAAe,MAAM;AACnBP,cAAMQ,oBAAoB,UAAUT,gBAAgB;MACtD,CAAC;IACH;EACF;AAEA,WAASU,QAASC,KAAU;AAC1B,QAAIvC,cAAc5D,WAAY;AAE9B,UAAMyD,OAAO0C,IAAIC,SAASC,SAASC;AACnC,QAAI7C,MAAM;AACR,UAAS8C,UAAT,WAAoB;AAClB,eAAO;UACLpD,OAAO,CAAC;YACNqD,aAAa1D,OAAO7B;YACpB2B,IAAIgB,cAActE;YAClBmH,OAAO7C,cAAcf,YAAY;UACnC,CAAC;QACH;MACF;AAEA,UAAIY,KAAK/C,MAAM;AACb,cAAMgG,QAAQjD,KAAK/C,KAAK6F,OAAO;AAC/B,YAAIrD,YAAY;AACdyD,gBAAM7D,QAAQ,MAAM;AAAE4D,kBAAME,MAAML,OAAO;UAAE,CAAC;QAC9C;MACF,OAAO;AACL,YAAIrD,YAAY;AACdO,eAAKoD,YAAYpC,MAAM8B,OAAO,CAAC;AAC/BO,sBAAY,MAAMrD,KAAKsD,UAAU,CAAC;QACpC,OAAO;AACLtD,eAAKoD,YAAYN,QAAQ,CAAC;QAC5B;MACF;IACF,OAAO;AAOL,UAASS,eAAT,WAAyB;AACvBrE,qBAAaiB,cAActE,cAAcsE,cAAcf,UAAUC,OAAO7B,KAAK;MAC/E;AARA,UAAIiC,YAAY;AACdyD,cAAM7D,QAAQkE,cAAc;UAAEC,WAAW;QAAK,CAAC;MACjD,OAAO;AACLD,qBAAa;MACf;IAKF;EACF;AAEA,WAASE,OAAQtC,WAAmB;AAClC,QAAIA,cAAc,YAAY,CAACU,WAAWrE,MAAMkG,SAASvC,SAAS,GAAG;AACnEwC,kBAAY,UAAUxC,SAAS,2CAA2C;AAC1E;IACF;AAEAjD,SAAKV,QAAQ2D;EACf;AAEA,WAASyC,QAAgD;AAAA,QAAzCC,aAAoB1H,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG0F,WAAWrE;AAChD,UAAMsG,eAAeD,WAAWE,QAAQ7F,KAAKV,KAAK;AAClD,UAAMwG,YAAYF,iBAAiB,KAAK,KAAKA,eAAe,KAAKD,WAAWzH;AAE5EqH,WAAOI,WAAWG,SAAS,CAAC;EAC9B;AAEA,WAASC,SAA0D;AAAA,QAAlDJ,aAA4B1H,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC,SAAS,MAAM;AAC9DyH,UAAMC,UAAU;EAClB;AAEA,QAAMK,aAAa,IAAIC,MAAMjG,MAAM;IACjCuC,IAAK2D,QAAQC,MAAM;AACjB,aAAOC,QAAQ7D,IAAI2D,QAAQC,IAAI;IACjC;IACA3D,IAAK0D,QAAQC,MAAM1D,KAAK;AACtB,UAAI0D,SAAS,SAAS;AACpBE,kBAAU,6BAA6B5D,GAAG,IAAI,iBAAiBA,GAAG,IAAI;MACxE;AACA,aAAO2D,QAAQ5D,IAAI0D,QAAQC,MAAM1D,GAAG;IACtC;EACF,CAAC;AAED,SAAO;IACL8B;IACAgB;IACAG;IACAK;IACA1H,YAAY4D,cAAc5D;IAC1B0E;IACA/C;IACAjD;IACA8F;IACAH;IACAhG,QAAQuF,cAAcvF;IACtBgH;IACAvC;IACAmF,QAAQ;MACNtG,MAAMgG;MACNnD;IACF;EACF;AACF;AA0BO,SAAS0D,WAAY;AAC1BC,EAAAA,oBAAmB,UAAU;AAE7B,QAAMC,QAAQC,OAAOC,aAAa,IAAI;AAEtC,MAAI,CAACF,MAAO,OAAM,IAAIG,MAAM,wCAAwC;AAEpE,SAAOH;AACT;;;AC9fO,IAAMI,mBAAgDC,OAAOC,IAAI,gBAAgB;AACjF,IAAMC,uBAAqDF,OAAOC,IAAI,qBAAqB;AAI3F,IAAME,kBAAkBC,aAAa;EAC1CC,UAAU;IACRC,MAAMC;IACNC,SAASA,MAAO,CAAA;EAClB;EACAC,YAAYC;AACd,GAAG,QAAQ;AAGJ,IAAMC,sBAAsBP,aAAa;EAC9CQ,MAAM;IACJN,MAAMO;EACR;EACAC,OAAO;IACLR,MAAM,CAACS,QAAQF,MAAM;IACrBL,SAAS;EACX;EACAQ,UAAUN;AACZ,GAAG,aAAa;AAET,SAASO,YAAa;AAC3B,QAAMC,SAASC,OAAOC,gBAAgB;AAEtC,MAAI,CAACF,OAAQ,OAAM,IAAIG,MAAM,0CAA0C;AAEvE,SAAO;IACLC,eAAeJ,OAAOI;IACtBC,UAAUL,OAAOK;IACjBC,YAAYN,OAAOM;EACrB;AACF;;;AC7FO,IAAMC,cAAsC;;EAEjDC,SAAS;EACTC,SAAS;EACTC,QAAQ;;EAGRC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,OAAO;;EAGPC,KAAK;EACLC,UAAU;EACVC,OAAO;EACPC,QAAQ;EACRC,KAAK;;EAGLC,OAAO;EACPC,QAAQ;AACV;AAQO,SAASC,aAAcC,KAAqB;AACjD,QAAMC,WAAWD,IAAIE,YAAY;AACjC,SAAOlB,YAAYiB,QAAQ,KAAKA;AAClC;;;AC1BO,SAASE,oBAAqBC,aAAmD;AAAA,MAA9BC,aAAUC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACrE,MAAI,CAACF,aAAa;AAChB,QAAI,CAACC,WAAYI,aAAY,mDAAmD;AAChF,WAAO,CAAA;EACT;AAGA,QAAMC,6BAA6BN,YAAYO,WAAW,GAAG,KAAKP,YAAYO,WAAW,GAAG;AAE5F,QAAMC;;IAEJF,8BACA,EAAEN,YAAYO,WAAW,IAAI,KAAKP,YAAYO,WAAW,IAAI;;AAG/D,QAAME;;IAEHT,YAAYG,SAAS,KAAKK;IAE3BR,YAAYU,SAAS,IAAI,KAAKV,YAAYU,SAAS,IAAI,KACvDV,gBAAgB,OAAOA,gBAAgB;IAEtCA,YAAYG,SAAS,MAAMH,YAAYW,SAAS,GAAG,KAAKX,YAAYW,SAAS,GAAG,MAAMX,YAAYY,GAAG,EAAE,MAAMZ,YAAYY,GAAG,EAAE;IAE/HZ,gBAAgB,QAAQA,gBAAgB,QAAQA,gBAAgB;;AAGlE,MAAIS,qBAAqB;AACvB,QAAI,CAACR,WAAYI,aAAY,gCAAgCL,WAAW,yBAAyB;AACjG,WAAO,CAAA;EACT;AAEA,QAAMa,OAAiB,CAAA;AACvB,MAAIC,SAAS;AAEb,QAAMC,cAAcA,MAAM;AACxB,QAAID,QAAQ;AACVD,WAAKG,KAAKC,aAAaH,MAAM,CAAC;AAC9BA,eAAS;IACX;EACF;AAEA,WAASI,IAAI,GAAGA,IAAIlB,YAAYG,QAAQe,KAAK;AAC3C,UAAMC,OAAOnB,YAAYkB,CAAC;AAC1B,UAAME,WAAWpB,YAAYkB,IAAI,CAAC;AAElC,QAAIC,SAAS,OAAOA,SAAS,OAAOA,SAAS,KAAK;AAChD,UAAIA,SAASC,UAAU;AACrBL,oBAAY;AACZF,aAAKG,KAAKG,IAAI;AACdD;MACF,WAAWC,SAAS,OAAOA,SAAS,KAAK;AACvCJ,oBAAY;MACd,OAAO;AACLD,kBAAUK;MACZ;IACF,OAAO;AACLL,gBAAUK;IACZ;EACF;AACAJ,cAAY;AAIZ,QAAMM,kBAAkBR,KAAKS,KAAKC,SAAOA,IAAIpB,SAAS,KAAKoB,IAAIb,SAAS,GAAG,KAAKa,QAAQ,IAAI;AAC5F,MAAIF,iBAAiB;AACnB,QAAI,CAACpB,WAAYI,aAAY,gCAAgCL,WAAW,yBAAyB;AACjG,WAAO,CAAA;EACT;AAEA,MAAIa,KAAKV,WAAW,KAAKH,aAAa;AACpC,WAAO,CAACiB,aAAajB,WAAW,CAAC;EACnC;AAEA,SAAOa;AACT;AAQO,SAASW,iBAAkBC,KAAuB;AACvD,MAAI,CAACA,KAAK;AACRpB,gBAAY,gDAAgD;AAC5D,WAAO,CAAA;EACT;AAIA,QAAMqB,kBAAkBD,IAAIlB,WAAW,GAAG,KAAK,CAAC,CAAC,OAAO,KAAK,EAAEG,SAASe,GAAG;AAC3E,QAAME,gBAAgBF,IAAId,SAAS,GAAG,KAAK,CAACc,IAAId,SAAS,IAAI,KAAK,CAACc,IAAId,SAAS,IAAI,KAAKc,QAAQ,OAAOA,QAAQ;AAEhH,MAAIC,mBAAmBC,eAAe;AACpCtB,gBAAY,6BAA6BoB,GAAG,iCAAiC;AAC7E,WAAO,CAAA;EACT;AAEA,QAAMG,SAAmB,CAAA;AACzB,MAAId,SAAS;AACb,MAAII,IAAI;AAER,SAAOA,IAAIO,IAAItB,QAAQ;AACrB,UAAMgB,OAAOM,IAAIP,CAAC;AAElB,QAAIC,SAAS,KAAK;AAEhB,YAAMU,WAAWJ,IAAIP,IAAI,CAAC;AAC1B,YAAMY,eAAeZ,IAAI,IAAIO,IAAIP,IAAI,CAAC,IAAId;AAE1C,YAAM2B,oCACHF,aAAa,OAAOA,aAAa,QAAQC,iBAAiB;AAG7D,UAAIC,kCAAkC;AAEpCjB,kBAAUK;AACVD;MACF,OAAO;AAEL,YAAIJ,QAAQ;AACVc,iBAAOZ,KAAKF,MAAM;AAClBA,mBAAS;QACX,OAAO;AAELc,iBAAOZ,KAAK,GAAG;QACjB;AACAE;MACF;IACF,OAAO;AACLJ,gBAAUK;AACVD;IACF;EACF;AAGA,MAAIJ,QAAQ;AACVc,WAAOZ,KAAKF,MAAM;EACpB;AAGA,QAAMkB,YAAsB,CAAA;AAC5B,MAAIC,aAAa;AACjB,aAAWC,QAAQN,QAAQ;AACzB,QAAIM,SAAS,KAAK;AAChB,UAAID,aAAa,MAAM,EAAGD,WAAUhB,KAAK,GAAG;AAC5CiB;IACF,OAAO;AACLA,mBAAa;AACbD,gBAAUhB,KAAKkB,IAAI;IACrB;EACF;AAGA,QAAMC,cAAcH,UAAUI,MAAMC,OAAKtC,oBAAoBsC,GAAG,IAAI,EAAElC,SAAS,CAAC;AAEhF,MAAI,CAACgC,aAAa;AAChB9B,gBAAY,6BAA6BoB,GAAG,iCAAiC;AAC7E,WAAO,CAAA;EACT;AAEA,SAAOO;AACT;;;AC9JO,SAASM,UACdC,MACAC,UAEA;AAtBF;AAsBE,MADAC,UAAsBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAE1B,MAAI,CAACG,WAAY,QAAO,WAAY;EAAC;AAErC,QAAM;IACJC,QAAQ;IACRC,SAAS;IACTC,iBAAiB;IACjBC,kBAAkB;EACpB,IAAIR;AAEJ,QAAMS,UAAQC,4CAAWC,cAAXD,mBAAsBE,SAAS,iBAAgB;AAC7D,MAAIC,UAAU;AACd,MAAIC;AACJ,MAAIC,aAAa;AACjB,MAAIC,aAAa;AAEjB,WAASC,aAAc;AACrB,QAAI,CAACJ,QAAS;AAEdK,iBAAaL,OAAO;AACpBA,cAAU;EACZ;AAEA,WAASM,iBAAkB;AACzB,QAAIC,QAAQd,MAAM,EAAG,QAAO;AAE5B,UAAMe,gBAAgBC,SAASD;AAE/B,WAAOA,kBACLA,cAAcE,YAAY,WAC1BF,cAAcE,YAAY,cAC1BF,cAAcG,qBACdH,cAAcI,oBAAoB;EAEtC;AAEA,WAASC,gBAAiB;AACxBV,iBAAa;AACbC,eAAW;EACb;AAEA,WAASU,QAASC,GAAkB;AAClC,UAAMC,QAAQf,UAAUE,UAAU;AAElC,QAAI,CAACa,SAASV,eAAe,EAAG;AAEhC,QAAI,CAACW,gBAAgBF,GAAGC,KAAK,GAAG;AAC9B,UAAId,WAAYW,eAAc;AAC9B;IACF;AAEA,QAAIN,QAAQb,cAAc,EAAGqB,GAAErB,eAAe;AAE9C,QAAI,CAACQ,YAAY;AACfhB,eAAS6B,CAAC;AACV;IACF;AAEAX,eAAW;AACXD;AAEA,QAAIA,eAAeF,UAAUZ,QAAQ;AACnCH,eAAS6B,CAAC;AACVF,oBAAc;AACd;IACF;AAEAb,cAAUkB,OAAOC,WAAWN,eAAeN,QAAQZ,eAAe,CAAC;EACrE;AAEA,WAASyB,UAAW;AAClBF,WAAOG,oBAAoBd,QAAQf,KAAK,GAAGsB,OAAO;AAClDV,eAAW;EACb;AAEAkB,QAAM,MAAMf,QAAQtB,IAAI,GAAG,SAAUsC,WAAW;AAC9CH,YAAQ;AAER,QAAIG,WAAW;AACb,YAAMC,SAASC,iBAAiBF,UAAUG,YAAY,CAAC;AACvDxB,mBAAasB,OAAOnC,SAAS;AAC7BY,kBAAYuB;AACZX,oBAAc;AACdK,aAAOS,iBAAiBpB,QAAQf,KAAK,GAAGsB,OAAO;IACjD;EACF,GAAG;IAAEc,WAAW;EAAK,CAAC;AAGtBN,QAAM,MAAMf,QAAQf,KAAK,GAAG,SAAUqC,UAAUC,UAAU;AACxD,QAAIA,YAAY7B,aAAaA,UAAUZ,SAAS,GAAG;AACjD6B,aAAOG,oBAAoBS,UAAUhB,OAAO;AAC5CI,aAAOS,iBAAiBE,UAAUf,OAAO;IAC3C;EACF,CAAC;AAED,MAAI;AACFiB,IAAAA,oBAAmB,WAAW;AAC9BC,oBAAgBZ,OAAO;EACzB,QAAQ;EACN;AAGF,WAASa,cAAejB,OAAe;AACrC,UAAMkB,YAAY,CAAC,QAAQ,SAAS,OAAO,QAAQ,KAAK;AAGxD,UAAMC,QAAQC,oBAAoBpB,MAAMU,YAAY,CAAC;AAGrD,QAAIS,MAAM9C,WAAW,GAAG;AACtB,aAAO;QAAEgD,WAAWC,OAAOC,YAAYL,UAAUM,IAAIC,OAAK,CAACA,GAAG,KAAK,CAAC,CAAC;QAAGC,WAAWpD;MAAU;IAC/F;AAEA,UAAM+C,YAAYC,OAAOC,YAAYL,UAAUM,IAAIC,OAAK,CAACA,GAAG,KAAK,CAAC,CAAC;AACnE,QAAIC;AAEJ,eAAWC,QAAQR,OAAO;AACxB,UAAID,UAAUnC,SAAS4C,IAAI,GAAG;AAC5BN,kBAAUM,IAAI,IAAI;MACpB,OAAO;AACLD,oBAAYC;MACd;IACF;AAEA,WAAO;MAAEN;MAAWK;IAAU;EAChC;AAEA,WAASzB,gBAAiBF,GAAkBC,OAAe;AACzD,UAAM;MAAEqB;MAAWK;IAAU,IAAIT,cAAcjB,KAAK;AAEpD,UAAM4B,aAAaP,UAAUQ,QAAS,CAACjD,UAAUyC,UAAUS,OAAOT,UAAUU;AAC5E,UAAMC,aAAapD,UAAUyC,UAAUS,OAAOT,UAAUU;AAExD,WACEhC,EAAEkC,YAAYL,cACd7B,EAAEmC,YAAYF,cACdjC,EAAEoC,aAAad,UAAUe,SACzBrC,EAAEsC,WAAWhB,UAAUiB,OACvBvC,EAAEwC,IAAI7B,YAAY,OAAMgB,uCAAWhB;EAEvC;AAEA,SAAON;AACT;;;ACzHO,SAASoC,gBAA6C;AAAA,MAA9BC,UAAuBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AACxD,QAAM;IAAEG;IAAW,GAAGC;EAAK,IAAIL;AAC/B,QAAMM,UAA0BC,UAAUH,WAAWC,IAAI;AACzD,QAAM;IACJG,SAAAA,WAAU,CAAC;IACXC,aAAa,CAAC;IACdC,aAAa,CAAC;EAChB,IAAIJ;AAEJ,QAAMK,QAAQC,YAAY;AAC1B,SAAOD,MAAME,IAAI,MAAM;AACrB,UAAMC,WAAWC,eAAeT,QAAQQ,QAAQ;AAChD,UAAME,UAAUC,cAAcX,QAAQU,SAASV,QAAQY,GAAG;AAC1D,UAAMC,QAAQC,YAAYd,QAAQa,KAAK;AACvC,UAAME,QAAQC,YAAYhB,QAAQe,KAAK;AACvC,UAAME,SAASC,aAAalB,QAAQiB,MAAM;AAC1C,UAAME,QAAOC,WAAWpB,QAAQmB,MAAMF,MAAM;AAC5C,UAAMI,OAAOC,WAAWtB,QAAQqB,MAAMJ,MAAM;AAE5C,aAASM,QAASC,KAAU;AAC1B,iBAAWC,OAAOrB,YAAY;AAC5BoB,YAAIE,UAAUD,KAAKrB,WAAWqB,GAAG,CAAC;MACpC;AAEA,iBAAWA,OAAOtB,YAAY;AAC5BqB,YAAIG,UAAUF,KAAKtB,WAAWsB,GAAG,CAAC;MACpC;AAEA,iBAAWA,OAAOvB,UAAS;AACzBsB,YAAIG,UAAUF,KAAKG,iBAAgB;UACjC,GAAG1B,SAAQuB,GAAG;UACdI,MAAMJ;UACNK,WAAW5B,SAAQuB,GAAG,EAAEI;QAC1B,CAAC,CAAC;MACJ;AAEA,YAAME,WAAWzB,YAAY;AAC7ByB,eAASxB,IAAI,MAAM;AACjBM,cAAMU,QAAQC,GAAG;MACnB,CAAC;AACDA,UAAIQ,UAAU,MAAMD,SAASE,KAAK,CAAC;AAEnCT,UAAIU,QAAQC,gBAAgB3B,QAAQ;AACpCgB,UAAIU,QAAQE,eAAe1B,OAAO;AAClCc,UAAIU,QAAQG,aAAaxB,KAAK;AAC9BW,UAAIU,QAAQI,YAAYvB,KAAK;AAC7BS,UAAIU,QAAQK,cAActB,MAAM;AAChCO,UAAIU,QAAQM,mBAAmBrB,MAAKnB,OAAO;AAC3CwB,UAAIU,QAAQO,mBAAmBtB,MAAKuB,QAAQ;AAC5ClB,UAAIU,QAAQS,YAAYtB,IAAI;AAE5B,UAAIuB,cAAc5C,QAAQY,KAAK;AAC7B,YAAIY,IAAIqB,OAAO;AACbrB,cAAIqB,MAAMC,KAAK,wBAAwB,MAAM;AAC3CpC,oBAAQqC,OAAO;UACjB,CAAC;QACH,OAAO;AACL,gBAAM;YAAEC;UAAM,IAAIxB;AAClBA,cAAIwB,QAAQ,WAAa;AACvB,kBAAMC,KAAKD,MAAM,GAAArD,SAAO;AACxBuD,qBAAS,MAAMxC,QAAQqC,OAAO,CAAC;AAC/BvB,gBAAIwB,QAAQA;AACZ,mBAAOC;UACT;QACF;MACF;AAEA,UAAI,OAAOE,wBAAwB,aAAaA,qBAAqB;AACnE3B,YAAI4B,MAAM;UACRC,UAAU;YACRC,WAAY;AACV,qBAAOC,SAAS;gBACd/C,UAAUgD,QAAOC,KAAK,MAAMtB,cAAc;gBAC1CzB,SAAS8C,QAAOC,KAAK,MAAMrB,aAAa;gBACxCvB,OAAO2C,QAAOC,KAAK,MAAMpB,WAAW;gBACpCtB,OAAOyC,QAAOC,KAAK,MAAMnB,UAAU;gBACnCrB,QAAQuC,QAAOC,KAAK,MAAMlB,YAAY;gBACtCpB,MAAMqC,QAAOC,KAAK,MAAMhB,iBAAiB;cAC3C,CAAC;YACH;UACF;QACF,CAAC;MACH;IACF;AAEA,aAASiB,UAAW;AAClBrD,YAAM4B,KAAK;IACb;AAEA,WAAO;MACLV;MACAmC;MACAlD;MACAE;MACAG;MACAE;MACAE;MACAE,MAAAA;MACAE;IACF;EACF,CAAC;AACH;AAEO,IAAMsC,UAAO;AACpBlE,cAAckE,UAAUA;AAGxB,SAASH,QAAuC/B,KAAiC;AAvJjF;AAwJE,QAAMwB,KAAK,KAAKW;AAEhB,QAAMC,aAAWZ,QAAGa,WAAHb,mBAAWY,eAAYZ,QAAGc,MAAMC,eAATf,mBAAqBY;AAE7D,MAAIA,YAAapC,OAAeoC,UAAU;AACxC,WAAOA,SAAUpC,GAAG;EACtB;AACF;", "names": ["useToggleScope", "source", "fn", "scope", "start", "effectScope", "run", "length", "stop", "watch", "active", "undefined", "immediate", "onScopeDispose", "IN_BROWSER", "window", "SUPPORTS_INTERSECTION", "SUPPORTS_TOUCH", "navigator", "maxTouchPoints", "SUPPORTS_EYE_DROPPER", "SUPPORTS_MATCH_MEDIA", "matchMedia", "getNestedValue", "obj", "path", "fallback", "last", "length", "undefined", "i", "getObjectValueByPath", "obj", "path", "fallback", "undefined", "replace", "getNestedValue", "split", "createRange", "length", "start", "arguments", "undefined", "Array", "from", "v", "k", "isPlainObject", "obj", "proto", "Object", "getPrototypeOf", "prototype", "refElement", "el", "$el", "nodeType", "Node", "TEXT_NODE", "nextElement<PERSON><PERSON>ling", "keyCodes", "freeze", "enter", "tab", "delete", "esc", "space", "up", "down", "left", "right", "end", "home", "del", "backspace", "insert", "pageup", "pagedown", "shift", "keyV<PERSON><PERSON>", "has", "obj", "key", "every", "k", "hasOwnProperty", "pick", "paths", "found", "Object", "prototype", "call", "clamp", "value", "min", "arguments", "length", "undefined", "max", "Math", "padEnd", "str", "length", "char", "arguments", "undefined", "repeat", "Math", "max", "padStart", "chunk", "size", "chunked", "index", "push", "substr", "mergeDeep", "source", "arguments", "length", "undefined", "target", "arrayFn", "out", "key", "sourceProperty", "targetProperty", "isPlainObject", "Array", "isArray", "toKebabCase", "str", "arguments", "length", "undefined", "cache", "has", "get", "kebab", "replace", "toLowerCase", "set", "Map", "mainTRC", "Rco", "Gco", "Bco", "normBG", "normTXT", "revTXT", "revBG", "blkThrs", "blkClmp", "deltaYmin", "scaleBoW", "scaleWoB", "lo<PERSON>on<PERSON><PERSON><PERSON>", "loConFactor", "loConOffset", "loClip", "APCAcontrast", "text", "background", "Rtxt", "r", "Gtxt", "g", "Btxt", "b", "Rbg", "Gbg", "Bbg", "Ytxt", "Ybg", "Math", "abs", "outputContrast", "SAPC", "console<PERSON>arn", "message", "warn", "consoleError", "deprecate", "original", "replacement", "Array", "isArray", "slice", "map", "s", "join", "at", "delta", "cielabForwardTransform", "t", "Math", "cbrt", "cielabReverseTransform", "fromXYZ", "xyz", "transform", "transformedY", "toXYZ", "lab", "Ln", "srgbForwardMatrix", "srgbForwardTransform", "C", "srgbReverseMatrix", "srgbReverseTransform", "fromXYZ", "xyz", "rgb", "Array", "transform", "matrix", "i", "Math", "round", "clamp", "r", "g", "b", "toXYZ", "_ref", "cssColorRe", "mappers", "rgb", "r", "g", "b", "a", "rgba", "hsl", "h", "s", "l", "HSLtoRGB", "hsla", "hsv", "v", "HSVtoRGB", "hsva", "parseColor", "color", "isNaN", "console<PERSON>arn", "test", "groups", "match", "fn", "values", "realValues", "split", "map", "i", "endsWith", "includes", "parseFloat", "hex", "startsWith", "slice", "length", "char", "join", "int", "parseInt", "HexToRGB", "has", "HSLtoHSV", "TypeError", "String", "constructor", "name", "HSVtoRGB", "hsva", "h", "s", "v", "a", "f", "n", "k", "Math", "max", "min", "rgb", "map", "round", "r", "g", "b", "HSLtoRGB", "hsla", "HSLtoHSV", "HSLtoHSV", "hsl", "h", "s", "l", "a", "v", "Math", "min", "sprime", "toHex", "v", "h", "Math", "round", "toString", "substr", "length", "toUpperCase", "RGBtoHex", "_ref2", "r", "g", "b", "a", "undefined", "join", "HexToRGB", "hex", "parseHex", "chunk", "map", "c", "parseInt", "parseHex", "hex", "startsWith", "slice", "replace", "length", "split", "map", "x", "join", "padEnd", "lighten", "value", "amount", "lab", "fromXYZ", "toXYZ", "darken", "getLuma", "color", "rgb", "parseColor", "getForeground", "color", "blackContrast", "Math", "abs", "APCAcontrast", "parseColor", "whiteContrast", "min", "propsFactory", "props", "source", "defaults", "Object", "keys", "reduce", "obj", "prop", "isObjectDefinition", "Array", "isArray", "definition", "type", "default", "makeComponentProps", "propsFactory", "class", "String", "Array", "Object", "style", "type", "default", "getCurrentInstance", "name", "message", "vm", "_getCurrentInstance", "Error", "getCurrentInstanceName", "arguments", "length", "undefined", "type", "toKebabCase", "<PERSON><PERSON><PERSON>", "injectSelf", "key", "vm", "arguments", "length", "undefined", "getCurrentInstance", "provides", "DefaultsSymbol", "Symbol", "for", "createDefaults", "options", "ref", "injectDefaults", "defaults", "inject", "Error", "propIsDefined", "vnode", "prop", "props", "toKebabCase", "internalUseDefaults", "arguments", "length", "undefined", "name", "defaults", "injectDefaults", "vm", "getCurrentInstance", "type", "__name", "Error", "componentDefaults", "computed", "value", "_as", "_props", "Proxy", "get", "target", "propValue", "Reflect", "filter", "v", "_componentDefault", "_globalDefault", "global", "_subcomponentDefaults", "shallowRef", "watchEffect", "subComponents", "Object", "entries", "_ref", "key", "startsWith", "toUpperCase", "fromEntries", "provideSubDefaults", "injected", "injectSelf", "DefaultsSymbol", "provide", "mergeDeep", "useDefaults", "defineComponent", "options", "_setup", "setup", "name", "console<PERSON>arn", "props", "propsFactory", "propKeys", "Object", "keys", "filter", "key", "filterProps", "pick", "_as", "String", "ctx", "defaults", "injectDefaults", "value", "_props", "provideSubDefaults", "internalUseDefaults", "setupBindings", "genericComponent", "exposeDefaults", "arguments", "length", "undefined", "_defineComponent", "easingPatterns", "linear", "t", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "useProxiedModel", "props", "prop", "defaultValue", "transformIn", "arguments", "length", "undefined", "v", "transformOut", "vm", "getCurrentInstance", "internal", "ref", "kebabProp", "toKebabCase", "checkKebab", "isControlled", "computed", "vnode", "hasOwnProperty", "useToggleScope", "value", "watch", "val", "model", "get", "externalValue", "set", "internalValue", "newValue", "toRaw", "emit", "Object", "defineProperty", "badge", "open", "close", "dismiss", "confirmEdit", "ok", "cancel", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "dateRangeInput", "divider", "datePicker", "itemsSelected", "range", "title", "header", "input", "placeholder", "previousMonth", "nextMonth", "selectYear", "selectDate", "currentDate", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "today", "clear", "prependAction", "appendAction", "otp", "fileInput", "counter", "counterSize", "fileUpload", "browse", "timePicker", "am", "pm", "pagination", "root", "previous", "page", "currentPage", "first", "last", "stepper", "rating", "item", "loading", "infiniteScroll", "loadMore", "empty", "rules", "required", "email", "number", "integer", "capital", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "strictLength", "exclude", "notEmpty", "pattern", "hotkey", "then", "ctrl", "command", "space", "shift", "alt", "enter", "escape", "upArrow", "downArrow", "leftArrow", "rightArrow", "backspace", "option", "plus", "shortcut", "video", "play", "pause", "seek", "volume", "showVolume", "mute", "unmute", "enterFullscreen", "exitFullscreen", "LANG_PREFIX", "replace", "str", "params", "match", "index", "String", "Number", "createTranslateFunction", "current", "fallback", "messages", "key", "_len", "arguments", "length", "Array", "_key", "startsWith", "<PERSON><PERSON><PERSON>", "currentLocale", "value", "fallback<PERSON><PERSON><PERSON>", "getObjectValueByPath", "console<PERSON>arn", "consoleError", "createNumberFunction", "options", "numberFormat", "Intl", "NumberFormat", "format", "inferDecimalSeparator", "includes", "useProvided", "props", "prop", "provided", "internal", "useProxiedModel", "watch", "v", "createProvideFunction", "state", "name", "decimalSeparator", "toRef", "t", "n", "provide", "createVuetifyAdapter", "shallowRef", "locale", "ref", "en", "LocaleSymbol", "Symbol", "for", "isLocaleInstance", "obj", "name", "createLocale", "options", "i18n", "adapter", "createVuetifyAdapter", "rtl", "createRtl", "useLocale", "locale", "inject", "Error", "RtlSymbol", "Symbol", "for", "gen<PERSON><PERSON><PERSON><PERSON>", "af", "ar", "bg", "ca", "ckb", "cs", "de", "el", "en", "es", "et", "fa", "fi", "fr", "hr", "hu", "he", "id", "it", "ja", "km", "ko", "lv", "lt", "nl", "no", "pl", "pt", "ro", "ru", "sk", "sl", "srCyrl", "srLatn", "sv", "th", "tr", "az", "uk", "vi", "zhHans", "zhHant", "createRtl", "i18n", "options", "rtl", "ref", "isRtl", "computed", "value", "current", "rtlClasses", "toRef", "useRtl", "locale", "inject", "LocaleSymbol", "Error", "isRtl", "rtlClasses", "weekInfo", "locale", "code", "slice", "toUpperCase", "firstDay", "firstWeekSize", "includes", "getWeekArray", "date", "firstDayOfWeek", "weeks", "currentWeek", "firstDayOfMonth", "startOfMonth", "lastDayOfMonth", "endOfMonth", "first", "firstDayWeekIndex", "getDay", "lastDayWeekIndex", "i", "adjacentDay", "Date", "setDate", "getDate", "push", "day", "getFullYear", "getMonth", "length", "startOfWeek", "console<PERSON>arn", "d", "endOfWeek", "lastDay", "parseLocalDate", "value", "parts", "split", "map", "Number", "_YYYMMDD", "parsed", "test", "parse", "isNaN", "sundayJanuarySecond2000", "getWeekdays", "weekdayFormat", "days<PERSON><PERSON><PERSON><PERSON><PERSON>", "createRange", "weekday", "Intl", "DateTimeFormat", "format", "formatString", "formats", "newDate", "customFormat", "options", "year", "month", "NumberFormat", "hour", "hour12", "minute", "second", "replace", "timeZone", "timeZoneName", "toISO", "adapter", "toJsDate", "padStart", "String", "parseISO", "addMinutes", "amount", "setMinutes", "getMinutes", "addHours", "setHours", "getHours", "addDays", "addWeeks", "addMonths", "setMonth", "getYear", "getWeek", "firstWeekMinSize", "weekInfoFromLocale", "weekStart", "minWeekSize", "yearStart", "getDiff", "currentWeekEnd", "size", "d1w1", "endOfDay", "startOfDay", "getNextMonth", "getPrevious<PERSON><PERSON>h", "startOfYear", "endOfYear", "is<PERSON>ithinRange", "range", "isAfter", "isBefore", "<PERSON><PERSON><PERSON><PERSON>", "getTime", "comparing", "isAfterDay", "isEqual", "isSameDay", "isSameMonth", "isSameYear", "unit", "c", "Math", "floor", "count", "setYear", "setFullYear", "VuetifyDateAdapter", "constructor", "undefined", "DateOptionsSymbol", "Symbol", "for", "DateAdapterSymbol", "createDate", "options", "locale", "_options", "mergeDeep", "adapter", "VuetifyDateAdapter", "af", "bg", "ca", "ckb", "cs", "de", "el", "en", "et", "fa", "fi", "hr", "hu", "he", "id", "it", "ja", "ko", "lv", "lt", "nl", "no", "pl", "pt", "ro", "ru", "sk", "sl", "srCyrl", "srLatn", "sv", "th", "tr", "az", "uk", "vi", "zhHans", "zhHant", "instance", "createInstance", "createInstance", "options", "locale", "instance", "reactive", "adapter", "current", "value", "formats", "watch", "useDate", "inject", "DateOptionsSymbol", "Error", "useLocale", "DisplaySymbol", "Symbol", "for", "defaultDisplayOptions", "mobileBreakpoint", "thresholds", "xs", "sm", "md", "lg", "xl", "xxl", "parseDisplayOptions", "options", "arguments", "length", "undefined", "mergeDeep", "getClientWidth", "ssr", "IN_BROWSER", "window", "innerWidth", "clientWidth", "getClientHeight", "innerHeight", "clientHeight", "getPlatform", "userAgent", "navigator", "match", "regexp", "Boolean", "android", "ios", "<PERSON><PERSON>", "electron", "chrome", "edge", "firefox", "opera", "win", "mac", "linux", "touch", "SUPPORTS_TOUCH", "createDisplay", "height", "shallowRef", "platform", "state", "reactive", "width", "updateSize", "value", "update", "watchEffect", "name", "breakpoint<PERSON><PERSON>ue", "mobile", "smAndUp", "mdAndUp", "lgAndUp", "xlAndUp", "smAndDown", "mdAndDown", "lgAndDown", "xlAndDown", "addEventListener", "passive", "onScopeDispose", "removeEventListener", "toRefs", "makeDisplayProps", "propsFactory", "type", "default", "Number", "String", "useDisplay", "props", "getCurrentInstanceName", "display", "inject", "Error", "computed", "displayClasses", "toRef", "GoToSymbol", "Symbol", "for", "gen<PERSON><PERSON><PERSON><PERSON>", "container", "undefined", "duration", "layout", "offset", "easing", "patterns", "easingPatterns", "getContainer", "el", "get<PERSON><PERSON><PERSON>", "document", "scrollingElement", "body", "querySelector", "refElement", "getOffset", "target", "horizontal", "rtl", "totalOffset", "offsetLeft", "offsetTop", "offsetParent", "createGoTo", "options", "locale", "isRtl", "mergeDeep", "scrollTo", "_target", "_options", "goTo", "property", "value", "HTMLElement", "parentElement", "ease", "TypeError", "targetLocation", "styles", "window", "getComputedStyle", "layoutOffset", "getPropertyValue", "parseInt", "clampTarget", "startLocation", "Promise", "resolve", "startTime", "performance", "now", "requestAnimationFrame", "step", "currentTime", "timeElapsed", "progress", "location", "Math", "floor", "clamp", "abs", "console<PERSON>arn", "useGoTo", "arguments", "length", "goToInstance", "inject", "useRtl", "Error", "toRef", "go", "scrollWidth", "scrollHeight", "containerWidth", "containerHeight", "innerWidth", "innerHeight", "offsetWidth", "offsetHeight", "min", "max", "aliases", "collapse", "complete", "cancel", "close", "delete", "clear", "success", "info", "warning", "error", "prev", "next", "checkboxOn", "checkboxOff", "checkboxIndeterminate", "delimiter", "sortAsc", "sortDesc", "expand", "menu", "subgroup", "dropdown", "radioOn", "radioOff", "edit", "ratingEmpty", "ratingFull", "ratingHalf", "loading", "first", "last", "unfold", "file", "plus", "minus", "calendar", "treeviewCollapse", "treeviewExpand", "eyeDropper", "upload", "color", "command", "ctrl", "space", "shift", "alt", "enter", "arrowup", "arrowdown", "arrowleft", "arrowright", "backspace", "play", "pause", "fullscreen", "fullscreenExit", "volumeHigh", "volumeMedium", "volumeLow", "volumeOff", "mdi", "component", "props", "h", "VClassIcon", "class", "IconValue", "String", "Function", "Object", "Array", "IconSymbol", "Symbol", "for", "makeIconProps", "propsFactory", "icon", "type", "tag", "required", "VComponentIcon", "genericComponent", "name", "props", "setup", "_ref", "slots", "Icon", "_createVNode", "default", "VSvgIcon", "defineComponent", "inheritAttrs", "_ref2", "attrs", "_mergeProps", "_createElementVNode", "isArray", "map", "path", "VLigatureIcon", "VClassIcon", "_normalizeClass", "gen<PERSON><PERSON><PERSON><PERSON>", "svg", "component", "class", "createIcons", "options", "sets", "defaultSet", "mdi", "mergeDeep", "aliases", "vuetify", "ThemeSymbol", "Symbol", "for", "makeThemeProps", "propsFactory", "theme", "String", "gen<PERSON><PERSON><PERSON><PERSON>", "defaultTheme", "prefix", "variations", "colors", "lighten", "darken", "themes", "light", "dark", "background", "surface", "primary", "secondary", "error", "info", "success", "warning", "variables", "stylesheetId", "scoped", "unimportant", "utilities", "parseThemeOptions", "options", "arguments", "length", "undefined", "defaults", "isDisabled", "key", "Object", "entries", "mergeDeep", "createCssClass", "lines", "selector", "content", "scope", "push", "getScopedSelector", "map", "line", "genCssVariables", "lightOverlay", "dark<PERSON><PERSON><PERSON>", "value", "rgb", "parseColor", "r", "g", "b", "startsWith", "getLuma", "color", "genVariation", "name", "object", "variation", "fn", "amount", "createRange", "RGBtoHex", "genVariations", "variationColors", "genOnColors", "onColors", "keys", "onColor", "colorVal", "getForeground", "scopeSelector", "upsertStyles", "id", "cspNonce", "styles", "styleEl", "getOrCreateStyleElement", "innerHTML", "IN_BROWSER", "style", "document", "getElementById", "createElement", "type", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "createTheme", "parsedOptions", "_name", "shallowRef", "ref", "systemName", "computed", "get", "set", "val", "computedThemes", "acc", "original", "current", "toRef", "isSystem", "important", "themeName", "bgLines", "fgLines", "Set", "values", "flatMap", "str", "i", "join", "themeClasses", "themeNames", "SUPPORTS_MATCH_MEDIA", "updateSystemName", "media", "matches", "window", "matchMedia", "addEventListener", "passive", "getCurrentScope", "onScopeDispose", "removeEventListener", "install", "app", "_context", "provides", "usehead", "getHead", "textContent", "nonce", "entry", "watch", "patch", "addHeadObjs", "watchEffect", "updateDOM", "updateStyles", "immediate", "change", "includes", "console<PERSON>arn", "cycle", "themeArray", "currentIndex", "indexOf", "nextIndex", "toggle", "globalName", "Proxy", "target", "prop", "Reflect", "deprecate", "global", "useTheme", "getCurrentInstance", "theme", "inject", "ThemeSymbol", "Error", "VuetifyLayoutKey", "Symbol", "for", "VuetifyLayoutItemKey", "makeLayoutProps", "propsFactory", "overlaps", "type", "Array", "default", "fullHeight", "Boolean", "makeLayoutItemProps", "name", "String", "order", "Number", "absolute", "useLayout", "layout", "inject", "VuetifyLayoutKey", "Error", "getLayoutItem", "mainRect", "mainStyles", "keyAliasMap", "control", "command", "option", "up", "down", "left", "right", "esc", "spacebar", "space", "return", "del", "minus", "hyphen", "normalizeKey", "key", "lowerKey", "toLowerCase", "splitKeyCombination", "combination", "isInternal", "arguments", "length", "undefined", "console<PERSON>arn", "startsWithPlusOrUnderscore", "startsWith", "hasInvalidLeadingSeparator", "hasInvalidStructure", "includes", "endsWith", "at", "keys", "buffer", "flushBuffer", "push", "normalizeKey", "i", "char", "nextChar", "hasInvalidMinus", "some", "key", "splitKeySequence", "str", "hasInvalidStart", "hasInvalidEnd", "result", "prevChar", "prevPrevChar", "precededBySinglePlusOrUnderscore", "collapsed", "minusCount", "part", "areAllValid", "every", "s", "useHotkey", "keys", "callback", "options", "arguments", "length", "undefined", "IN_BROWSER", "event", "inputs", "preventDefault", "sequenceTimeout", "isMac", "navigator", "userAgent", "includes", "timeout", "keyGroups", "isSequence", "groupIndex", "clearTimer", "clearTimeout", "isInputFocused", "toValue", "activeElement", "document", "tagName", "isContentEditable", "contentEditable", "resetSequence", "handler", "e", "group", "matchesKeyGroup", "window", "setTimeout", "cleanup", "removeEventListener", "watch", "unrefKeys", "groups", "splitKeySequence", "toLowerCase", "addEventListener", "immediate", "newEvent", "oldEvent", "getCurrentInstance", "onBeforeUnmount", "parseKeyGroup", "MODIFIERS", "parts", "splitKeyCombination", "modifiers", "Object", "fromEntries", "map", "m", "<PERSON><PERSON>ey", "part", "expectCtrl", "ctrl", "cmd", "meta", "expectMeta", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "shift", "altKey", "alt", "key", "createVuetify", "vuetify", "arguments", "length", "undefined", "blueprint", "rest", "options", "mergeDeep", "aliases", "components", "directives", "scope", "effectScope", "run", "defaults", "createDefaults", "display", "createDisplay", "ssr", "theme", "createTheme", "icons", "createIcons", "locale", "createLocale", "date", "createDate", "goTo", "createGoTo", "install", "app", "key", "directive", "component", "defineComponent", "name", "<PERSON><PERSON><PERSON>", "appScope", "onUnmount", "stop", "provide", "DefaultsSymbol", "DisplaySymbol", "ThemeSymbol", "IconSymbol", "LocaleSymbol", "DateOptionsSymbol", "DateAdapterSymbol", "instance", "GoToSymbol", "IN_BROWSER", "$nuxt", "hook", "update", "mount", "vm", "nextTick", "__VUE_OPTIONS_API__", "mixin", "computed", "$vuetify", "reactive", "inject", "call", "unmount", "version", "$", "provides", "parent", "vnode", "appContext"]}