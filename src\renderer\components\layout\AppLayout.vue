<template>
  <v-layout>
    <!-- 侧边导航栏 -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      permanent
      class="app-navigation"
      color="surface"
      elevation="1"
    >
      <!-- 应用Logo -->
      <div class="nav-header">
        <v-img
          v-if="!rail"
          src="/logo.png"
          alt="Print Terminal"
          height="40"
          width="40"
          class="nav-logo"
        />
        <div v-if="!rail" class="nav-title">
          Print Terminal
        </div>
      </div>

      <v-divider class="my-4" />

      <!-- 导航菜单 -->
      <v-list nav density="comfortable">
        <v-list-item
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.to"
          :prepend-icon="item.icon"
          :title="item.title"
          :value="item.name"
          color="primary"
          class="nav-item"
        />
      </v-list>

      <!-- 底部操作 -->
      <template #append>
        <div class="nav-footer">
          <v-btn
            :icon="rail ? 'mdi-chevron-right' : 'mdi-chevron-left'"
            variant="text"
            size="small"
            @click="rail = !rail"
          />
        </div>
      </template>
    </v-navigation-drawer>

    <!-- 主内容区域 -->
    <v-main class="app-main">
      <!-- 顶部应用栏 -->
      <v-app-bar
        color="surface"
        elevation="1"
        density="comfortable"
        class="app-bar"
      >
        <!-- 面包屑导航 -->
        <v-breadcrumbs
          :items="breadcrumbs"
          class="pa-0"
        >
          <template #divider>
            <v-icon icon="mdi-chevron-right" size="small" />
          </template>
        </v-breadcrumbs>

        <v-spacer />

        <!-- 同步状态 -->
        <v-chip
          :color="syncStatus.color"
          :prepend-icon="syncStatus.icon"
          size="small"
          variant="tonal"
          class="mr-4"
        >
          {{ syncStatus.text }}
        </v-chip>

        <!-- 网络状态 -->
        <v-icon
          :icon="isOnline ? 'mdi-wifi' : 'mdi-wifi-off'"
          :color="isOnline ? 'success' : 'error'"
          class="mr-4"
        />

        <!-- 用户菜单 -->
        <v-menu>
          <template #activator="{ props }">
            <v-btn
              icon="mdi-account-circle"
              variant="text"
              v-bind="props"
            />
          </template>
          <v-list>
            <v-list-item
              prepend-icon="mdi-cog"
              title="设置"
              @click="$router.push('/settings')"
            />
            <v-list-item
              prepend-icon="mdi-information"
              title="关于"
              @click="$router.push('/settings/about')"
            />
          </v-list>
        </v-menu>
      </v-app-bar>

      <!-- 页面内容 -->
      <div class="app-content">
        <router-view />
      </div>
    </v-main>
  </v-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';

// 路由
const route = useRoute();

// 响应式数据
const drawer = ref(true);
const rail = ref(false);

// 导航菜单项
const navigationItems = [
  {
    name: 'Dashboard',
    title: '仪表板',
    icon: 'mdi-view-dashboard',
    to: '/dashboard'
  },
  {
    name: 'Orders',
    title: '订单管理',
    icon: 'mdi-file-document-multiple',
    to: '/orders'
  },
  {
    name: 'PrintQueue',
    title: '打印队列',
    icon: 'mdi-printer-settings',
    to: '/print-queue'
  },
  {
    name: 'Printers',
    title: '打印机管理',
    icon: 'mdi-printer',
    to: '/printers'
  },
  {
    name: 'Logistics',
    title: '物流发货',
    icon: 'mdi-truck-delivery',
    to: '/logistics'
  },
  {
    name: 'Files',
    title: '文件管理',
    icon: 'mdi-folder-multiple',
    to: '/files'
  },
  {
    name: 'Sync',
    title: '同步状态',
    icon: 'mdi-sync',
    to: '/sync'
  },
  {
    name: 'Statistics',
    title: '统计报表',
    icon: 'mdi-chart-line',
    to: '/statistics'
  },
  {
    name: 'Settings',
    title: '系统设置',
    icon: 'mdi-cog',
    to: '/settings'
  }
];

// 计算属性
const breadcrumbs = computed(() => {
  return appStore.breadcrumbs || [
    {
      title: '首页',
      to: '/dashboard',
      disabled: false
    }
  ];
});

const isOnline = computed(() => appStore.isOnline);

const syncStatus = computed(() => {
  // TODO: 从同步状态store获取实际状态
  return {
    color: 'success',
    icon: 'mdi-check-circle',
    text: '已同步'
  };
});
</script>

<style lang="scss" scoped>
.app-navigation {
  border-right: 1px solid var(--border-light);
  
  .nav-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    gap: var(--spacing-3);
    
    .nav-logo {
      flex-shrink: 0;
    }
    
    .nav-title {
      font-size: var(--font-size-h6);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
    }
  }
  
  .nav-item {
    margin: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-md);
    
    &.v-list-item--active {
      background: rgba(var(--v-theme-primary), 0.1);
      color: var(--primary-500);
    }
  }
  
  .nav-footer {
    padding: var(--spacing-4);
    text-align: center;
  }
}

.app-bar {
  border-bottom: 1px solid var(--border-light);
}

.app-content {
  padding: var(--spacing-6);
  min-height: calc(100vh - 64px);
  background: var(--bg-page);
  
  @media (max-width: 600px) {
    padding: var(--spacing-4);
  }
}
</style>
