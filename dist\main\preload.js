"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
/**
 * Preload脚本 - 为渲染进程提供安全的API接口
 */
// 订单管理API
const orderAPI = {
    getList: (options) => electron_1.ipcRenderer.invoke('order:getList', options),
    getDetails: (orderId) => electron_1.ipcRenderer.invoke('order:getDetails', orderId),
    updateStatus: (orderId, status, notes) => electron_1.ipcRenderer.invoke('order:updateStatus', orderId, status, notes),
    batchUpdate: (orderIds, updates) => electron_1.ipcRenderer.invoke('order:batchUpdate', orderIds, updates),
    getStatistics: (dateRange) => electron_1.ipcRenderer.invoke('order:getStatistics', dateRange)
};
// 文件管理API
const fileAPI = {
    process: (fileId, options) => electron_1.ipcRenderer.invoke('file:process', fileId, options),
    generatePreview: (filePath, options) => electron_1.ipcRenderer.invoke('file:generatePreview', filePath, options),
    analyzeDocument: (filePath) => electron_1.ipcRenderer.invoke('file:analyzeDocument', filePath),
    convertFormat: (inputPath, outputFormat) => electron_1.ipcRenderer.invoke('file:convertFormat', inputPath, outputFormat),
    getFileList: (orderId) => electron_1.ipcRenderer.invoke('file:getFileList', orderId)
};
// 打印队列API
const printAPI = {
    addJob: (jobConfig) => electron_1.ipcRenderer.invoke('print:addJob', jobConfig),
    getQueueStatus: () => electron_1.ipcRenderer.invoke('print:getQueueStatus'),
    getAvailablePrinters: () => electron_1.ipcRenderer.invoke('print:getAvailablePrinters'),
    pauseQueue: () => electron_1.ipcRenderer.invoke('print:pauseQueue'),
    resumeQueue: () => electron_1.ipcRenderer.invoke('print:resumeQueue'),
    cancelJob: (jobId) => electron_1.ipcRenderer.invoke('print:cancelJob', jobId),
    retryJob: (jobId) => electron_1.ipcRenderer.invoke('print:retryJob', jobId),
    getJobHistory: (options) => electron_1.ipcRenderer.invoke('print:getJobHistory', options)
};
// 物流发货API
const logisticsAPI = {
    scanAndShip: (orderId, barcode, operatorName) => electron_1.ipcRenderer.invoke('logistics:scanAndShip', orderId, barcode, operatorName),
    getShippingStatistics: (dateRange) => electron_1.ipcRenderer.invoke('logistics:getShippingStatistics', dateRange),
    queryTracking: (trackingNumber, companyCode) => electron_1.ipcRenderer.invoke('logistics:queryTracking', trackingNumber, companyCode),
    updateShipmentStatus: (shipmentId, status) => electron_1.ipcRenderer.invoke('logistics:updateShipmentStatus', shipmentId, status),
    getLogisticsCompanies: () => electron_1.ipcRenderer.invoke('logistics:getLogisticsCompanies'),
    batchUpdateShippingStatus: () => electron_1.ipcRenderer.invoke('logistics:batchUpdateShippingStatus')
};
// 同步服务API
const syncAPI = {
    pullNewOrders: () => electron_1.ipcRenderer.invoke('sync:pullNewOrders'),
    pushStatusUpdates: () => electron_1.ipcRenderer.invoke('sync:pushStatusUpdates'),
    getStatus: () => electron_1.ipcRenderer.invoke('sync:getStatus'),
    startPeriodicSync: () => electron_1.ipcRenderer.invoke('sync:startPeriodicSync'),
    stopPeriodicSync: () => electron_1.ipcRenderer.invoke('sync:stopPeriodicSync'),
    forceSync: () => electron_1.ipcRenderer.invoke('sync:forceSync'),
    getSyncHistory: (limit) => electron_1.ipcRenderer.invoke('sync:getSyncHistory', limit)
};
// 系统配置API
const configAPI = {
    get: (key) => electron_1.ipcRenderer.invoke('config:get', key),
    set: (key, value) => electron_1.ipcRenderer.invoke('config:set', key, value),
    getAll: () => electron_1.ipcRenderer.invoke('config:getAll'),
    reset: (key) => electron_1.ipcRenderer.invoke('config:reset', key),
    export: () => electron_1.ipcRenderer.invoke('config:export'),
    import: (configData) => electron_1.ipcRenderer.invoke('config:import', configData)
};
// 应用控制API
const appAPI = {
    getVersion: () => electron_1.ipcRenderer.invoke('app:getVersion'),
    quit: () => electron_1.ipcRenderer.invoke('app:quit'),
    minimize: () => electron_1.ipcRenderer.invoke('app:minimize'),
    maximize: () => electron_1.ipcRenderer.invoke('app:maximize'),
    getSystemInfo: () => electron_1.ipcRenderer.invoke('app:getSystemInfo'),
    openExternal: (url) => electron_1.ipcRenderer.invoke('app:openExternal', url),
    showItemInFolder: (path) => electron_1.ipcRenderer.invoke('app:showItemInFolder', path)
};
// 事件监听API
const eventAPI = {
    // 订阅事件
    on: (channel, callback) => {
        electron_1.ipcRenderer.on(channel, (_, ...args) => callback(...args));
    },
    // 取消订阅
    off: (channel, callback) => {
        electron_1.ipcRenderer.removeListener(channel, callback);
    },
    // 订阅一次性事件
    once: (channel, callback) => {
        electron_1.ipcRenderer.once(channel, (_, ...args) => callback(...args));
    },
    // 移除所有监听器
    removeAllListeners: (channel) => {
        electron_1.ipcRenderer.removeAllListeners(channel);
    }
};
// 日志API
const logAPI = {
    info: (message, ...args) => electron_1.ipcRenderer.invoke('log:info', message, ...args),
    warn: (message, ...args) => electron_1.ipcRenderer.invoke('log:warn', message, ...args),
    error: (message, ...args) => electron_1.ipcRenderer.invoke('log:error', message, ...args),
    debug: (message, ...args) => electron_1.ipcRenderer.invoke('log:debug', message, ...args)
};
// 统计API
const statsAPI = {
    getDashboardStats: () => electron_1.ipcRenderer.invoke('stats:getDashboardStats'),
    getOrderStats: (dateRange) => electron_1.ipcRenderer.invoke('stats:getOrderStats', dateRange),
    getPrintStats: (dateRange) => electron_1.ipcRenderer.invoke('stats:getPrintStats', dateRange),
    getPerformanceStats: () => electron_1.ipcRenderer.invoke('stats:getPerformanceStats')
};
// 暴露API到渲染进程
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    order: orderAPI,
    file: fileAPI,
    print: printAPI,
    logistics: logisticsAPI,
    sync: syncAPI,
    config: configAPI,
    app: appAPI,
    event: eventAPI,
    log: logAPI,
    stats: statsAPI
});
// 暴露平台信息
electron_1.contextBridge.exposeInMainWorld('platform', {
    isWindows: process.platform === 'win32',
    isMac: process.platform === 'darwin',
    isLinux: process.platform === 'linux'
});
// 暴露版本信息
electron_1.contextBridge.exposeInMainWorld('versions', {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
});
//# sourceMappingURL=preload.js.map