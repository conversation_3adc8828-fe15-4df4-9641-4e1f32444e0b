import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表板',
      icon: 'mdi-view-dashboard',
      requiresAuth: false
    }
  },
  {
    path: '/orders',
    name: 'Orders',
    component: () => import('@/views/orders/OrderList.vue'),
    meta: {
      title: '订单管理',
      icon: 'mdi-file-document-multiple',
      requiresAuth: false
    }
  },
  {
    path: '/orders/:id',
    name: 'OrderDetail',
    component: () => import('@/views/orders/OrderDetail.vue'),
    meta: {
      title: '订单详情',
      parent: 'Orders',
      requiresAuth: false
    }
  },
  {
    path: '/print-queue',
    name: 'PrintQueue',
    component: () => import('@/views/print/PrintQueue.vue'),
    meta: {
      title: '打印队列',
      icon: 'mdi-printer-settings',
      requiresAuth: false
    }
  },
  {
    path: '/printers',
    name: 'Printers',
    component: () => import('@/views/print/PrinterManagement.vue'),
    meta: {
      title: '打印机管理',
      icon: 'mdi-printer',
      requiresAuth: false
    }
  },
  {
    path: '/logistics',
    name: 'Logistics',
    component: () => import('@/views/logistics/LogisticsList.vue'),
    meta: {
      title: '物流发货',
      icon: 'mdi-truck-delivery',
      requiresAuth: false
    }
  },
  {
    path: '/logistics/scan',
    name: 'LogisticsScan',
    component: () => import('@/views/logistics/LogisticsScan.vue'),
    meta: {
      title: '扫描发货',
      parent: 'Logistics',
      requiresAuth: false
    }
  },
  {
    path: '/files',
    name: 'Files',
    component: () => import('@/views/files/FileManagement.vue'),
    meta: {
      title: '文件管理',
      icon: 'mdi-folder-multiple',
      requiresAuth: false
    }
  },
  {
    path: '/sync',
    name: 'Sync',
    component: () => import('@/views/sync/SyncStatus.vue'),
    meta: {
      title: '同步状态',
      icon: 'mdi-sync',
      requiresAuth: false
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/settings/Settings.vue'),
    meta: {
      title: '系统设置',
      icon: 'mdi-cog',
      requiresAuth: false
    },
    children: [
      {
        path: '',
        redirect: '/settings/general'
      },
      {
        path: 'general',
        name: 'SettingsGeneral',
        component: () => import('@/views/settings/GeneralSettings.vue'),
        meta: {
          title: '常规设置',
          parent: 'Settings'
        }
      },
      {
        path: 'printers',
        name: 'SettingsPrinters',
        component: () => import('@/views/settings/PrinterSettings.vue'),
        meta: {
          title: '打印机设置',
          parent: 'Settings'
        }
      },
      {
        path: 'sync',
        name: 'SettingsSync',
        component: () => import('@/views/settings/SyncSettings.vue'),
        meta: {
          title: '同步设置',
          parent: 'Settings'
        }
      },
      {
        path: 'about',
        name: 'SettingsAbout',
        component: () => import('@/views/settings/AboutSettings.vue'),
        meta: {
          title: '关于',
          parent: 'Settings'
        }
      }
    ]
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/statistics/Statistics.vue'),
    meta: {
      title: '统计报表',
      icon: 'mdi-chart-line',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Print Terminal`;
  } else {
    document.title = 'Print Terminal - 云打印终端系统';
  }

  // 记录路由跳转日志
  if (window.electronAPI?.log) {
    window.electronAPI.log.info(`路由跳转: ${from.path} -> ${to.path}`);
  }

  next();
});

// 全局后置钩子
router.afterEach((to, from) => {
  // 记录页面访问
  if (window.electronAPI?.stats) {
    window.electronAPI.stats.recordPageView(to.path);
  }
});

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error);
  
  if (window.electronAPI?.log) {
    window.electronAPI.log.error('路由错误', error);
  }
  
  // 可以在这里添加错误上报逻辑
});

export default router;

// 导出路由配置供其他地方使用
export { routes };
