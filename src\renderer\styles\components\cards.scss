// 卡片组件样式

.v-card {
  border-radius: var(--border-radius-card) !important;
  border: 1px solid var(--border-light) !important;
  
  // 卡片标题
  .v-card-title {
    font-size: var(--font-size-h6) !important;
    font-weight: var(--font-weight-semibold) !important;
    color: var(--text-primary) !important;
    padding: var(--spacing-6) var(--spacing-6) var(--spacing-4) var(--spacing-6) !important;
  }
  
  // 卡片副标题
  .v-card-subtitle {
    font-size: var(--font-size-body-2) !important;
    color: var(--text-secondary) !important;
    padding: 0 var(--spacing-6) var(--spacing-4) var(--spacing-6) !important;
  }
  
  // 卡片内容
  .v-card-text {
    color: var(--text-primary) !important;
    padding: var(--spacing-6) !important;
    
    &:not(:first-child) {
      padding-top: 0 !important;
    }
  }
  
  // 卡片操作
  .v-card-actions {
    padding: var(--spacing-4) var(--spacing-6) var(--spacing-6) var(--spacing-6) !important;
    
    .v-btn {
      margin-right: var(--spacing-2) !important;
      
      &:last-child {
        margin-right: 0 !important;
      }
    }
  }
  
  // 卡片项目
  .v-card-item {
    padding: var(--spacing-6) !important;
    
    .v-card-item__content {
      .v-card-title {
        padding: 0 !important;
      }
      
      .v-card-subtitle {
        padding: 0 !important;
      }
    }
  }
  
  // 悬停效果
  &.card-hover {
    transition: all var(--duration-normal) var(--ease-out) !important;
    cursor: pointer;
    
    &:hover {
      box-shadow: var(--shadow-card-hover) !important;
      transform: translateY(-2px);
    }
  }
  
  // 可点击卡片
  &.card-clickable {
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out) !important;
    
    &:hover {
      box-shadow: var(--shadow-card-hover) !important;
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
}

// 内容卡片
.content-card {
  background: var(--bg-card) !important;
  
  .card-title {
    display: flex;
    align-items: center;
    font-size: var(--font-size-h6) !important;
    font-weight: var(--font-weight-semibold) !important;
    color: var(--text-primary) !important;
  }
}

// 统计卡片
.stats-card {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-light) !important;
  transition: all var(--duration-normal) var(--ease-out) !important;
  
  &:hover {
    box-shadow: var(--shadow-card-hover) !important;
    transform: translateY(-2px);
  }
  
  .stats-content {
    padding: var(--spacing-6) !important;
  }
  
  .stats-value {
    font-size: var(--font-size-h4) !important;
    font-weight: var(--font-weight-bold) !important;
    color: var(--text-primary) !important;
    line-height: 1.2;
  }
  
  .stats-label {
    font-size: var(--font-size-body-2) !important;
    color: var(--text-secondary) !important;
    margin-top: var(--spacing-1) !important;
  }
  
  .stats-trend {
    font-size: var(--font-size-caption) !important;
    color: var(--text-tertiary) !important;
    margin-top: var(--spacing-2) !important;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    
    .trend-icon {
      font-size: 14px !important;
    }
  }
  
  .stats-icon {
    opacity: 0.6;
    color: var(--text-tertiary) !important;
  }
  
  // 统计卡片变体
  &.stats-card-primary {
    border-left: 4px solid var(--primary-500) !important;
    
    .stats-value {
      color: var(--primary-500) !important;
    }
  }
  
  &.stats-card-success {
    border-left: 4px solid var(--success-500) !important;
    
    .stats-value {
      color: var(--success-500) !important;
    }
  }
  
  &.stats-card-warning {
    border-left: 4px solid var(--warning-500) !important;
    
    .stats-value {
      color: var(--warning-500) !important;
    }
  }
  
  &.stats-card-error {
    border-left: 4px solid var(--error-500) !important;
    
    .stats-value {
      color: var(--error-500) !important;
    }
  }
  
  &.stats-card-info {
    border-left: 4px solid var(--info-500) !important;
    
    .stats-value {
      color: var(--info-500) !important;
    }
  }
}

// 统计网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
}

// 卡片网格
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-6);
  
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
}
