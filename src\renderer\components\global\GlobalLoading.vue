<template>
  <v-overlay
    v-model="isLoading"
    class="global-loading"
    persistent
    contained
  >
    <div class="loading-content">
      <v-progress-circular
        indeterminate
        size="64"
        width="4"
        color="primary"
      />
      <div class="loading-text mt-4">
        加载中...
      </div>
    </div>
  </v-overlay>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/stores/app';

const appStore = useAppStore();

const isLoading = computed(() => appStore.isLoading);
</script>

<style lang="scss" scoped>
.global-loading {
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .loading-text {
      color: var(--text-primary);
      font-size: var(--font-size-body-1);
      font-weight: var(--font-weight-medium);
    }
  }
}
</style>
